import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || "dhwanini-sing-along";

const storeFileInSupabase = async (file: File, fileName: string, mimeType: string): Promise<{success: boolean, url?: string, error: string | null}> => {
    try {
        const supabase = await createClient();
        if (!supabase) {
            throw new Error('Failed to create supabase client');
        }

        const { data: uploadData, error: uploadError } = await supabase.storage
            .from(BUCKET_NAME)
            .upload(fileName, file, {
                contentType: mimeType,
                upsert: true
            });
        
        if (uploadError) {
            console.error('Upload error:', uploadError);
            return { success: false, error: `Upload failed: ${uploadError.message}` };
        }

        return { success: true, error: null };
    }
    catch (error) {
        console.error('Unexpected error in storeFileInSupabase:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}

export async function POST(request: Request) {
    try {
        const formData = await request.formData();

        const file = formData.get('file') as File;
        const fileName = formData.get('fileName') as string;
        const mimeType = formData.get('mimeType') as string;

        console.log('Received upload request:', { fileName, mimeType, fileSize: file?.size });

        if (!file || !fileName || !mimeType) {
            return NextResponse.json({ success: false, error: 'Missing required fields' }, { status: 400 });
        }
        
        const result = await storeFileInSupabase(file, fileName, mimeType);

        console.log('Upload result:', result);

        return NextResponse.json({ 
            success: result.success, 
            url: result.url,
            error: result.error 
        });
    }
    catch (error) {
        console.error('Error in POST handler:', error);
        return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
    }
}