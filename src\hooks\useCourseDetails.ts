'use client'

import { useEffect, useState, useTransition, useCallback } from "react"
import { getAccessableDetails } from "@/data-access/course/course"
import { AccessableCourseDetails } from "@/types/course"
import { createClient } from "@/utils/supabase/client"

export const useCourseDetails = (courseId: string): {
  course: AccessableCourseDetails | null, 
  isPending: boolean, 
  error: string | null,
  refreshCourse: () => void
} => {
  const [course, setCourse] = useState<AccessableCourseDetails | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPending, startTransition] = useTransition()

  const fetchCourseDetails = useCallback(async () => {
    if (!courseId) return
    
    const supabase = createClient();
    const {data: {user}, error} = await supabase.auth.getUser();
    const email = user?.email;
    if (!email) {
      setError(error?.message || 'User not found')
      return
    }
    try {
      const result = await getAccessableDetails(courseId, email)
      if (result.success && result.data) {
        setCourse(result.data)
        console.log("result.data", result.data)
        setError(null)
      } else {
        setError(result.error || 'Failed to load course details')
        setCourse(null)
      }
    } catch (err) {
      setError('An unexpected error occurred')
      setCourse(null)
      console.error('Error fetching course details:', err)
    }
  }, [courseId])

  useEffect(() => {
    startTransition(() => {
      fetchCourseDetails()
    })
  }, [fetchCourseDetails])

  const refreshCourse = useCallback(() => {
    startTransition(() => {
      fetchCourseDetails()
    })
  }, [fetchCourseDetails])

  return {
    course,
    isPending,
    error,
    refreshCourse
  }
}

