import { create } from "zustand";
import { createClient } from "@/utils/supabase/client";
import { prisma } from "@/utils/prisma/prisma";

// User interface with proper typing
export interface User {
  id: string;
  email: string;
  name: string;
  role: "SUPER_ADMIN" | "ADMISSION_SUPPORT" | "COURSE_MANAGER" | "STUDENT";
  phone?: string;
  country?: string;
  createdAt?: Date;
}

export async function getUserDetailsByEmail(email: string): Promise<{
  user: User | null,
  error: string | null
}> {
  try {
    const User = await prisma.user.findUnique({
      where: {
        email: email
      }
    })
    return { user: User, error: null };
  }
  catch (error) { 
    return { user: null, error: 'Error fetching user details' };
  }
}

interface AuthStore {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string; redirectPath?: string }>;
  logout: () => Promise<void>;
  init: () => Promise<void>;
  clearError: () => void;
  fetchUserDetails: (email: string) => Promise<User | null>;

  // Role-based helpers
  hasRole: (roles: string[]) => boolean;
  isAdmin: () => boolean;
  isStudent: () => boolean;
  getRedirectPath: () => string;
}

export const useAuthStore = create<AuthStore>((set, get) => {
  const supabase = createClient();

  // Auth change subscription
  supabase.auth.onAuthStateChange(async (event, session) => {
    if (event === "SIGNED_OUT" || !session?.user) {
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    } else if (event === "SIGNED_IN" && session?.user) {
      // Fetch user details from database when signed in
      await get().fetchUserDetails(session.user.email!);
    }
  });

  const store = {
    // Initial state
    user: null,
    isAuthenticated: false,
    isLoading: true, // Start with loading true
    error: null,

    // Actions
    setUser: (user) =>
      set({
        user,
        isAuthenticated: !!user,
        error: null,
      }),

    setLoading: (isLoading) => set({ isLoading }),

    setError: (error) => set({ error }),

    clearError: () => set({ error: null }),

    // Login function
    login: async (email: string, password: string) => {
      set({ isLoading: true, error: null });

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          set({ isLoading: false, error: error.message });
          return { success: false, error: error.message };
        }

        if (data.user) {
          // Fetch user details from database
          const userDetails = await get().fetchUserDetails(email);
          if (userDetails) {
            // User state is already set in fetchUserDetails
            const redirectPath = get().getRedirectPath();
            set({ isLoading: false });
            return { success: true, redirectPath };
          } else {
            set({ isLoading: false, error: "User not found in system" });
            await supabase.auth.signOut();
            return { success: false, error: "User not found in system" };
          }
        }

        set({ isLoading: false, error: "Login failed" });
        return { success: false, error: "Login failed" };
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Login failed";
        set({ isLoading: false, error: errorMessage });
        return { success: false, error: errorMessage };
      }
    },

    // Logout function
    logout: async () => {
      set({ isLoading: true });
      try {
        await supabase.auth.signOut();
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
        // Force navigation to login page
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Logout error:", error);
        set({ isLoading: false });
      }
    },

    // Initialize auth state
    init: async () => {
      set({ isLoading: true });
      try {
        const { data } = await supabase.auth.getUser();
        if (data.user?.email) {
          await get().fetchUserDetails(data.user.email);
        } else {
          set({ user: null, isAuthenticated: false });
        }
      } catch (error) {
        console.error("Auth init error:", error);
        set({
          user: null,
          isAuthenticated: false,
          error: "Failed to initialize auth",
        });
      } finally {
        set({ isLoading: false });
      }
    },

    // Fetch user details from database
    fetchUserDetails: async (email: string) => {
      try {
        const { user, error } = await getUserDetailsByEmail(email);

        if (user) {
          set({
            user: user,
            isAuthenticated: true,
            error: null,
          });
          return user;
        } else {
          set({ user: null, isAuthenticated: false });
          return null;
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
        set({
          user: null,
          isAuthenticated: false,
          error: "Failed to fetch user details",
        });
        return null;
      }
    },

    // Role-based helper functions
    hasRole: (roles: string[]) => {
      const { user } = get();
      return user ? roles.includes(user.role) : false;
    },

    isAdmin: () => {
      const { user } = get();
      return user
        ? ["SUPER_ADMIN", "ADMISSION_SUPPORT", "COURSE_MANAGER"].includes(
            user.role
          )
        : false;
    },

    isStudent: () => {
      const { user } = get();
      return user?.role === "STUDENT";
    },

    getRedirectPath: () => {
      const { user } = get();
      if (!user) return "/login";

      switch (user.role) {
        case "SUPER_ADMIN":
          return "/admin";
        case "ADMISSION_SUPPORT":
          return "/admissions";
        case "COURSE_MANAGER":
          return "/admin/courses";
        case "STUDENT":
        default:
          return "/dashboard";
      }
    },
  };

  // Initialize auth state on store creation
  store.init();

  return store;
});