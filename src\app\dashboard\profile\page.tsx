import { Suspense } from 'react'
import { getCompleteProfileData } from '@/data-access/profile/profile'
import { ProfileDashboard } from '@/components/Dashboard/Profile/ProfileDashboard'
import { ProfileLoadingSkeleton } from '@/components/Dashboard/Profile/ProfileLoadingSkeleton'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export default async function ProfilePage() {
  const supabase = await createClient();
  const {data: {user}, error} = await supabase.auth.getUser();
  if (error || !user) {
    return <div>Error: {error?.message || 'User not found'}</div>;
  }
  const result = await getCompleteProfileData(user.email)

  if (!result.success) {
    if (result.error === 'User not authenticated') {
      redirect('/login')
    }
    throw new Error(result.error || 'Failed to load profile data')
  }

  const profileData = result.data!

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8">
        <div className="mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">
            Manage your account, track your progress, and view your achievements
          </p>
        </div>

        <Suspense fallback={<ProfileLoadingSkeleton />}>
          <ProfileDashboard profileData={profileData} />
        </Suspense>
      </div>
    </div>
  )
}
