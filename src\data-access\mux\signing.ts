"use server"
import Mux from "@mux/mux-node";
import { createClient } from "@/utils/supabase/server";


const mux = new Mux({
    tokenId: process.env.MUX_TOKEN_ID!,
    tokenSecret: process.env.MUX_TOKEN_SECRET!
})

interface VideoUrlResponse {
    token: string | null
    error?: string
}

const getVideoUrl = async (playbackId: string): Promise<VideoUrlResponse> => {

    const supabase = await createClient();
    const {error} = await supabase.auth.getUser()
    if(error){
        return {
            token: null,
            error:error.message
        }
    }
    
    try {
        const PRIVATE_KEY = process.env.MUX_PRIVATE_KEY!.replace(/\\n/g, '\n')

        let baseOptions = {
            keyId: process.env.MUX_KEY_ID,
            keySecret: PRIVATE_KEY,
            expiration: '2h'
        }
    
        const token = await mux.jwt.signPlaybackId(playbackId, {...baseOptions, type: 'video'})
    
        return {
            token: token,
            error: null
        }
    } 
    catch (error) {
        console.error(error)
        return {
            token: null,
            error: error.message
        }
    }
}

export default getVideoUrl