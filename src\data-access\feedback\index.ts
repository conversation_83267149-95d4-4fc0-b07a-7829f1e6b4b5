'use server'

import { prisma } from '@/utils/prisma/prisma'
import { ActionResult, handleError } from '../helpers'
import { SessionStatus } from '@prisma/client'
import { FeedbackSessionWithDetails as FeedbackSessionType } from '@/types/feedback'

export type FeedbackSessionWithDetails = {
  id: string
  assignmentId?: string | null
  assignment?: {
    id: string
    title: string
    moduleId: string
  } | null
  course: {
    id: string
    name: string
  }
  scheduledAt: Date | null
  status: SessionStatus
  calEventId?: string | null
  notes?: string | null
}

export async function getUserFeedbackSessions(email: string): Promise<ActionResult<FeedbackSessionWithDetails[]>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const sessions = await prisma.feedbackSession.findMany({
      where: { userId: user.id },
      include: {
        course: true,
        assignment: true
      },
      orderBy: { scheduledAt: 'asc' }
    })

    const formattedSessions = sessions.map(session => ({
      id: session.id,
      assignmentId: session.assignmentId,
      assignment: session.assignment ? {
        id: session.assignment.id,
        title: session.assignment.title,
        moduleId: session.assignment.moduleId
      } : null,
      course: {
        id: session.course.id,
        name: session.course.name
      },
      scheduledAt: session.scheduledAt,
      status: session.status,
      calEventId: session.calEventId,
      notes: session.notes
    }))

    return { success: true, data: formattedSessions }
  } catch (error) {
    return handleError(error, 'fetch user feedback sessions')
  }
}

export async function getCalLink(): Promise<ActionResult<string>> {
  try {
    // Get settings from the database or use default
    const settings = await prisma.feedbackSettings.findUnique({
      where: { id: 'feedback_settings' }
    })
    
    return { 
      success: true, 
      data: settings?.calLink || 'dhwani/feedback-session' 
    }
  } catch (error) {
    return handleError(error, 'fetch cal link')
  }
}

export async function scheduleFeedbackSession(
  email: string, 
  assignmentId: string | null, 
  calEventId: string, 
  scheduledAt: Date | null
): Promise<ActionResult<{ sessionId: string }>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    if (!scheduledAt) {
      return { success: false, error: 'Scheduled date is required' }
    }

    let courseId: string | undefined = undefined
    let finalAssignmentId: string | null = null

    if (assignmentId && assignmentId.trim() !== '') {
      // Assignment-specific feedback
      const assignment = await prisma.assignment.findUnique({
        where: { id: assignmentId },
        include: {
          module: {
            include: {
              course: true
            }
          }
        }
      })

      if (!assignment) {
        return { success: false, error: 'Assignment not found' }
      }


      courseId = assignment.module.course.id
      finalAssignmentId = assignmentId
    }

    // Create feedback session
    const session = await prisma.feedbackSession.create({
      data: {
        userId: user.id,
        courseId,
        assignmentId: finalAssignmentId,
        calEventId,
        scheduledAt
      }
    })

    return { success: true, data: { sessionId: session.id } }
  } catch (error) {
    return handleError(error, 'schedule feedback session')
  }
}

export async function cancelFeedbackSession(email: string, sessionId: string): Promise<ActionResult<void>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Verify session belongs to user
    const session = await prisma.feedbackSession.findUnique({
      where: { id: sessionId }
    })

    if (!session) {
      return { success: false, error: 'Feedback session not found' }
    }

    if (session.userId !== user.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Update session status
    await prisma.feedbackSession.update({
      where: { id: sessionId },
      data: { status: SessionStatus.CANCELLED }
    })

    return { success: true }
  } catch (error) {
    return handleError(error, 'cancel feedback session')
  }
} 