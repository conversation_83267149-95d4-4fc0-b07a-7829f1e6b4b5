import { createClient } from "@/utils/supabase/client";
import { storeUserRecording } from "./songs";

export const storeRecording = async (audio: Blob, mimeType: string, songName: string, duration: number) => {
    try {
        const supabase = createClient();
        const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-private';
        const { data, error } = await supabase.storage.from(BUCKET_NAME).upload(`user-recordings/${songName}-${Date.now()}.${mimeType.split('/')[1]}`, audio, {
            contentType: mimeType
        });
        if(error) {
            return { success: false, error: error.message };
        }
        const { success, recording, error: recordingError } = await storeUserRecording(data.path, mimeType, duration, songName);
        if(!success) {
            return { success: false, error: recordingError };
        }
        return { success: true, recording: recording, error: null };
    }
    catch (error) {
        console.error('Error storing recording:', error);
        return { success: false, error: 'Error storing recording' };
    }
}   