'use client'

import { useState } from 'react'
import { SubmissionStatus } from '@prisma/client'
import { UserAssignmentAnalytics } from '@/types/analytics'
import { 
  User, 
  BookOpen, 
  FileText, 
  CheckCircle2, 
  AlertCircle, 
  Clock,
  XCircle,
  TrendingUp,
  Calendar
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import AssignmentSubmissionManager from '../Course/Assignment/AssignmentSubmissionManager'

interface UserAssignmentAnalyticsDashboardProps {
  data: UserAssignmentAnalytics
  onStatusUpdate: (submissionId: string, status: SubmissionStatus, feedback?: string) => Promise<void>
  isUpdating: boolean
}

export default function UserAssignmentAnalyticsDashboard({
  data,
  onStatusUpdate,
  isUpdating
}: UserAssignmentAnalyticsDashboardProps) {
  const [selectedSubmission, setSelectedSubmission] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'submitted' | 'pending' | 'overdue' | 'graded' | 'needs_revision'>('all')

  const filteredSubmissions = data.submissions.filter(submission => {
    switch (filter) {
      case 'submitted':
        return submission.hasSubmission && submission.status === SubmissionStatus.SUBMITTED
      case 'pending':
        return !submission.hasSubmission && !submission.isOverdue
      case 'overdue':
        return submission.isOverdue
      case 'graded':
        return submission.status === SubmissionStatus.GRADED
      case 'needs_revision':
        return submission.status === SubmissionStatus.NEEDS_REVISION
      default:
        return true
    }
  })

  const getStatusIcon = (status: SubmissionStatus | null, hasSubmission: boolean, isOverdue: boolean) => {
    if (isOverdue) return <XCircle className="w-4 h-4 text-red-600" />
    if (!hasSubmission) return <Clock className="w-4 h-4 text-gray-400" />
    
    switch (status) {
      case SubmissionStatus.GRADED:
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case SubmissionStatus.NEEDS_REVISION:
        return <AlertCircle className="w-4 h-4 text-amber-600" />
      case SubmissionStatus.SUBMITTED:
        return <Clock className="w-4 h-4 text-blue-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: SubmissionStatus | null, hasSubmission: boolean, isOverdue: boolean) => {
    if (isOverdue) return 'bg-red-100 text-red-700 border-red-200'
    if (!hasSubmission) return 'bg-gray-100 text-gray-700 border-gray-200'
    
    switch (status) {
      case SubmissionStatus.GRADED:
        return 'bg-green-100 text-green-700 border-green-200'
      case SubmissionStatus.NEEDS_REVISION:
        return 'bg-amber-100 text-amber-700 border-amber-200'
      case SubmissionStatus.SUBMITTED:
        return 'bg-blue-100 text-blue-700 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getStatusText = (status: SubmissionStatus | null, hasSubmission: boolean, isOverdue: boolean) => {
    if (isOverdue) return 'Overdue'
    if (!hasSubmission) return 'Not Submitted'
    
    switch (status) {
      case SubmissionStatus.GRADED:
        return 'Graded'
      case SubmissionStatus.NEEDS_REVISION:
        return 'Needs Revision'
      case SubmissionStatus.SUBMITTED:
        return 'Under Review'
      default:
        return 'Unknown'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <User className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{data.user.name}</h1>
              <p className="text-gray-600">{data.user.email}</p>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <BookOpen className="w-4 h-4" />
                  <span>{data.course.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Enrolled: {formatDate(data.enrolledAt)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Total</p>
              <p className="text-2xl font-bold text-gray-900">{data.stats.totalAssignments}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <FileText className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Submitted</p>
              <p className="text-2xl font-bold text-blue-900">{data.stats.submitted}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Graded</p>
              <p className="text-2xl font-bold text-green-900">{data.stats.graded}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <CheckCircle2 className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Needs Revision</p>
              <p className="text-2xl font-bold text-amber-900">{data.stats.needsRevision}</p>
            </div>
            <div className="p-3 bg-amber-50 rounded-lg">
              <AlertCircle className="w-6 h-6 text-amber-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Overdue</p>
              <p className="text-2xl font-bold text-red-900">{data.stats.overdue}</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Completion Rate</p>
              <p className="text-2xl font-bold text-indigo-900">
                {data.stats.totalAssignments > 0 
                  ? Math.round((data.stats.graded / data.stats.totalAssignments) * 100)
                  : 0}%
              </p>
            </div>
            <div className="p-3 bg-indigo-50 rounded-lg">
              <TrendingUp className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Assignments</h3>
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'all', label: 'All', count: data.submissions.length },
            { key: 'submitted', label: 'Under Review', count: data.stats.submitted - data.stats.graded - data.stats.needsRevision },
            { key: 'graded', label: 'Graded', count: data.stats.graded },
            { key: 'needs_revision', label: 'Needs Revision', count: data.stats.needsRevision },
            { key: 'overdue', label: 'Overdue', count: data.stats.overdue },
            { key: 'pending', label: 'Pending', count: data.stats.pending }
          ].map(({ key, label, count }) => (
            <button
              key={key}
              onClick={() => setFilter(key as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {label} ({count})
            </button>
          ))}
        </div>
      </div>

      {/* Assignment Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assignment List */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Assignments ({filteredSubmissions.length})
            </h3>
          </div>
          <div className="p-6">
            {filteredSubmissions.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">No assignments found</h4>
                <p className="text-gray-600">No assignments match the current filter</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredSubmissions.map((submission) => (
                  <button
                    key={submission.assignmentId}
                    onClick={() => setSelectedSubmission(
                      selectedSubmission === submission.assignmentId ? null : submission.assignmentId
                    )}
                    className={`w-full text-left p-4 rounded-lg border transition-colors ${
                      selectedSubmission === submission.assignmentId
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {submission.assignmentTitle}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          Module: {submission.moduleTitle}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Due: {formatDate(submission.assignmentDeadline)}</span>
                          {submission.submittedAt && (
                            <span>Submitted: {formatDate(submission.submittedAt)}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        {getStatusIcon(submission.status, submission.hasSubmission, submission.isOverdue)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                          getStatusColor(submission.status, submission.hasSubmission, submission.isOverdue)
                        }`}>
                          {getStatusText(submission.status, submission.hasSubmission, submission.isOverdue)}
                        </span>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Assignment Detail */}
        <div>
          {selectedSubmission ? (
            <AssignmentSubmissionManager
              submission={filteredSubmissions.find(s => s.assignmentId === selectedSubmission)!}
              onStatusUpdate={onStatusUpdate}
              isUpdating={isUpdating}
            />
          ) : (
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Select an Assignment</h4>
                <p className="text-gray-600">Choose an assignment from the list to view details and manage feedback</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 