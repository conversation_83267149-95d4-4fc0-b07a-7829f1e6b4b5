'use client';

import { useEffect, useState, useRef } from 'react';
import { deleteRecording, getRecordings, toggleFavourite } from '@/data-access/sing-along/recordings';
import { getSignedUrl } from '@/data-access/sing-along/storage';
import { CustomAudioPlayer } from '@/components/SingAlong/SongPlayer/CustomAudioPlayer';
import { Music, Calendar, Clock, Trash2, Download, Search, Heart, Filter, X, AlertTriangle } from 'lucide-react';
import Link from 'next/link';

// Custom Confirmation Modal Component
function DeleteConfirmationModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  musicTitle 
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  musicTitle: string;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white/60 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-center mb-4">
            <div className="bg-red-100 rounded-full p-3">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </div>
          
          {/* Title */}
          <h3 className="text-xl font-bold text-slate-800 text-center mb-2">
            Delete Recording
          </h3>
          
          {/* Message */}
          <div className="text-center mb-6">
            <p className="text-slate-600 mb-3">
              Are you sure you want to delete this recording?
            </p>
            <div className="bg-slate-50 rounded-lg p-3 border">
              <p className="font-semibold text-slate-800 truncate">
                "{musicTitle}"
              </p>
            </div>
            <p className="text-sm text-red-600 mt-3 font-medium">
              This action cannot be undone.
            </p>
          </div>
          
          {/* Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2.5 bg-slate-100 text-slate-700 rounded-lg font-medium hover:bg-slate-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              className="flex-1 px-4 py-2.5 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Audio Player Component for individual recordings
function RecordingAudioPlayer({ recording }: { recording: any }) {
  const audioRef = useRef<HTMLAudioElement>(null);
  //TODO: OPTION TO DELETE RECORDING
  return (
    <CustomAudioPlayer 
      src={recording.audioUrl}
      duration={recording.duration}
      audioRef={audioRef}
      compact={true}
      className="w-full"
    />
  );
}

// Loading component for the recordings grid
function RecordingsGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="bg-white rounded-xl border border-slate-200 p-6 animate-pulse">
          <div className="h-4 bg-slate-200 rounded w-3/4 mb-3"></div>
          <div className="h-3 bg-slate-200 rounded w-1/2 mb-4"></div>
          <div className="h-8 bg-slate-200 rounded mb-4"></div>
          <div className="flex justify-between">
            <div className="h-3 bg-slate-200 rounded w-1/4"></div>
            <div className="h-3 bg-slate-200 rounded w-1/4"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Filter component
function FilterBar({ 
  searchTerm, 
  setSearchTerm, 
  selectedMusic, 
  setSelectedMusic, 
  showFavoritesOnly, 
  setShowFavoritesOnly, 
  musicTitles,
  recordingsCount,
  favoritesCount 
}: {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedMusic: string;
  setSelectedMusic: (music: string) => void;
  showFavoritesOnly: boolean;
  setShowFavoritesOnly: (show: boolean) => void;
  musicTitles: string[];
  recordingsCount: number;
  favoritesCount: number;
}) {
  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Bar */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <input
            type="text"
            placeholder="Search recordings..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 text-sm border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          />
        </div>

        {/* Music Filter */}
        <div className="relative">
          <select
            value={selectedMusic}
            onChange={(e) => setSelectedMusic(e.target.value)}
            className="appearance-none bg-white border border-slate-200 rounded-lg px-4 py-2.5 pr-10 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer"
          >
            <option value="">All Music</option>
            {musicTitles.map((title) => (
              <option key={title} value={title}>{title}</option>
            ))}
          </select>
          <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none" />
        </div>

        {/* Favorites Toggle */}
        <button
          onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
          className={`inline-flex items-center px-4 py-2.5 rounded-lg text-sm font-medium transition-all ${
            showFavoritesOnly
              ? 'bg-red-50 text-red-700 border-2 border-red-200 hover:bg-red-100'
              : 'bg-slate-50 text-slate-600 border-2 border-slate-200 hover:bg-slate-100'
          }`}
        >
          <Heart className={`h-4 w-4 mr-2 ${showFavoritesOnly ? 'fill-current' : ''}`} />
          {showFavoritesOnly ? 'Show All' : 'Favorites Only'}
        </button>
      </div>

      {/* Filter Summary */}
      <div className="flex items-center justify-between text-sm text-slate-600 bg-slate-50 rounded-lg p-3">
        <div className="flex items-center space-x-4">
          <span>
            Showing <span className="font-medium text-slate-800">{recordingsCount}</span> recordings
          </span>
          {favoritesCount > 0 && (
            <span className="flex items-center">
              <Heart className="h-3 w-3 mr-1 fill-current text-red-500" />
              <span className="font-medium text-red-600">{favoritesCount}<span className="text-xs"> favorites</span></span>
            </span>
          )}
        </div>
        
        {/* Active Filters */}
        <div className="flex items-center space-x-2">
          {searchTerm && (
            <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs">
              Search: "{searchTerm}"
              <button
                onClick={() => setSearchTerm('')}
                className="ml-1 hover:bg-blue-200 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {selectedMusic && (
            <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded-md text-xs">
              Music: {selectedMusic}
              <button
                onClick={() => setSelectedMusic('')}
                className="ml-1 hover:bg-green-200 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          )}
          {showFavoritesOnly && (
            <span className="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 rounded-md text-xs">
              <Heart className="h-3 w-3 mr-1 fill-current" />
              Favorites Only
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

// Main recordings grid component
function RecordingsGrid({ 
  recordings, 
  onToggleFavorite,
  onDeleteRecording,
  onDownload
}: { 
  recordings: any[]; 
  onToggleFavorite: (recordingId: string, currentFavorite: boolean) => void;
  onDeleteRecording: (recordingId: string, musicTitle: string) => void;
  onDownload: (audioUrl: string, musicTitle: string, fileName: string) => void;
}) {
  if (recordings.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
          <Heart className="h-16 w-16 text-slate-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-slate-700 mb-2">No recordings found</h3>
          <p className="text-slate-500 mb-6">Try adjusting your filters or search terms.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {recordings.map((recording) => (
        <div key={recording.id} className="bg-white rounded-xl border border-slate-200 hover:border-blue-200 hover:shadow-lg transition-all duration-200 p-5 group relative overflow-hidden">
          {/* Favorite Badge */}
          {recording.favourite && (
            <div className="absolute top-3 right-3 z-10">
              <div className="bg-red-500 text-white p-1.5 rounded-full shadow-lg">
                <Heart className="h-3 w-3 fill-current" />
              </div>
            </div>
          )}

          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 min-w-0 pr-4">
              <h3 className="text-lg font-semibold text-slate-800 truncate group-hover:text-blue-600 transition-colors">
                {recording.musicTitle}
              </h3>
              <p className="text-sm text-slate-500 mt-1">
                {recording.createdAt.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button 
                onClick={() => onToggleFavorite(recording.id, recording.favourite)}
                className={`p-2 rounded-lg transition-colors ${
                  recording.favourite 
                    ? 'text-red-500 hover:bg-red-50' 
                    : 'text-slate-400 hover:text-red-500 hover:bg-red-50'
                }`}
                title={recording.favourite ? 'Remove from favorites' : 'Add to favorites'}
              >
                <Heart className={`h-4 w-4 ${recording.favourite ? 'fill-current' : ''}`} />
              </button>
              <button 
                onClick={() => onDownload(recording.audioUrl, recording.musicTitle, recording.fileName)}
                className="p-2 text-slate-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Download recording"
                disabled={!recording.audioUrl}
              >
                <Download className="h-4 w-4" />
              </button>
              <button 
                onClick={() => onDeleteRecording(recording.id, recording.musicTitle)}
                className="p-2 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete recording"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="mb-4">
            {recording.audioUrl ? (
              <RecordingAudioPlayer recording={recording} />
            ) : (
              <div className="bg-slate-100 rounded-lg p-3 text-center">
                <p className="text-sm text-slate-500">Audio unavailable</p>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between text-sm text-slate-500">
            <div className="flex items-center space-x-1">
              <Clock className="h-3.5 w-3.5" />
              <span>{recording.duration}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Music className="h-3.5 w-3.5" />
              <span className="text-xs">Recording</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default function RecordingsPage() {
  const [recordings, setRecordings] = useState<any[]>([]);
  const [recordingsWithUrls, setRecordingsWithUrls] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [urlsLoading, setUrlsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMusic, setSelectedMusic] = useState('');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Delete confirmation modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [recordingToDelete, setRecordingToDelete] = useState<{id: string, title: string} | null>(null);

  // Load recordings on mount
  useEffect(() => {
    const loadRecordings = async () => {
      const { recordings: data, error: err } = await getRecordings();
      if (err) {
        setError(err);
      } else {
        setRecordings(data || []);
      }
      setLoading(false);
    };

    loadRecordings();
  }, []);

  // Load URLs when recordings change
  useEffect(() => {
    if (recordings.length === 0) return;

    const loadAudioUrls = async () => {
      setUrlsLoading(true);
      const recordingsWithUrlsPromise = await Promise.all(
        recordings.map(async (recording) => {
          const { signedUrl, error } = await getSignedUrl(recording.fileName);
          return {
            ...recording,
            audioUrl: error ? null : signedUrl,
            createdAt: new Date(recording.createdAt)
          };
        })
      );
      setRecordingsWithUrls(recordingsWithUrlsPromise);
      setUrlsLoading(false);
    };

    loadAudioUrls();
  }, [recordings]);

  // Handle favorite toggle
  const handleToggleFavorite = async (recordingId: string, currentFavorite: boolean) => {
    // Optimistically update both states
    setRecordings(prev => 
      prev.map(recording => 
        recording.id === recordingId 
          ? { ...recording, favourite: !currentFavorite }
          : recording
      )
    );
    
    setRecordingsWithUrls(prev => 
      prev.map(recording => 
        recording.id === recordingId 
          ? { ...recording, favourite: !currentFavorite }
          : recording
      )
    );

    // Make the API call
    const result = await toggleFavourite(recordingId, !currentFavorite);
    
    if (!result.success) {
      // Revert both states on error
      setRecordings(prev => 
        prev.map(recording => 
          recording.id === recordingId 
            ? { ...recording, favourite: currentFavorite }
            : recording
        )
      );
      
      setRecordingsWithUrls(prev => 
        prev.map(recording => 
          recording.id === recordingId 
            ? { ...recording, favourite: currentFavorite }
            : recording
        )
      );
      console.error('Failed to toggle favorite:', result.error);
    }
  };

  const handleDeleteRecording = async (recordingId: string, musicTitle: string) => {
    // Show the custom modal instead of browser confirm
    setRecordingToDelete({ id: recordingId, title: musicTitle });
    setShowDeleteModal(true);
  };

  const handleDownload = async (audioUrl: string, musicTitle: string, fileName: string) => {
    try {
      // Fetch the audio file
      const response = await fetch(audioUrl);
      if (!response.ok) {
        throw new Error('Failed to download file');
      }

      // Create blob from response
      const blob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Set filename - use original filename or create one from music title
      const downloadFileName = fileName || `${musicTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.mp3`;
      link.download = downloadFileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download recording. Please try again.');
    }
  };

  const confirmDelete = async () => {
    if (!recordingToDelete) return;

    // Call the delete API
    const result = await deleteRecording(recordingToDelete.id);
    
    if (result.success) {
      // Remove from both state arrays
      setRecordings(prev => prev.filter(recording => recording.id !== recordingToDelete.id));
      setRecordingsWithUrls(prev => prev.filter(recording => recording.id !== recordingToDelete.id));
    } else {
      console.error('Failed to delete recording:', result.error);
      // Show error to user
      alert('Failed to delete recording. Please try again.');
    }

    // Close modal and reset state
    setShowDeleteModal(false);
    setRecordingToDelete(null);
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setRecordingToDelete(null);
  };

  // Filter recordings based on search term, selected music, and favorites
  const filteredRecordings = recordingsWithUrls.filter(recording => {
    const matchesSearch = !searchTerm || 
      recording.musicTitle.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesMusic = !selectedMusic || 
      recording.musicTitle === selectedMusic;
    
    const matchesFavorites = !showFavoritesOnly || recording.favourite;

    return matchesSearch && matchesMusic && matchesFavorites;
  });

  // Get unique music titles for filter dropdown
  const musicTitles = [...new Set(recordings.map(r => r.musicTitle))].sort();
  
  // Count favorites
  const favoritesCount = recordings.filter(r => r.favourite).length;

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-xl p-6 max-w-md mx-auto">
              <div className="text-red-600 font-medium mb-2">Error loading recordings</div>
              <div className="text-red-500 text-sm">{error}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-slate-800 mb-2">My Recordings</h1>
                <p className="text-slate-600">Manage and listen to your recorded songs</p>
              </div>
              <Link 
                href="/dashboard/sing-along" 
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Music className="h-4 w-4 mr-2" />
                New Recording
              </Link>
            </div>

            {/* Filter Bar Skeleton */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 max-w-md h-11 bg-slate-200 rounded-lg animate-pulse"></div>
                <div className="w-40 h-11 bg-slate-200 rounded-lg animate-pulse"></div>
                <div className="w-32 h-11 bg-slate-200 rounded-lg animate-pulse"></div>
              </div>
              <div className="h-16 bg-slate-200 rounded-lg animate-pulse"></div>
            </div>
          </div>

          {/* Loading Grid */}
          <RecordingsGridSkeleton />
        </div>
      </div>
    );
  }

  if (!recordings || recordings.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-slate-800 mb-2">My Recordings</h1>
                <p className="text-slate-600">Manage and listen to your recorded songs</p>
              </div>
              <Link 
                href="/dashboard/sing-along" 
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Music className="h-4 w-4 mr-2" />
                New Recording
              </Link>
            </div>
          </div>

          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <Music className="h-16 w-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-700 mb-2">No recordings yet</h3>
              <p className="text-slate-500 mb-6">Start recording your first song to see it here!</p>
              <Link 
                href="/dashboard/sing-along" 
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Music className="h-4 w-4 mr-2" />
                Start Recording
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        musicTitle={recordingToDelete?.title || ''}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-slate-800 mb-2">My Recordings</h1>
                <p className="text-slate-600">Manage and listen to your recorded songs</p>
              </div>
              <Link 
                href="/dashboard/sing-along" 
                className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Music className="h-4 w-4 mr-2" />
                New Recording
                </Link>
            </div>

            {/* Filter Bar */}
            <FilterBar
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              selectedMusic={selectedMusic}
              setSelectedMusic={setSelectedMusic}
              showFavoritesOnly={showFavoritesOnly}
              setShowFavoritesOnly={setShowFavoritesOnly}
              musicTitles={musicTitles}
              recordingsCount={filteredRecordings.length}
              favoritesCount={favoritesCount}
            />
          </div>

          {/* Loading state for URLs */}
          {urlsLoading && (
            <div className="mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-blue-700 text-sm font-medium">Loading audio files...</span>
                </div>
              </div>
            </div>
          )}

          {/* No Results for Favorites */}
          {showFavoritesOnly && filteredRecordings.length === 0 && favoritesCount === 0 && (
            <div className="text-center py-16">
              <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
                <Heart className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 mb-2">No favorite recordings yet</h3>
                <p className="text-slate-500 mb-6">Mark recordings as favorites by clicking the heart icon to see them here!</p>
                <button 
                  onClick={() => setShowFavoritesOnly(false)}
                  className="inline-flex items-center px-4 py-2.5 bg-slate-600 text-white font-medium rounded-lg hover:bg-slate-700 transition-all"
                >
                  View All Recordings
                </button>
              </div>
            </div>
          )}

          {/* General No Results */}
          {filteredRecordings.length === 0 && recordings.length > 0 && !showFavoritesOnly && (
            <div className="text-center py-16">
              <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
                <Search className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-slate-700 mb-2">No recordings found</h3>
                <p className="text-slate-500 mb-6">Try adjusting your filters or search terms.</p>
                <button 
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedMusic('');
                    setShowFavoritesOnly(false);
                  }}
                  className="inline-flex items-center px-4 py-2.5 bg-slate-600 text-white font-medium rounded-lg hover:bg-slate-700 transition-all"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}

          {/* Recordings Grid */}
          {filteredRecordings.length > 0 && (
            <RecordingsGrid 
              recordings={filteredRecordings} 
              onToggleFavorite={handleToggleFavorite}
              onDeleteRecording={handleDeleteRecording}
              onDownload={handleDownload}
            />
          )}
        </div>
      </div>
    </>
  );
}
