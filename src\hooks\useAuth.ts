import { useAuthStore, User } from "@/lib/store/authStore";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

// Main auth hook
export const useAuth = () => {
  const store = useAuthStore();

  return {
    // State
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,

    // Actions
    login: store.login,
    logout: store.logout,
    clearError: store.clearError,

    // Role helpers
    hasRole: store.hasRole,
    isAdmin: store.isAdmin,
    isStudent: store.isStudent,
    getRedirectPath: store.getRedirectPath,
  };
};

// Hook for role-based access control
export const useRequireAuth = (allowedRoles?: string[]) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push("/login");
        return;
      }

      if (allowedRoles && user && !allowedRoles.includes(user.role)) {
        // Redirect to appropriate dashboard based on role
        const redirectPath = useAuthStore.getState().getRedirectPath();
        router.push(redirectPath);
      }
    }
  }, [isAuthenticated, isLoading, user, allowedRoles, router]);

  return {
    user,
    isAuthenticated,
    isLoading,
    hasAccess: !allowedRoles || (user && allowedRoles.includes(user.role)),
  };
};

// Hook for admin-only access
export const useRequireAdmin = () => {
  return useRequireAuth(["SUPER_ADMIN", "ADMISSION_SUPPORT", "COURSE_MANAGER"]);
};

// Hook for student-only access
export const useRequireStudent = () => {
  return useRequireAuth(["STUDENT"]);
};

// Hook for specific role access
export const useRequireRole = (role: string) => {
  return useRequireAuth([role]);
};

// Hook to initialize auth on app start
export const useAuthInit = () => {
  const init = useAuthStore((state) => state.init);
  const isLoading = useAuthStore((state) => state.isLoading);

  useEffect(() => {
    init();
  }, [init]);

  return { isLoading };
};