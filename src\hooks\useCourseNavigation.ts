'use client'
import { useState } from 'react'
import { AccessableCourseDetails, SelectedAssignment, SelectedLesson } from '@/types/course'

export const useCourseNavigation = (
  course: AccessableCourseDetails | null,
  canAccessLesson: (id: string) => boolean,
  canAccessAssignment: (id: string) => boolean
) => {
  const [selectedLesson, setSelectedLesson] = useState<SelectedLesson | null>(null)
  const [selectedAssignment, setSelectedAssignment] = useState<SelectedAssignment | null>(null)
  const [videoProgress, setVideoProgress] = useState(0)

  // Get all lessons in order
  const getAllLessons = () => {
    if (!course) return []
    return course.modules.flatMap(module =>
      module.lessons.map(lesson => ({
        id: lesson.id,
        title: lesson.title,
        moduleId: module.id,
        video: lesson.video
      }))
    )
  }

  // Get current assignment data
  const getCurrentAssignment = () => {
    if (!selectedAssignment || !course) return null
    for (const module of course.modules) {
      const assignment = module.assignments?.find(a => a.id === selectedAssignment.id)
      if (assignment) return assignment
    }
    return null
  }

  // Get current lesson index
  const getCurrentLessonIndex = () => {
    const allLessons = getAllLessons()
    return allLessons.findIndex(lesson => lesson.id === selectedLesson?.id)
  }

  const allLessons = getAllLessons()
  const currentAssignment = getCurrentAssignment()
  const currentLessonIndex = getCurrentLessonIndex()

  // Navigation capabilities
  const canGoPrevious = currentLessonIndex > 0
  const canGoNext = currentLessonIndex < allLessons.length - 1

  // Event handlers
  const handleLessonSelect = (lesson: { id: string; title: string }) => {
    if (!canAccessLesson(lesson.id)) return
    const selectedLesson = allLessons.find(l => l.id === lesson.id) as SelectedLesson
    setSelectedLesson({
      id: selectedLesson.id,
      title: selectedLesson.title,
      video: selectedLesson.video
    })
    setSelectedAssignment(null)
    setVideoProgress(0)
  }

  const handleAssignmentSelect = (assignment: { id: string; title: string }) => {
    if (!canAccessAssignment(assignment.id)) return
    setSelectedAssignment(assignment)
    setSelectedLesson(null)
  }

  const handlePreviousLesson = () => {
    if (canGoPrevious) {
      const previousLesson = allLessons[currentLessonIndex - 1]
      handleLessonSelect({ id: previousLesson.id, title: previousLesson.title })
    }
  }

  const handleNextLesson = () => {
    if (canGoNext) {
      const nextLesson = allLessons[currentLessonIndex + 1]
      handleLessonSelect({ id: nextLesson.id, title: nextLesson.title })
    }
  }

  const handleVideoProgress = (progress: number) => {
    setVideoProgress(progress)
  }

  return {
    // State
    selectedLesson,
    selectedAssignment,
    videoProgress,
    currentAssignment,
    allLessons,
    currentLessonIndex,
    // Navigation capabilities
    canGoPrevious,
    canGoNext,
    // Handlers
    handleLessonSelect,
    handleAssignmentSelect,
    handlePreviousLesson,
    handleNextLesson,
    handleVideoProgress,
    setVideoProgress
  }
}