import { Role, User,PaymentStatus, CourseLevel } from '@prisma/client'

export type UserProfile = User 

export interface EnrollmentWithCourse {
  id: string
  enrolledAt: Date
  completedAt?: Date
  course: {
    id: string
    name: string
    description: string
    level: CourseLevel
    instructorEmail: string
  }
  progress: {
    totalModules: number
    completedModules: number
    totalLessons: number
    completedLessons: number
    totalAssignments: number
    completedAssignments: number
    progressPercentage: number
    lessonProgress: number
    assignmentProgress: number
  }
}

export interface CertificateWithCourse {
  id: string
  pdfPath: string
  issuedAt: Date
  course: {
    id: string
    name: string
    level: CourseLevel
  }
}

export interface PaymentHistory {
  id: string
  amount: number
  currency: string
  status: PaymentStatus
  createdAt: Date
  courseId?: string
  courseName?: string
  razorpayOrderId?: string
  razorpayPaymentId?: string
}

export interface ProfileStats {
  totalEnrollments: number
  completedCourses: number
  activeCourses: number
  totalCertificates: number
  totalPayments: number
  totalAmountPaid: number
  overdueItems: number
}

export interface ProfileData {
  user: UserProfile
  enrollments: EnrollmentWithCourse[]
  certificates: CertificateWithCourse[]
  payments: PaymentHistory[]
  stats: ProfileStats
} 