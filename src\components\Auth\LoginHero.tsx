import Image from 'next/image';
import { QuoteIcon, Music } from 'lucide-react';

export default function LoginHero(){
  const imageUrl = '/static-images/rini-violin.jpg'
  const imageAlt = 'Music students learning'
  const title = 'Welcome to <PERSON><PERSON><PERSON><PERSON>'
  const subtitle = 'The Music Education Platform'
  const description = 'Sign in to access your courses, track your progress, and continue your musical journey with expert guidance.'
  return (
    <div className="hidden lg:flex lg:flex-1 relative">
      <Image
        src={imageUrl}
        alt={imageAlt}
        fill
        className="object-cover"
        priority
      />
      <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-indigo-800/75 flex items-center justify-center">
        <div className="max-w-md text-center px-8">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <div className="bg-white/10 p-3 rounded-full">
              <Music className="h-10 w-10 text-white" />
            </div>
          </div>
          
          <h1 className="text-4xl font-bold text-white mb-2">{title}</h1>
          <p className="text-xl text-indigo-100 mb-6">{subtitle}</p>
          <p className="text-indigo-200 leading-relaxed">{description}</p>
          
          {/* Testimonial Quote */}
          <div className="mt-12 bg-white/10 p-6 rounded-lg border border-indigo-300/20 backdrop-blur-sm">
            <QuoteIcon className="h-8 w-8 text-indigo-300 mb-4 mx-auto" />
            <p className="text-white italic mb-4">
              "Dhwanini transformed my understanding of music theory and performance. The courses are exceptional!"
            </p>
            <div className="flex items-center justify-center">
              <div className="h-8 w-8 rounded-full bg-indigo-200 flex items-center justify-center mr-2">
                <span className="text-indigo-800 font-bold">AR</span>
              </div>
              <div className="text-left">
                <p className="text-white font-medium">Aarav Rao</p>
                <p className="text-indigo-200 text-sm">Student</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 