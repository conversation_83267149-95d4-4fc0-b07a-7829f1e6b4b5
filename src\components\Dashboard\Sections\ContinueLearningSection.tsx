// app/components/sections/ContinueLearningSection.tsx
import type { EnrollmentWithProgress } from '@/data-access/dashboard/data';
import InProgressCourseCard from '../Cards/InProgressCourseCard';
import { BookOpenCheck } from 'lucide-react';
import { getThumbnailUrl } from '@/data-access/course/storage';

interface ContinueLearningSectionProps {
  enrollments: EnrollmentWithProgress[];
}

export default async function ContinueLearningSection({ enrollments }: ContinueLearningSectionProps) {
  if (!enrollments || enrollments.length === 0) {
    return null; // Don't render the section if there are no in-progress courses
  }

  const thumbnailsMap = new Map<string, string>();

  await Promise.all(
    enrollments.map(async (enrollment) => {
      const { url, error } = await getThumbnailUrl(enrollment.course.id, enrollment.course.thumbnailPath);
      thumbnailsMap.set(enrollment.course.id, error ? "/images/placeholder-course.png" : url);
    })
  );

  return (
    <section className="mb-16">
      <div className="flex items-center mb-8">
        <BookOpenCheck size={32} className="mr-3 text-blue-600" />
        <h2 className="text-3xl font-bold text-slate-800">Continue Your Journey</h2>
      </div>
      <div className="space-y-8">
        {enrollments.map(enrollment => (
          <InProgressCourseCard 
            key={enrollment.id} 
            enrollment={enrollment} 
            thumbnail={thumbnailsMap.get(enrollment.course.id) || '/images/placeholder-course.png'} 
          />
        ))}
      </div>
    </section>
  );
}