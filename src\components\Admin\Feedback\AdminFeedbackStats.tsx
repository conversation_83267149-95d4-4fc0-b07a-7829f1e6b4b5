'use client'

import { AdminFeedbackStats as StatsType } from '@/data-access/feedback/admin'

interface AdminFeedbackStatsProps {
  stats: StatsType
}

export default function AdminFeedbackStats({ stats }: AdminFeedbackStatsProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <p className="text-sm text-gray-600 mb-1">Total Sessions</p>
        <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <p className="text-sm text-gray-600 mb-1">Scheduled</p>
        <p className="text-2xl font-semibold text-blue-600">{stats.scheduled}</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <p className="text-sm text-gray-600 mb-1">Completed</p>
        <p className="text-2xl font-semibold text-green-600">{stats.completed}</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <p className="text-sm text-gray-600 mb-1">Cancelled</p>
        <p className="text-2xl font-semibold text-amber-600">{stats.cancelled}</p>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <p className="text-sm text-gray-600 mb-1">No Show</p>
        <p className="text-2xl font-semibold text-red-600">{stats.noShow}</p>
      </div>
    </div>
  )
} 