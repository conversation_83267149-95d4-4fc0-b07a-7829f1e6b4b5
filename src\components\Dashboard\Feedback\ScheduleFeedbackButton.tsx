'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { UserAssignment } from '@/types/assignment'

interface ScheduleFeedbackButtonProps {
  assignment: UserAssignment
}

export default function ScheduleFeedbackButton({ assignment }: ScheduleFeedbackButtonProps) {
  const router = useRouter()
  
  const handleScheduleFeedback = () => {
    router.push(`/dashboard/feedback?assignmentId=${assignment.id}`)
  }

  return (
    <button
      onClick={handleScheduleFeedback}
      className="flex items-center justify-between w-full px-4 py-3 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors touch-manipulation"
    >
      <span>Schedule Feedback Session</span>
      <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    </button>
  )
} 