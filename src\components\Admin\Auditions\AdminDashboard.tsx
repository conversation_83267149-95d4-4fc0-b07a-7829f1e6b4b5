'use client';

import { useState, useCallback, useMemo } from 'react';
import { ApplicationWithDetails, ApplicationFilters } from '@/types/auditions';
import { AuditionFilters } from './AuditionFilters';
import { AuditionList } from './AuditionList';
import { useDebounce } from '@/hooks/useDebounce';

interface AdminDashboardProps {
  initialApplications: ApplicationWithDetails[];
  searchParams: ApplicationFilters;
}

export function AdminDashboard({ initialApplications, searchParams }: AdminDashboardProps) {
  const [applications, setApplications] = useState(initialApplications);
  const [filters, setFilters] = useState<ApplicationFilters>(searchParams);
  const [isLoading, setIsLoading] = useState(false);

  // Debounce search for better performance
  const debouncedSearchTerm = useDebounce(filters.search || '', 300);

  // Filter applications client-side for immediate feedback
  const filteredApplications = useMemo(() => {
    let filtered = applications;

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(app => app.auditionStatus === filters.status);
    }

    if (filters.courseId) {
      filtered = filtered.filter(app => app.courseId === filters.courseId);
    }

    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      filtered = filtered.filter(app =>
        app.user.name?.toLowerCase().includes(searchLower) ||
        app.user.email.toLowerCase().includes(searchLower) ||
        app.course.name.toLowerCase().includes(searchLower)
      );
    }

    if (filters.dateFrom) {
      const dateFrom = new Date(filters.dateFrom);
      filtered = filtered.filter(app => new Date(app.createdAt) >= dateFrom);
    }

    if (filters.dateTo) {
      const dateTo = new Date(filters.dateTo);
      filtered = filtered.filter(app => new Date(app.createdAt) <= dateTo);
    }

    return filtered;
  }, [applications, filters.status, filters.courseId, filters.dateFrom, filters.dateTo, debouncedSearchTerm]);

  const handleFiltersChange = useCallback((newFilters: ApplicationFilters) => {
    setFilters(newFilters);
  }, []);

  const handleApplicationUpdate = useCallback((updatedApplication: ApplicationWithDetails) => {
    setApplications(prev => 
      prev.map(app => 
        app.id === updatedApplication.id ? updatedApplication : app
      )
    );
  }, []);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <AuditionFilters 
          onFiltersChange={handleFiltersChange}
          initialFilters={filters}
          isLoading={isLoading}
        />
      </div>

      {/* Applications List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Applications</h2>
            <div className="text-sm text-gray-500">
              {filteredApplications.length} of {applications.length}
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <AuditionList 
            applications={filteredApplications}
            onApplicationUpdate={handleApplicationUpdate}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
} 