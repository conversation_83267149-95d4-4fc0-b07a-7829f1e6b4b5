{"name": "learning-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@calcom/embed-react": "^1.5.3", "@mux/mux-node": "^11.1.0", "@mux/mux-player": "^3.4.0", "@mux/mux-player-react": "^3.4.0", "@mux/mux-uploader-react": "^1.2.0", "@mux/videojs-kit": "^0.12.0", "@prisma/client": "^6.8.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@types/video.js": "^7.3.58", "babel-plugin-react-compiler": "^19.1.0-rc.2", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "nextjs-server-action-tester": "^1.0.4", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "video.js": "^8.22.0", "zod": "^3.25.20", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}