import ModernSidebar from "@/components/Layout/ModernSidebar";
import { getUser } from "@/data-access/auth";


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await getUser()
  return (
    <div className="flex min-h-screen bg-white">
        <ModernSidebar userDetails={{email: user?.email, name: user?.user_metadata.name, role: user?.user_metadata.role}} />
        <main className="flex-1 overflow-y-auto bg-white pt-16 pb-16 md:ml-64 md:pt-0 md:pb-0">
        <div className="w-full p-6">
            {children}
        </div>
        </main>

    </div>
  );
}