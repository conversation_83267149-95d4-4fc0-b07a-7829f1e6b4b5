import { LoginForm } from '@/components/Auth/LoginForm';
import LoginHero from '@/components/Auth/LoginHero';
import { Music } from 'lucide-react';


export default function LoginPage() {

  return (
    <div className="flex min-h-screen bg-white text-gray-700">
      {/* Left side - Hero Image (hidden on mobile) */}
      <LoginHero />
      
      {/* Mobile Logo - Only visible on mobile */}
      <div className="lg:hidden absolute top-0 left-0 w-full p-6 flex justify-center">
        <div className="p-2 bg-indigo-100 rounded-full">
          <Music className="h-6 w-6 text-indigo-600" />
        </div>
      </div>
      
      {/* Right side - Login Form */}
      <div className="flex flex-1 items-center justify-center p-6 pt-20 lg:p-12 lg:pt-12">
        <div className="w-full max-w-md">
          <LoginForm />
        </div>
      </div>
    </div>
  );
}