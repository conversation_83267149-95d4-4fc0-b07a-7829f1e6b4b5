"use client"

import { useState } from "react"
import { BookOpen, X, Save, AlertCircle } from "lucide-react"
import { Module as ModuleData } from "@/types/course"


interface ModuleBuilderProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: ModuleData) => Promise<void>
  initialData?: Partial<ModuleData>
  nextOrder: number
}

export default function ModuleBuilder({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData, 
  nextOrder 
}: ModuleBuilderProps) {
  const [formData, setFormData] = useState<ModuleData>({
    id: initialData?.id || "",
    courseId: initialData?.courseId || "",
    title: initialData?.title || "",
    description: initialData?.description || null,
    order: initialData?.order || nextOrder,
    lessons: initialData?.lessons || [],
    assignments: initialData?.assignments || [],
    createdAt: initialData?.createdAt || new Date(),
    updatedAt: initialData?.updatedAt || new Date()
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      newErrors.title = "Module title is required"
    }
    
    if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters"
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {      
      await onSave(formData)
      onClose()
    } 
    catch (error) {
      console.error("Failed to save module:", error)
    } 
    finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 text-gray-700 bg-white/70 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <BookOpen className="w-5 h-5 text-gray-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {initialData ? "Edit Module" : "Create New Module"}
              </h2>
              <p className="text-sm text-gray-600">
                {initialData ? "Update module details" : "Add a new module to your course"}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Module Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.title ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter module title"
            />
            {errors.title && (
              <div className="flex items-center gap-2 mt-2 text-red-600">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{errors.title}</span>
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description || ""}
              onChange={(e) => setFormData({ 
                ...formData, 
                description: e.target.value || null 
              })}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                errors.description ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Detailed description of what students will learn in this module (optional)"
            />
            {errors.description && (
              <div className="flex items-center gap-2 mt-2 text-red-600">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{errors.description}</span>
              </div>
            )}
            <div className="text-right text-sm text-gray-500 mt-1">
              {formData.description?.length || 0}/500
            </div>
          </div>

          {/* Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Module Order
            </label>
            <input
              type="number"
              value={formData.order}
              onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 1 })}
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="1"
            />
            <p className="text-sm text-gray-500 mt-1">
              Determines the order in which modules appear in the course
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-white transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              {loading ? "Saving..." : "Save Module"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 