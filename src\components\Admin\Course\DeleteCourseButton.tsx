"use client"

import { useState, useTransition } from "react"
import { Trash2, AlertTriangle } from "lucide-react"
import { deleteCourseAction } from "@/data-access/course/admin-course"

interface DeleteCourseButtonProps {
  courseId: string
  courseName: string
}

export function DeleteCourseButton({ courseId, courseName }: DeleteCourseButtonProps) {
  const [showConfirm, setShowConfirm] = useState(false)
  const [isPending, startTransition] = useTransition()

  const handleDelete = () => {
    startTransition(async () => {
      const result = await deleteCourseAction(courseId)
      if (result.success) {
        setShowConfirm(false)
        // The page will automatically revalidate due to revalidatePath in the action
      } else {
        console.error('Failed to delete course:', result.error)
        // You could show a toast notification here
      }
    })
  }

  return (
    <>
      <button
        onClick={() => setShowConfirm(true)}
        className="p-2.5 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg transition-colors"
        title="Delete course"
      >
        <Trash2 className="w-4 h-4" />
      </button>

      {/* Confirmation Modal */}
      {showConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Delete Course</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete <strong>"{courseName}"</strong>? This action cannot be undone and will permanently remove all course content, modules, lessons, and assignments.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={() => setShowConfirm(false)}
                disabled={isPending}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-white transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={isPending}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {isPending ? 'Deleting...' : 'Delete Course'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
} 