'use client';

import { Edit, Trash2, Play } from 'lucide-react';

interface MusicLibraryActionsProps {
  songId: string;
  onDelete: (songId: string) => void;
}

export default function MusicLibraryActions({ songId, onDelete }: MusicLibraryActionsProps) {
  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this song?')) {
      onDelete(songId);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <button
        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-white"
        title="Play"
      >
        <Play className="h-4 w-4" />
      </button>
      <button
        className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-white"
        title="Edit"
      >
        <Edit className="h-4 w-4" />
      </button>
      <button
        onClick={handleDelete}
        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
        title="Delete"
      >
        <Trash2 className="h-4 w-4" />
      </button>
    </div>
  );
} 