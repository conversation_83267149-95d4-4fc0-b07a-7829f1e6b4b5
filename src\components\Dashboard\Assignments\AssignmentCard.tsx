import { UserAssignment } from '@/types/assignment'
import { SubmissionStatus } from '@prisma/client'
import Link from 'next/link'

interface AssignmentCardProps {
  assignment: UserAssignment
}

export function AssignmentCard({ assignment }: AssignmentCardProps) {
  const getStatusColor = () => {
    if (assignment.isOverdue) return 'text-red-700 bg-red-50 border-red-200'
    if (assignment.submission?.status === SubmissionStatus.GRADED) return 'text-green-700 bg-green-50 border-green-200'
    if (assignment.submission?.status === SubmissionStatus.SUBMITTED) return 'text-blue-700 bg-white border-blue-200'
    if (assignment.submission?.status === SubmissionStatus.NEEDS_REVISION) return 'text-orange-700 bg-orange-50 border-orange-200'
    return 'text-gray-700 bg-white border-gray-200'
  }

  const getStatusText = () => {
    if (assignment.isOverdue) return 'Overdue'
    if (assignment.submission?.status === SubmissionStatus.GRADED) return 'Graded'
    if (assignment.submission?.status === SubmissionStatus.SUBMITTED) return 'Submitted'
    if (assignment.submission?.status === SubmissionStatus.NEEDS_REVISION) return 'Needs Revision'
    return 'Pending'
  }

  const getDeadlineText = () => {
    if (assignment.isOverdue) {
      const daysOverdue = Math.abs(assignment.daysUntilDeadline)
      return `${daysOverdue} day${daysOverdue === 1 ? '' : 's'} overdue`
    }
    if (assignment.daysUntilDeadline === 0) return 'Due today'
    if (assignment.daysUntilDeadline === 1) return 'Due tomorrow'
    if (assignment.daysUntilDeadline > 0) return `Due in ${assignment.daysUntilDeadline} days`
    return 'Past due'
  }

  const getDeadlineColor = () => {
    if (assignment.isOverdue) return 'text-red-600'
    if (assignment.daysUntilDeadline <= 1) return 'text-orange-600'
    if (assignment.daysUntilDeadline <= 3) return 'text-yellow-600'
    return 'text-gray-600'
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  return (
    <Link href={`/dashboard/assignments/${assignment.id}`}>
      <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md hover:border-gray-300 transition-all duration-200 cursor-pointer">
        
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-700 transition-colors line-clamp-1">
              {assignment.title}
            </h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>{assignment.module.course.name}</div>
              <div className="text-gray-500">{assignment.module.title}</div>
            </div>
          </div>
          
          <div className="ml-4">
            <span className={`inline-flex px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>
        </div>

        {/* Description */}
        {assignment.description && (
          <div className="mb-4">
            <p className="text-gray-700 text-sm leading-relaxed line-clamp-2">
              {assignment.description}
            </p>
          </div>
        )}

        {/* Deadline */}
        <div className="flex items-center justify-between text-sm mb-4">
          <div className="flex items-center text-gray-500">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Due: {formatDate(assignment.deadline)}</span>
          </div>
          <span className={`text-sm font-medium ${getDeadlineColor()}`}>
            {getDeadlineText()}
          </span>
        </div>

        {/* Submission Status */}
        {assignment.submission ? (
          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Submitted {formatDate(assignment.submission.submittedAt)}</span>
              </div>
              {assignment.submission.feedback && (
                <span className="text-blue-600 text-sm font-medium">
                  Feedback available
                </span>
              )}
            </div>
          </div>
        ) : (
          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span>Not submitted</span>
            </div>
          </div>
        )}
      </div>
    </Link>
  )
} 