"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { prisma } from "@/utils/prisma/prisma";
import { requireRole, getUser } from "../auth";
import { ApplicationFilters, ApplicationWithDetails, CreateApplicationData, UpdateApplicationData } from "@/types/auditions";
import { AuditionStatus, PaymentStatus } from "@prisma/client";
import { ActionResult } from "../helpers";
import { createClient } from "@/utils/supabase/server";

// Validation schemas
const CreateApplicationSchema = z.object({
  courseId: z.string().min(1, "Course is required"),
  userDetails: z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Invalid email address"),
    phone: z.string().optional(),
    country: z.string().optional(),
    experience: z.string().optional(),
    motivation: z.string().optional(),
  }),
  audioPath: z.string().optional(),
});

const UpdateApplicationSchema = z.object({
  auditionStatus: z.enum([
    "PENDING",
    "SUBMITTED", 
    "UNDER_REVIEW",
    "APPROVED",
    "REJECTED",
  ]),
  feedback: z.string().optional(),
});

const ApplicationFiltersSchema = z.object({
  status: z.string().optional(),
  courseId: z.string().optional(),
  search: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

// ===== PUBLIC FUNCTIONS =====

export async function createApplication(data: CreateApplicationData): Promise<{
  success: boolean;
  data: string | null; // application ID
  error: string | null;
}> {
  try {
    const user = await getUser();
    if (!user) {
      return { success: false, data: null, error: "Authentication required" };
    }

    // Validate input
    const validatedData = CreateApplicationSchema.parse(data);

    // Check if user already has an application for this course
    const existingApplication = await prisma.application.findFirst({
      where: {
        userId: user.id,
        courseId: validatedData.courseId,
      },
    });

    if (existingApplication) {
      return { 
        success: false, 
        data: null, 
        error: "You have already applied for this course" 
      };
    }

    // Get course details
    const course = await prisma.course.findUnique({
      where: { id: validatedData.courseId },
      select: { auditionFee: true, name: true },
    });

    if (!course) {
      return { success: false, data: null, error: "Course not found" };
    }

    // Create application
    const application = await prisma.application.create({
      data: {
        userId: user.id,
        courseId: validatedData.courseId,
        userDetails: validatedData.userDetails,
        audioPath: validatedData.audioPath,
        auditionStatus: validatedData.audioPath ? "SUBMITTED" : "PENDING",
        auditionPaymentStatus: course.auditionFee > 0 ? "INITIATED" : "COMPLETED",
        enrollmentPaymentStatus: "PENDING",
      },
    });

    revalidatePath("/dashboard/applications");
    revalidatePath("/admissions");

    return { success: true, data: application.id, error: null };
  } catch (error) {
    console.error("Error creating application:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: null,
        error: "Invalid input: " + error.errors.map(e => e.message).join(", "),
      };
    }
    return {
      success: false,
      data: null,
      error: "Failed to create application",
    };
  }
}

export async function getUserApplications(): Promise<{
  success: boolean;
  data: ApplicationWithDetails[];
  error: string | null;
}> {
  try {
    const user = await getUser();
    if (!user) {
      return { success: false, data: [], error: "Authentication required" };
    }

    const applications = await prisma.application.findMany({
      where: { userId: user.id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            level: true,
            price: true,
            currency: true,
            auditionFee: true,
            instructorEmail: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            country: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: applications as ApplicationWithDetails[], error: null };
  } catch (error) {
    console.error("Error fetching user applications:", error);
    return { success: false, data: [], error: "Failed to fetch applications" };
  }
}

export async function uploadAuditionFile(
  applicationId: string,
  audioPath: string
): Promise<{
  success: boolean;
  data: null;
  error: string | null;
}> {
  try {
    const user = await getUser();
    if (!user) {
      return { success: false, data: null, error: "Authentication required" };
    }

    // Verify the application belongs to the user
    const application = await prisma.application.findFirst({
      where: {
        id: applicationId,
        userId: user.id,
      },
    });

    if (!application) {
      return { success: false, data: null, error: "Application not found" };
    }

    // Update application with audio file
    await prisma.application.update({
      where: { id: applicationId },
      data: {
        audioPath,
        auditionStatus: "SUBMITTED",
        updatedAt: new Date(),
      },
    });

    revalidatePath("/admin/admissions");

    return { success: true, data: null, error: null };
  } catch (error) {
    console.error("Error uploading audition file:", error);
    return { success: false, data: null, error: "Failed to upload file" };
  }
}

// ===== ADMIN FUNCTIONS =====

export async function getApplications(filters: ApplicationFilters = {}): Promise<{
  success: boolean;
  data: ApplicationWithDetails[];
  error: string | null;
}> {
  try {
    // Require admin role
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: [], error: roleCheck.error };
    }

    // Validate filters
    const validatedFilters = ApplicationFiltersSchema.parse(filters);

    const whereClause: any = {};

    if (validatedFilters.status && validatedFilters.status !== "all") {
      whereClause.auditionStatus = validatedFilters.status;
    }

    if (validatedFilters.courseId) {
      whereClause.courseId = validatedFilters.courseId;
    }

    if (validatedFilters.search) {
      whereClause.OR = [
        { user: { name: { contains: validatedFilters.search, mode: "insensitive" } } },
        { user: { email: { contains: validatedFilters.search, mode: "insensitive" } } },
        { course: { name: { contains: validatedFilters.search, mode: "insensitive" } } },
      ];
    }

    if (validatedFilters.dateFrom || validatedFilters.dateTo) {
      whereClause.createdAt = {};
      if (validatedFilters.dateFrom) {
        whereClause.createdAt.gte = new Date(validatedFilters.dateFrom);
      }
      if (validatedFilters.dateTo) {
        whereClause.createdAt.lte = new Date(validatedFilters.dateTo);
      }
    }

    const applications = await prisma.application.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            country: true,
          },
        },
        course: {
          select: {
            id: true,
            name: true,
            level: true,
            price: true,
            currency: true,
            auditionFee: true,
            instructorEmail: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: applications as ApplicationWithDetails[], error: null };
  } catch (error) {
    console.error("Error fetching applications:", error);
    return { success: false, data: [], error: "Failed to fetch applications" };
  }
}

export async function getApplicationById(applicationId: string): Promise<{
  success: boolean;
  data: ApplicationWithDetails | null;
  error: string | null;
}> {
  try {
    // Require admin role
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            country: true,
          },
        },
        course: {
          select: {
            id: true,
            name: true,
            level: true,
            price: true,
            currency: true,
            auditionFee: true,
            instructorEmail: true,
          },
        },
      },
    });

    if (!application) {
      return { success: false, data: null, error: "Application not found" };
    }

    return { success: true, data: application, error: null };
  } catch (error) {
    console.error("Error fetching application:", error);
    return { success: false, data: null, error: "Failed to fetch application" };
  }
}

export async function updateApplication(
  applicationId: string,
  data: UpdateApplicationData
): Promise<{
  success: boolean;
  data: ApplicationWithDetails | null;
  error: string | null;
}> {
  try {
    // Require admin role
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    // Validate input
    const validatedData = UpdateApplicationSchema.parse(data);

    const updatedApplication = await prisma.application.update({
      where: { id: applicationId },
      data: {
        auditionStatus: validatedData.auditionStatus as AuditionStatus,
        feedback: validatedData.feedback,
        updatedAt: new Date(),
      },
      include: {
        user: true,
        course: true,
      },
    });

    revalidatePath("/admin/admissions");
    revalidatePath(`/admin/admissions/${applicationId}`);

    return { success: true, data: updatedApplication as ApplicationWithDetails, error: null };
  } catch (error) {
    console.error("Error updating application:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: null,
        error: "Invalid input: " + error.errors.map(e => e.message).join(", "),
      };
    }
    return {
      success: false,
      data: null,
      error: "Failed to update application",
    };
  }
}

export async function deleteApplication(applicationId: string): Promise<{
  success: boolean;
  data: null;
  error: string | null;
}> {
  try {
    // Require admin role
    const roleCheck = await requireRole(["SUPER_ADMIN"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    await prisma.application.delete({
      where: { id: applicationId },
    });

    revalidatePath("/admin/admissions");

    return { success: true, data: null, error: null };
  } catch (error) {
    console.error("Error deleting application:", error);
    return { success: false, data: null, error: "Failed to delete application" };
  }
}

// ===== ANALYTICS =====

export async function getApplicationStats(): Promise<{
  success: boolean;
  data: {
    total: number;
    pending: number;
    underReview: number;
    approved: number;
    rejected: number;
    recentApplications: number;
  } | null;
  error: string | null;
}> {
  try {
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    const [
      total,
      pending,
      underReview,
      approved,
      rejected,
      recentApplications,
    ] = await Promise.all([
      prisma.application.count(),
      prisma.application.count({ where: { auditionStatus: "PENDING" } }),
      prisma.application.count({ where: { auditionStatus: "UNDER_REVIEW" } }),
      prisma.application.count({ where: { auditionStatus: "APPROVED" } }),
      prisma.application.count({ where: { auditionStatus: "REJECTED" } }),
      prisma.application.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
      }),
    ]);

    return {
      success: true,
      data: {
        total,
        pending,
        underReview,
        approved,
        rejected,
        recentApplications,
      },
      error: null,
    };
  } catch (error) {
    console.error("Error fetching application stats:", error);
    return { success: false, data: null, error: "Failed to fetch stats" };
  }
}

export async function updateFeedbackStatus(applicationId: string, feedbackStatus: string, feedback: string): Promise<ActionResult<boolean>> {
  try {
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    const updatedApplication = await prisma.application.update({
      where: { id: applicationId },
      data: {
        feedback: feedback,
        auditionStatus: feedbackStatus as AuditionStatus,
      },
    });

    if (!updatedApplication) {
      return { success: false, data: null, error: "Application not found" };
    }

    return { success: true, data: true, error: null };
  }
  catch (error) {
    console.error("Error updating feedback status:", error);
    return { success: false, data: null, error: "Failed to update feedback status" };
  }
}

export async function getAudioUrl(audioPath: string): Promise<{
  success: boolean;
  data: string | null;
  error: string | null;
}> {
  const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-private';
  try {
    const roleCheck = await requireRole(["SUPER_ADMIN", "ADMISSION_SUPPORT"]);
    if (!roleCheck.success) {
      return { success: false, data: null, error: roleCheck.error };
    }

    const supabase = await createClient();
    const { data, error } = await supabase.storage.from(BUCKET_NAME).createSignedUrl(audioPath, 60 * 60);
    if (error) {
      return { success: false, data: null, error: "Failed to fetch audio URL" };
    }

    return { success: true, data: data?.signedUrl || null, error: null };
  } 
  catch (error) {
    console.error("Error fetching audio URL:", error);
    return { success: false, data: null, error: "Failed to fetch audio URL" };
  }
}