'use client';

import Link from 'next/link';
import { Frown } from 'lucide-react'; 


export function NotFoundPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white text-center px-4 py-8 lg:py-16">
      {/* Main content container */}
      <div className="max-w-md w-full">
        {/* Icon */}
        <div className="mb-6 text-blue-500">
          <Frown size={80} className="mx-auto" strokeWidth={1.5} />
        </div>

        {/* Heading */}
        <h1 className="text-5xl sm:text-6xl font-bold text-slate-800 mb-4">
          404
        </h1>
        <h2 className="text-2xl sm:text-3xl font-semibold text-slate-700 mb-3">
          Page Not Found
        </h2>

        {/* Message */}
        <p className="text-slate-600 mb-8 text-base sm:text-lg leading-relaxed">
          Oops! The page you're looking for doesn't seem to exist. It might have been moved, deleted, or perhaps you mistyped the URL.
        </p>

        {/* Call to action button */}
        <Link
          href="/dashboard"
          className="inline-flex items-center justify-center px-6 py-3 bg-blue-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2"
        >
          Go to Homepage
        </Link>

      </div>
    </div>
  );
}

export default NotFoundPage;
