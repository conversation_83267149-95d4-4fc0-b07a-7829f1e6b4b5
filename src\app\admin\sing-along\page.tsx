import { Music, Plus } from 'lucide-react';
import { getAvailableSongs } from '@/data-access/sing-along/songs';
import AdminDashboardClient from '@/components/Admin/SingAlong/AdminDashboardClient';
import { Suspense } from 'react';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { LoadingSkeleton } from '@/components/ui/LoadingSkeleton';

interface SingAlongAdminPageProps {
  searchParams: Promise<{
    search?: string;
    genre?: string;
    difficulty?: string;
    sortBy?: string;
    sortOrder?: string;
  }>;
}

function PageHeader() {
  return (
    <div className="flex justify-between items-start">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Sing Along</h1>
        <p className="text-gray-600 mt-2">Manage your music library and karaoke content</p>
      </div>
    </div>
  );
}

export default async function SingAlongAdminPage({ searchParams }: SingAlongAdminPageProps) {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        <PageHeader />
        
        <ErrorBoundary fallback={<div className="text-red-600">Failed to load songs</div>}>
          <Suspense fallback={<LoadingSkeleton />}>
            <SongContent searchParams={searchParams} />
          </Suspense>
        </ErrorBoundary>
      </div>
    </div>
  );
}

async function SongContent({ searchParams }: { searchParams: Promise<{ search?: string; genre?: string; difficulty?: string; sortBy?: string; sortOrder?: string; }> }) {
  const { songs, error } = await getAvailableSongs();
  const resolvedSearchParams = await searchParams;
  
  if (error) {
    throw new Error(error || 'Failed to load songs');
  }

  return <AdminDashboardClient initialSongs={songs} searchParams={resolvedSearchParams} />;
}
