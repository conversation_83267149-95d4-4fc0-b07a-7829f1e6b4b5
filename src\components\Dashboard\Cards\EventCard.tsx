// app/components/cards/EventCard.tsx
import { CalendarDays, Clock, BookOpen, AlertTriangle, CheckCircle2 } from 'lucide-react';
import type { UpcomingDeadline } from '@/data-access/dashboard/data';

interface EventCardProps {
  deadline: UpcomingDeadline;
}

const EventIcon = ({ type }: { type: UpcomingDeadline['type'] }) => {
  switch (type) {
    case 'feedback':
      return <CheckCircle2 className="text-blue-600" size={24} />;
    case 'assignment':
      return <AlertTriangle className="text-red-500" size={24} />;
    default:
      return <CalendarDays className="text-blue-600" size={24} />;
  }
};

export default function EventCard({ deadline }: EventCardProps) {
  const formattedDate = deadline.deadline.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long', 
    day: 'numeric'
  });
  
  const formattedTime = deadline.deadline.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 flex items-start space-x-4 transition-all duration-300 hover:shadow-xl hover:scale-[1.02]">
      <div className="flex-shrink-0 w-12 h-12 bg-white rounded-full flex items-center justify-center">
        <EventIcon type={deadline.type} />
      </div>
      <div>
        <h3 className="text-lg font-semibold text-slate-800 mb-1">{deadline.title}</h3>
        <p className="text-sm text-slate-600 flex items-center mb-1">
          <CalendarDays size={16} className="mr-2 text-slate-400" />
          {formattedDate}
        </p>
        <p className="text-sm text-slate-500 flex items-center mb-1">
          <Clock size={16} className="mr-2 text-slate-400" />
          {formattedTime}
        </p>
        <p className="text-sm text-slate-500 flex items-center mt-1">
          <BookOpen size={16} className="mr-2 text-slate-400" />
          {deadline.courseName}
        </p>
      </div>
    </div>
  );
}