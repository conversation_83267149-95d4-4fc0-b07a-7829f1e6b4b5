import Link from 'next/link';
import { Play, Headphones, Music4 } from 'lucide-react';
import SongLibrary from '@/components/SingAlong/SongLibrary';

// TODO: Add Recent Sessions

export default function SingAlongPage() {
  
  return (
    <div className="min-h-screen w-full bg-white">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Hero Section */}
        <section className="rounded-2xl bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white p-8 md:p-12 mb-12 relative overflow-hidden">
          <div className="relative z-10">
            <div className="flex items-center mb-4">
              <Headphones size={32} className="mr-3 text-blue-300" />
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Sing Along Studio
              </h1>
            </div>
            <p className="text-blue-200 text-lg max-w-3xl mb-8 leading-relaxed">
              Perfect your classical music skills with karaoke-style practice sessions. Upload your own songs, 
              record performances, and track your progress with real-time feedback.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/dashboard/sing-along/play"
                className="group inline-flex items-center bg-white text-blue-700 font-semibold px-6 py-3 rounded-lg shadow-md hover:bg-white transition-colors duration-300 text-base focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-700"
              >
                <Play size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110" />
                Practice with Lyrics
              </Link>
            </div>
          </div>
          
          {/* Background patterns */}
          <Music4 className="absolute -right-10 -bottom-16 text-white text-opacity-10 w-72 h-72 md:w-96 md:h-96 transform rotate-[15deg]" strokeWidth={1} />
          <div className="absolute -left-20 top-10 opacity-5">
            <Headphones className="w-80 h-80 text-white" strokeWidth={0.5} />
          </div>
        </section>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Song Library */}
          <div className="lg:col-span-2">
            <SongLibrary />
          </div>
        
        </div>

        {/* Footer */}
        <footer className="text-center py-10 mt-12 border-t border-slate-200">
          <p className="text-sm text-slate-500">&copy; {new Date().getFullYear()} Dhwanini Academy. All rights reserved.</p>
          <p className="text-xs text-slate-400 mt-1">Nurturing Classical Arts</p>
        </footer>
      </main>
    </div>
  );
} 