'use server';
import { createClient } from "@/utils/supabase/server";

const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-private';

export const getSignedUrl = async (fileName: string): Promise<{signedUrl: string | null, error: string | null}> => {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.storage.from(BUCKET_NAME).createSignedUrl(fileName, 60 * 60);
        if (error) {
            throw error;
        }
        return {signedUrl: data.signedUrl, error: null};
    }
    catch (error) {
        console.error(`Error getting signed url for ${fileName} in bucket ${BUCKET_NAME}`, error);
        return {signedUrl: null, error: error instanceof Error ? error.message : 'Unknown error'};
    }
}


