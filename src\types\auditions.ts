import { Prisma } from "@prisma/client";

// Base application type from Prisma with specific selections
export type ApplicationWithDetails = Prisma.ApplicationGetPayload<{
  include: {
    course: {
      select: {
        id: true;
        name: true;
        level: true;
        price: true;
        currency: true;
        auditionFee: true;
        instructorEmail: true;
      };
    };
    user: {
      select: {
        id: true;
        name: true;
        email: true;
        phone: true;
        country: true;
      };
    };
  };
}>

// Input types for API functions
export interface CreateApplicationData {
  courseId: string;
  userDetails: {
    name: string;
    email: string;
    phone?: string;
    country?: string;
    experience?: string;
    motivation?: string;
  };
  audioPath?: string;
}

export interface UpdateApplicationData {
  auditionStatus: string;
  feedback?: string;
}

export interface ApplicationFilters {
  status?: string;
  courseId?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}