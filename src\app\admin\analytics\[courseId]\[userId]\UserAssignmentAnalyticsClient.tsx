'use client'

import { useState, useTransition } from 'react'
import { SubmissionStatus } from '@prisma/client'
import { UserAssignmentAnalytics } from '@/types/analytics'
import { updateAssignmentSubmissionStatus } from '@/data-access/analytics/analytics'
import UserAssignmentAnalyticsDashboard from '@/components/Admin/Analytics/UserAssignmentAnalyticsDashboard'
import { useRouter } from 'next/navigation'

interface UserAssignmentAnalyticsClientProps {
  initialData: UserAssignmentAnalytics
  courseId: string
  userId: string
}

export default function UserAssignmentAnalyticsClient({
  initialData,
  courseId,
  userId
}: UserAssignmentAnalyticsClientProps) {
  const [data, setData] = useState(initialData)
  const [isPending, startTransition] = useTransition()
  const router = useRouter()

  const handleStatusUpdate = async (
    submissionId: string, 
    status: SubmissionStatus, 
    feedback?: string
  ) => {
    try {
      const result = await updateAssignmentSubmissionStatus(submissionId, status, feedback)
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update submission status')
      }

      // Update local state optimistically
      setData(prevData => ({
        ...prevData,
        submissions: prevData.submissions.map(submission => {
          if (submission.id === submissionId) {
            return {
              ...submission,
              status,
              feedback: feedback || submission.feedback,
              updatedAt: new Date()
            }
          }
          return submission
        })
      }))

      // Recalculate stats
      setData(prevData => {
        const submissions = prevData.submissions
        const newStats = {
          totalAssignments: submissions.length,
          submitted: submissions.filter(s => s.hasSubmission).length,
          graded: submissions.filter(s => s.status === SubmissionStatus.GRADED).length,
          needsRevision: submissions.filter(s => s.status === SubmissionStatus.NEEDS_REVISION).length,
          overdue: submissions.filter(s => s.isOverdue).length,
          pending: submissions.filter(s => !s.hasSubmission && !s.isOverdue).length
        }

        return {
          ...prevData,
          stats: newStats
        }
      })

      // Refresh the page data to ensure consistency
      startTransition(() => {
        router.refresh()
      })

    } catch (error) {
      console.error('Failed to update submission status:', error)
      alert(error instanceof Error ? error.message : 'Failed to update submission status')
    }
  }

  return (
    <UserAssignmentAnalyticsDashboard
      data={data}
      onStatusUpdate={handleStatusUpdate}
      isUpdating={isPending}
    />
  )
} 