'use client';

import { CourseLevel } from "@prisma/client";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";
import Link from "next/link";

type LevelFilter = CourseLevel | 'ALL';

interface FilterFormProps {
  selectedLevel: LevelFilter;
  searchQuery: string;
}

export default function FilterForm({ selectedLevel, searchQuery }: FilterFormProps) {
  const router = useRouter();

  const handleLevelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (e.target.value !== 'ALL') params.set('level', e.target.value);
    router.push(`/dashboard/courses${params.toString() ? `?${params.toString()}` : ''}`);
  };

  return (
    <div className="mt-4 md:mt-0 flex text-gray-900 gap-4">
      {/* Search Form */}
      <form className="relative flex-1 md:flex-none" action="/dashboard/courses">
        <input
          type="text"
          name="search"
          placeholder="Search courses..."
          defaultValue={searchQuery}
          className="w-full md:w-64 pl-10 pr-4 py-2.5 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {searchQuery && (
          <Link
            href={`/dashboard/courses${selectedLevel !== 'ALL' ? `?level=${selectedLevel}` : ''}`}
            className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </Link>
        )}
      </form>
      {/* Level Filter */}
      <select
        value={selectedLevel}
        onChange={handleLevelChange}
        className="inline-flex items-center px-4 py-2.5 border rounded-lg text-sm font-medium transition-colors border-gray-300 text-gray-700 bg-white hover:bg-white"
      >
        <option value="ALL">All Levels</option>
        <option value={CourseLevel.BEGINNER}>Beginner</option>
        <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
        <option value={CourseLevel.ADVANCED}>Advanced</option>
      </select>
    </div>
  );
} 