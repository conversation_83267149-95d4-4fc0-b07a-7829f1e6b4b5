'use client'

import { useState } from 'react'

export type FilterStatus = 'all' | 'pending' | 'submitted' | 'graded' | 'overdue' | 'needs_revision'

interface AssignmentFiltersProps {
  onFilterChange: (status: FilterStatus, searchTerm: string) => void
  courses: Array<{ id: string; name: string }>
  selectedCourse: string
  onCourseChange: (courseId: string) => void
}

export function AssignmentFilters({ 
  onFilterChange, 
  courses, 
  selectedCourse, 
  onCourseChange 
}: AssignmentFiltersProps) {
  const [activeFilter, setActiveFilter] = useState<FilterStatus>('all')
  const [searchTerm, setSearchTerm] = useState('')

  const filters = [
    { key: 'all' as FilterStatus, label: 'All', color: 'text-gray-600 bg-gray-100' },
    { key: 'pending' as FilterStatus, label: 'Pending', color: 'text-gray-600 bg-gray-100' },
    { key: 'submitted' as FilterStatus, label: 'Submitted', color: 'text-blue-600 bg-blue-100' },
    { key: 'graded' as FilterStatus, label: 'Graded', color: 'text-green-600 bg-green-100' },
    { key: 'overdue' as FilterStatus, label: 'Overdue', color: 'text-red-600 bg-red-100' },
    { key: 'needs_revision' as FilterStatus, label: 'Needs Revision', color: 'text-orange-600 bg-orange-100' }
  ]

  const handleFilterClick = (status: FilterStatus) => {
    setActiveFilter(status)
    onFilterChange(status, searchTerm)
  }

  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    onFilterChange(activeFilter, value)
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="Search assignments..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Course Filter */}
        <div className="flex items-center gap-4">
          <select
            value={selectedCourse}
            onChange={(e) => onCourseChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Courses</option>
            {courses.map((course) => (
              <option key={course.id} value={course.id}>
                {course.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Status Filters */}
      <div className="flex flex-wrap gap-2 mt-4">
        {filters.map((filter) => (
          <button
            key={filter.key}
            onClick={() => handleFilterClick(filter.key)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeFilter === filter.key
                ? filter.color
                : 'text-gray-500 bg-white hover:bg-gray-100'
            }`}
          >
            {filter.label}
          </button>
        ))}
      </div>
    </div>
  )
} 