'use client';
import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard, Music, BookOpen,
  MessageSquare,
  Award, NotebookPen, Menu, X, Mic,
  Users, LogIn, LogOut,
  Mic2Icon
} from 'lucide-react';
import { logout } from '@/data-access/auth';

interface NavItemProps {
  to: string;
  icon: React.ElementType;
  label: string;
  isActive: boolean;
  badge?: number;
  isMobile?: boolean;
  onClick?: () => void;
}

interface NavigationItem {
  to: string;
  icon: React.ElementType;
  label: string;
  badge?: number;
}

interface MusicAcademySidebarProps {
  userDetails?: {
    name?: string;
    email?: string;
    [key: string]: any;
  };
}

const defaultNavigation: {
  category: string;
  items: NavigationItem[];
}[] = [
  {
    category: 'Main',
    items: [
      { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard' },
      { to: '/dashboard/courses', icon: BookOpen, label: 'My Courses' },
      { to: '/dashboard/sing-along', icon: Mic, label: 'Sing Along' },
    ],
  },
  {
    category: 'Learning',
    items: [
      { to: '/dashboard/assignments', icon: NotebookPen, label: 'Assignments' },
      { to: '/dashboard/feedback', icon: MessageSquare, label: 'Feedback' },
      { to: '/dashboard/support', icon: Users, label: 'Support' },
    ],
  },
  {
    category: 'User',
    items: [
      { to: '/dashboard/profile', icon: Users, label: 'Profile' },
      { to: '/dashboard/recordings', icon: Mic2Icon, label: 'Recordings' },
      { to: '/dashboard/certificates', icon: Award, label: 'Certificates' },
    ],
  },
];

const NavItem = React.memo(({
  to, icon: Icon, label, isActive, badge, isMobile, onClick
}: NavItemProps) => {
  const content = (
    <>
      <div className="flex-shrink-0">
        <Icon className={cn(
          "h-5 w-5 transition-all duration-200",
          isActive 
            ? "text-blue-600" 
            : "text-gray-600 group-hover:text-blue-500"
        )} />
      </div>

      <span className={cn(
        "ml-3 font-medium transition-all duration-200",
        isActive 
          ? "text-blue-600" 
          : "text-gray-700 group-hover:text-gray-900",
        isMobile ? "text-base" : "text-sm"
      )}>
        {label}
      </span>

      {badge && (
        <span className="ml-auto bg-blue-600 text-white text-xs font-medium px-2.5 py-1 rounded-full shadow-sm">
          {badge}
        </span>
      )}
    </>
  );

  const baseClasses = cn(
    "flex items-center rounded-xl transition-all duration-200 group relative",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
    isMobile ? "px-4 py-3.5 mx-3" : "px-4 py-3 mx-3",
    isActive 
      ? "bg-gradient-to-r from-blue-50 to-blue-100/50 text-blue-600 shadow-sm border border-blue-200/50" 
      : "text-gray-700 hover:bg-white hover:shadow-sm"
  );

  return (
    <Link
      href={to}
      onClick={onClick}
      className={baseClasses}
      aria-label={label}
      aria-current={isActive ? 'page' : undefined}
    >
      {content}
      {isActive && (
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-blue-600 rounded-l-full" />
      )}
    </Link>
  );
});

const UserProfile = React.memo(({ 
  userDetails, 
  isMobile 
}: {
  userDetails?: MusicAcademySidebarProps['userDetails'];
  isMobile?: boolean;
}) => {
  const initials = userDetails?.name
    ? userDetails.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    : 'U';

  return (
    <div className="p-4 border-t border-gray-100">
      <div className="flex items-center space-x-3">
        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-600 to-blue-700 flex-shrink-0 flex items-center justify-center text-white font-semibold text-sm shadow-md">
          {initials}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-semibold text-gray-900 truncate">
            {userDetails?.name || 'User'}
          </p>
          {userDetails?.email && (
            <p className="text-xs text-gray-500 truncate">{userDetails.email}</p>
          )}
        </div>
      </div>
    </div>
  );
});

const MusicAcademySidebar = ({ userDetails = {} }: MusicAcademySidebarProps) => {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const isUserLoggedIn = userDetails?.name || userDetails?.email;

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const checkScreenSize = () => {
        setIsMobile(window.innerWidth < 768);
      };
      
      checkScreenSize();
      window.addEventListener('resize', checkScreenSize);
      return () => window.removeEventListener('resize', checkScreenSize);
    }
  }, []);

  const closeMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(false);
  }, []);

  // Mobile Header
  if (isMobile) {
    return (
      <>
        <header className="fixed top-0 left-0 right-0 h-16 bg-white/95 backdrop-blur-sm border-b border-gray-200 flex items-center justify-between px-4 z-50 shadow-sm">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center shadow-md">
              <Music className="h-4 w-4 text-white" />
            </div>
            <span className="text-lg font-bold text-gray-900">Dhwanini</span>
          </div>
          
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-xl hover:bg-gray-100 transition-colors"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6 text-gray-600" />
            ) : (
              <Menu className="h-6 w-6 text-gray-600" />
            )}
          </button>
        </header>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <>
            <div 
              className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
              onClick={closeMobileMenu}
            />
            <div className="fixed top-16 left-0 right-0 bg-white/95 backdrop-blur-sm shadow-xl z-50 h-[calc(100vh-4rem)] overflow-y-auto border-t border-gray-200">
              <div className="py-6">
                {defaultNavigation.map((category) => (
                  <div key={category.category} className="mb-8">
                    <div className="px-6 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider">
                      {category.category}
                    </div>
                    <div className="space-y-2 mt-3">
                      {category.items.map((item) => (
                        <NavItem
                          key={item.to}
                          to={item.to}
                          icon={item.icon}
                          label={item.label}
                          isActive={pathname === item.to || pathname.startsWith(`${item.to}/`)}
                          badge={item.badge}
                          isMobile={true}
                          onClick={closeMobileMenu}
                        />
                      ))}
                    </div>
                  </div>
                ))}
                
                {!isUserLoggedIn && (
                  <div className="mb-8">
                    <div className="px-6 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider">
                      Account
                    </div>
                    <div className="mt-3">
                      <NavItem
                        to="/login"
                        icon={LogIn}
                        label="Login"
                        isActive={pathname === '/login'}
                        isMobile={true}
                        onClick={closeMobileMenu}
                      />
                    </div>
                  </div>
                )}
              </div>
              
              {isUserLoggedIn && (
                <div className="border-t border-gray-100">
                  <UserProfile userDetails={userDetails} isMobile={true} />
                  <div className="px-6 pb-6">
                    <button 
                      onClick={() => {
                        logout();
                        closeMobileMenu();
                      }}
                      className="flex items-center gap-3 w-full px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200"
                    >
                      <LogOut className="h-4 w-4" />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </>
    );
  }

  // Desktop Sidebar
  return (
    <aside
      className="fixed inset-y-0 left-0 flex flex-col z-40 w-72 bg-white border-r border-gray-200 shadow-sm"
      aria-label="Sidebar navigation"
    >
      {/* Header */}
      <div className="flex items-center h-16 px-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
        <div className="flex items-center gap-3">
          <div className="h-9 w-9 rounded-xl bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center shadow-lg">
            <Music className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900">Dhwanini</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-6 px-3">
        {defaultNavigation.map((category, index) => (
          <div key={category.category} className={cn("mb-8", index === 0 && "mb-6")}>
            <div className="px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider">
              {category.category}
            </div>
            <div className="space-y-1 mt-3">
              {category.items.map((item) => (
                <NavItem
                  key={item.to}
                  to={item.to}
                  icon={item.icon}
                  label={item.label}
                  isActive={pathname === item.to || pathname.startsWith(`${item.to}/`)}
                  badge={item.badge}
                />
              ))}
            </div>
          </div>
        ))}
        
        {!isUserLoggedIn && (
          <div className="mb-8">
            <div className="px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider">
              Account
            </div>
            <div className="space-y-1 mt-3">
              <NavItem
                to="/login"
                icon={LogIn}
                label="Login"
                isActive={pathname === '/login'}
              />
            </div>
          </div>
        )}
      </nav>

      {/* Footer */}
      {isUserLoggedIn && (
        <>
          <div className="px-6 pb-4">
            <button 
              onClick={logout}
              className="flex items-center gap-3 w-full px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200 group"
            >
              <LogOut className="h-4 w-4 group-hover:scale-110 transition-transform" />
              Logout
            </button>
          </div>
          
          <UserProfile userDetails={userDetails} />
        </>
      )}
    </aside>
  );
};

export default MusicAcademySidebar;