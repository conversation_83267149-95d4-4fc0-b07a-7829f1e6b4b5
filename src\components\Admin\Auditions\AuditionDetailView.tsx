"use client"

import { useState, useRef, useEffect, startTransition } from 'react';
import { useRouter } from 'next/navigation';
import { ApplicationWithDetails } from '@/types/auditions';
import { AuditionStatus, PaymentStatus } from '@prisma/client';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Music,
  DollarSign,
  Calendar,
  Clock,
  FileText,
  MessageSquare,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  Pause,
  Volume2,
  Download,
  Send,
  Star,
  GraduationCap,
  Link
} from 'lucide-react';
import { updateFeedbackStatus } from '@/data-access/auditions/auditions';

interface AuditionDetailViewProps {
  application: ApplicationWithDetails;
  audioUrl: string | null;
}

// Consistent date formatting function to prevent hydration mismatches
const formatDate = (date: Date | string): string => {
  const d = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC'
  };
  return d.toLocaleDateString('en-US', options);
};

const formatDateShort = (date: Date | string): string => {
  const d = new Date(date);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: 'UTC'
  };
  return d.toLocaleDateString('en-US', options);
};

export function AuditionDetailView({ application, audioUrl }: AuditionDetailViewProps) {
  const router = useRouter();
  const audioRef = useRef<HTMLAudioElement>(null);
  
  // Audio player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  
  // Form state
  const [feedback, setFeedback] = useState(application.feedback || '');
  const [selectedStatus, setSelectedStatus] = useState(application.auditionStatus);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Parse userDetails JSON safely
  const userDetails = application.userDetails as any || {};

  // Audio player functions
  const togglePlay = () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = Number(e.target.value);
    setCurrentTime(time);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const vol = Number(e.target.value);
    setVolume(vol);
    if (audioRef.current) {
      audioRef.current.volume = vol;
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Status badge styles
  const getStatusBadge = (status: AuditionStatus) => {
    const styles = {
      PENDING: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      SUBMITTED: 'bg-blue-100 text-blue-800 border-blue-200',
      UNDER_REVIEW: 'bg-purple-100 text-purple-800 border-purple-200',
      APPROVED: 'bg-green-100 text-green-800 border-green-200',
      REJECTED: 'bg-red-100 text-red-800 border-red-200'
    };
    
    return styles[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    const styles = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      COMPLETED: 'bg-green-100 text-green-800',
      FAILED: 'bg-red-100 text-red-800',
      REFUNDED: 'bg-orange-100 text-orange-800'
    };
    
    return styles[status] || 'bg-gray-100 text-gray-800';
  };

  const handleSubmitFeedback = async () => {
    setIsSubmitting(true);
    try {
      const { success, error } = await updateFeedbackStatus(application.id, selectedStatus, feedback);
      if (!success) {
        alert(error || 'Failed to update application');
      }
    } 
    catch (error) {
      alert(error || 'Failed to update application');
    }
    finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 text-gray-700">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Applications
          </button>
          
          <div className="flex items-center gap-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadge(application.auditionStatus)}`}>
              {application.auditionStatus.replace('_', ' ')}
            </span>
            {application.auditionPaymentStatus !== 'COMPLETED' && (
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusBadge(application.auditionPaymentStatus)}`}>
                Payment: {application.auditionPaymentStatus}
              </span>
            )}
            {application.enrollmentPaymentStatus !== 'COMPLETED' && (
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusBadge(application.enrollmentPaymentStatus)}`}>
                Payment: {application.enrollmentPaymentStatus}
              </span>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {application.user.name}
            </h1>
            <p className="text-gray-600 mb-4">
              Application for {application.course.name}
            </p>
            <div className="text-sm text-gray-500">
              Applied on {formatDate(application.createdAt)}
            </div>
          </div>
          
          <div className="flex justify-end">
            <div className="text-right">
              <div className="text-sm text-gray-500 mb-1">Application ID</div>
              <div className="font-mono text-sm bg-gray-100 px-3 py-1 rounded">
                {application.id.slice(-8)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          
          {/* Applicant Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <User className="w-5 h-5 text-blue-600" />
              Applicant Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <div>
                    <div className="text-sm text-gray-500">Email</div>
                    <div className="font-medium">{application.user.email}</div>
                  </div>
                </div>
                
                {application.user.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Phone</div>
                      <div className="font-medium">{application.user.phone}</div>
                    </div>
                  </div>
                )}
                
                {application.user.country && (
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Country</div>
                      <div className="font-medium">{application.user.country}</div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="space-y-4">
                {userDetails.experience && (
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Musical Experience</div>
                    <div className="font-medium">{userDetails.experience}</div>
                  </div>
                )}
                
                {userDetails.motivation && (
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Motivation</div>
                    <div className="font-medium">{userDetails.motivation}</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Q&A Responses */}
          {userDetails && Object.keys(userDetails).length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-green-600" />
                Application Responses
              </h2>
              
              <div className="space-y-4">
                {Object.entries(userDetails).map(([key, value], index) => {
                  if (!value || typeof value !== 'string') return null;
                  
                  const formattedKey = key
                    .replace(/([A-Z])/g, ' $1')
                    .replace(/^./, str => str.toUpperCase());
                  
                  return (
                    <div key={index} className="border-l-4 border-blue-200 pl-4">
                      <div className="text-sm font-medium text-gray-700 mb-1">
                        {formattedKey}
                      </div>
                      <div className="text-gray-900">
                        {value}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Audio Submission */}
          {application.audioPath && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Music className="w-5 h-5 text-purple-600" />
                Audio Submission
              </h2>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <audio
                  ref={audioRef}
                  src={audioUrl || application.audioPath}
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  onEnded={() => setIsPlaying(false)}
                  className="hidden"
                />
                
                <div className="flex items-center gap-4 mb-4">
                  <button
                    onClick={togglePlay}
                    className="flex items-center justify-center w-12 h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full transition-colors"
                  >
                    {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5 ml-1" />}
                  </button>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-1">
                      <span>{formatTime(currentTime)}</span>
                      <span>{formatTime(duration)}</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max={duration || 0}
                      value={currentTime}
                      onChange={handleSeek}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Volume2 className="w-4 h-4 text-gray-400" />
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={volume}
                      onChange={handleVolumeChange}
                      className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                  
                  {audioUrl && (
                    <a
                      href={audioUrl}
                      download={audioUrl}
                      className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                      title="Download audio"
                    >
                      <Download className="w-4 h-4" />
                    </a>
                  )}
                </div>
                
                <div className="text-sm text-gray-600">
                  {application.audioPath || 'Audition recording'}
                </div>
              </div>
            </div>
          )}

          {/* Current Feedback */}
          {application.feedback && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <FileText className="w-5 h-5 text-orange-600" />
                Previous Feedback
              </h2>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <p className="text-gray-900">{application.feedback}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          
          {/* Course Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <GraduationCap className="w-5 h-5 text-blue-600" />
              Course Details
            </h3>
            
            <div className="space-y-3">
              <div>
                <div className="text-sm text-gray-500">Course</div>
                <div className="font-medium">{application.course.name}</div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Level</div>
                <div className="font-medium">{application.course.level}</div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Course Price</div>
                <div className="font-medium">
                  {application.course.currency} {application.course.price}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Audition Fee</div>
                <div className="font-medium">
                  {application.course.currency} {application.course.auditionFee}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-gray-500">Instructor</div>
                <div className="font-medium">{application.course.instructorEmail}</div>
              </div>
            </div>
          </div>

          {/* Admin Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-600" />
              Admin Actions
            </h3>
            
            <div className="space-y-4">
              {/* Status Update */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Update Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value as AuditionStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="PENDING">Pending</option>
                  <option value="SUBMITTED">Submitted</option>
                  <option value="UNDER_REVIEW">Under Review</option>
                  <option value="APPROVED">Approved</option>
                  <option value="REJECTED">Rejected</option>
                </select>
              </div>
              
              {/* Feedback */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Feedback (Optional)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="Provide feedback to the applicant..."
                />
              </div>
              
              {/* Submit Button */}
              <button
                onClick={handleSubmitFeedback}
                disabled={isSubmitting}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors font-medium"
              >
                <Send className="w-4 h-4" />
                {isSubmitting ? 'Updating...' : 'Update Application'}
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Timeline</h3>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <div className="text-sm font-medium">Applied</div>
                  <div className="text-xs text-gray-500">
                    {formatDateShort(application.createdAt)}
                  </div>
                </div>
              </div>
              
              {application.updatedAt !== application.createdAt && (
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium">Last Updated</div>
                    <div className="text-xs text-gray-500">
                      {formatDateShort(application.updatedAt)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Custom CSS for sliders */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  );
} 