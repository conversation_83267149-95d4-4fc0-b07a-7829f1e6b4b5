'use server'

import { prisma } from '@/utils/prisma/prisma'
import { getUserDetails } from '../auth'
import { ActionResult, handleError } from '../helpers'
import { UserAssignment, AssignmentStats } from '@/types/assignment'
import { Assignment, SubmissionStatus } from '@prisma/client'

// TODO: Add overdues to the user using cronjobs


export async function getUserAssignments(email: string): Promise<ActionResult<UserAssignment[]>> {
  
  try {

    const user = await prisma.user.findUnique({
      where: {
        email: email
      }
    })
    if(!user) {
      return { success: false, error: 'User not found' }
    }

    const enrollments = await prisma.enrollment.findMany({
      where: { userId: user.id },
      include: {
        course: {
          include: {
            modules: {
              include: {
                assignments: {
                  include: {
                    submissions: {
                      where: { userId: user.id }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    const assignments: UserAssignment[] = []
    const now = new Date()

    for (const enrollment of enrollments) {
      for (const module of enrollment.course.modules) {
        for (const assignment of module.assignments) {
          const submission = assignment.submissions[0] || null
          const deadline = new Date(assignment.deadline)
          const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
          const isOverdue = deadline < now && !submission

          assignments.push({
            ...assignment,
            module: {
              id: module.id,
              title: module.title,
              course: {
                id: enrollment.course.id,
                name: enrollment.course.name
              }
            },
            submission,
            isOverdue,
            daysUntilDeadline
          })
        }
      }
    }

    // Sort by deadline (closest first)
    assignments.sort((a, b) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())

    return { success: true, data: assignments }
  } catch (error) {
    return handleError(error, 'fetch user assignments')
  }
}

export async function getAssignmentStats(email: string): Promise<ActionResult<AssignmentStats>> {
  
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email
      }
    })
    if(!user) {
      return { success: false, error: 'User not found' }
    }

    const assignmentsResult = await getUserAssignments(user.email)
    if (!assignmentsResult.success || !assignmentsResult.data) {
      return { success: false, error: 'Failed to fetch assignments' }
    }

    const assignments = assignmentsResult.data
    const now = new Date()

    const stats: AssignmentStats = {
      total: assignments.length,
      submitted: assignments.filter(a => a.submission).length,
      graded: assignments.filter(a => a.submission?.status === SubmissionStatus.GRADED).length,
      overdue: assignments.filter(a => new Date(a.deadline) < now && !a.submission).length,
      pending: assignments.filter(a => !a.submission && new Date(a.deadline) >= now).length
    }

    return { success: true, data: stats }
  } catch (error) {
    return handleError(error, 'fetch assignment stats')
  }
}

export async function getAssignmentById(assignmentId: string, email: string): Promise<ActionResult<UserAssignment | null>> {
  
  try {
    const user = await prisma.user.findUnique({
      where: {
        email: email
      }
    })
    if(!user) {
      return { success: false, error: 'User not found' }
    }

    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        module: {
          include: {
            course: true
          }
        },
        submissions: {
          where: { userId: user.id }
        }
      }
    })

    if (!assignment) {
      return { success: true, data: null }
    }

    // Check if user is enrolled in the course
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        userId_courseId: {
          userId: user.id,
          courseId: assignment.module.course.id
        }
      }
    })

    if (!enrollment) {
      return { success: false, error: 'You are not enrolled in this course' }
    }

    const submission = assignment.submissions[0] || null
    const now = new Date()
    const deadline = new Date(assignment.deadline)
    const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    const isOverdue = deadline < now && !submission

    const userAssignment: UserAssignment = {
      ...assignment,
      module: {
        id: assignment.module.id,
        title: assignment.module.title,
        course: {
          id: assignment.module.course.id,
          name: assignment.module.course.name
        }
      },
      submission,
      isOverdue,
      daysUntilDeadline
    }

    return { success: true, data: userAssignment }
  } catch (error) {
    return handleError(error, 'fetch assignment')
  }
}


export async function getSubmittedAssignments(email: string): Promise<ActionResult<UserAssignment[]>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    })
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Directly query assignment submissions for the user
    const submissions = await prisma.assignmentSubmission.findMany({
      where: { userId: user.id },
      include: {
        assignment: {
          include: {
            module: {
              include: {
                course: true
              }
            }
          }
        }
      },
      orderBy: { submittedAt: 'desc' }
    })

    const submittedAssignments: UserAssignment[] = submissions.map(submission => {
      const assignment = submission.assignment
      const deadline = new Date(assignment.deadline)
      const now = new Date()
      const daysUntilDeadline = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      const isOverdue = deadline < now

      return {
        ...assignment,
        module: {
          id: assignment.module.id,
          title: assignment.module.title,
          course: {
            id: assignment.module.course.id,
            name: assignment.module.course.name
          }
        },
        submission,
        isOverdue,
        daysUntilDeadline
      }
    })

    return { success: true, data: submittedAssignments }
  } catch (error) {
    return handleError(error, 'fetch submitted assignments')
  }
} 
