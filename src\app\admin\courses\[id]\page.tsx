import { getCourseByIdAction } from "@/data-access/course/admin-course"
import { notFound } from "next/navigation"
import CourseEditor from "@/components/Admin/Course/CourseEditor"

interface CoursePageProps {
  params: Promise<{ id: string }>
}

export default async function CoursePage({ params }: CoursePageProps) {
  const { id } = await params


  const result = await getCourseByIdAction(id)

  if (!result.success || !result.data) {
    notFound()
  }

  return <CourseEditor course={result.data} />
} 