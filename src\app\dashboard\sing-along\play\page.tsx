import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { SingAlongPlayer } from '@/components/SingAlong/SongPlayer/SingAlongPlayer';
import { SongSelectorClient } from '@/components/SingAlong/SongSelector/SongSelectorClient';
import { ChooseDifferentSongButton } from '@/components/SingAlong/ChooseDifferentSongButton';
import { getAudioUrl, getAvailableSongs, getSongByTitle } from '@/data-access/sing-along/songs';
import { Song } from '@/types/sing-along';

interface PlayPageProps {
  searchParams: Promise<{
    song?: string;
  }>
}

export default async function PlayPage({ searchParams }: PlayPageProps) {
  const selectedSongTitle = (await searchParams).song;
  let selectedSong: Song | null = null;
  let songError = null;
  let audioUrl = null;

  if (selectedSongTitle) {
    const { song, error } = await getSongByTitle(selectedSongTitle);
    const { audioUrl: audioUrlResponse, error: audioError } = await getAudioUrl(selectedSongTitle);
    console.log("audioUrlResponse", audioUrlResponse)
    if(audioError) {
      songError = audioError;
    }
    audioUrl = audioUrlResponse;
    selectedSong = song;
    songError = error;
  }
  const { songs: availableSongs, error: songsError } = await getAvailableSongs();


  return (
    <div className="min-h-screen w-full bg-white">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        {/* Header with Back Button */}
        <div className="flex items-center justify-between mb-8">
          <Link
            href="/dashboard/sing-along"
            className="group inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors duration-300"
          >
            <ArrowLeft size={20} className="mr-2 transition-transform duration-300 group-hover:-translate-x-1" />
            Back to Sing Along Studio
          </Link>

          {/* Show "Choose Different Song" when a song is selected */}
          {selectedSong && (
            <ChooseDifferentSongButton />
          )}
        </div>

        {/* Error Display */}
        {songError && (
          <div className="mb-8 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">Error loading song: {songError}</p>
          </div>
        )}

        {songsError && (
          <div className="mb-8 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">Error loading songs: {songsError}</p>
          </div>
        )}

        {/* Conditional Rendering: Song Selection or Player */}
        {!selectedSong ? (
          <>
            {/* Song Selection */}
            <SongSelectorClient songs={availableSongs} />

            {/* Instructions for Song Selection */}
            <div className="mt-12 bg-white rounded-xl shadow-xl border border-blue-100 p-8">
              <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">Getting Started</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">1</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Browse & Search</h3>
                  <p className="text-sm text-slate-600">Explore our collection of classical and traditional songs. Use filters to find songs by genre or difficulty level.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">2</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Select Your Song</h3>
                  <p className="text-sm text-slate-600">Click on any song card to start practicing. Each song comes with synchronized lyrics and difficulty ratings.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">3</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Practice & Record</h3>
                  <p className="text-sm text-slate-600">Follow along with lyrics, adjust playback settings, and record your performance for improvement.</p>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Song Player */}
            <SingAlongPlayer song={selectedSong} audioUrl={audioUrl} />

            {/* Instructions for Playing */}
            <div className="mt-12 bg-white rounded-xl shadow-xl border border-blue-100 p-8">
              <h2 className="text-2xl font-bold text-slate-800 mb-6 text-center">Now Playing: {selectedSong.title}</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">1</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Listen & Follow</h3>
                  <p className="text-sm text-slate-600">Play the song and follow along with the synchronized lyrics. The current line is highlighted and shows progress.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">2</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Practice Along</h3>
                  <p className="text-sm text-slate-600">Sing along with the original track. Use the volume control to adjust the backing track level.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">3</div>
                  <h3 className="font-semibold text-slate-800 mb-2">Record & Save</h3>
                  <p className="text-sm text-slate-600">Toggle the recorder to capture your performance. Your recording will be saved for future reference.</p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Footer */}
        <footer className="text-center py-10 mt-12 border-t border-slate-200">
          <p className="text-sm text-slate-500">&copy; {new Date().getFullYear()} Dhwanini Academy. All rights reserved.</p>
          <p className="text-xs text-slate-400 mt-1">Nurturing Classical Arts</p>
        </footer>
      </main>
    </div>
      );
} 