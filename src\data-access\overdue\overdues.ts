"use server"

import { prisma } from "@/utils/prisma/prisma"

export const overdueUpdator = async () => {
  const users = await prisma.user.findMany({
    where: {
      enrollments: {
        some: {}
      }
    },
    include: {
      assignments: {
        include: {
          assignment: true
        }
      },
      feedbackSessions: true
    }
  });

  const assignmentOverdues = await prisma.assignment.findMany({
    where: {
      deadline: {
        lt: new Date()
      }
    }
  });

  const overdueMap = new Map<string, number>();

  for (const user of users) {
    // 1. Check for unsubmitted or late-submitted assignments
    for (const overdueAssignment of assignmentOverdues) {
      const submission = user.assignments.find(
        (a) => a.assignment.id === overdueAssignment.id
      );

      if (!submission) {
        // Not submitted
        overdueMap.set(user.id, (overdueMap.get(user.id) || 0) + 1);
      } else if (
        submission.submittedAt &&
        submission.submittedAt > overdueAssignment.deadline
      ) {
        // Late submission
        overdueMap.set(user.id, (overdueMap.get(user.id) || 0) + 1);
      }
    }

    // 2. Check for missing feedback for submitted assignments
    for (const submission of user.assignments) {
      const hasFeedback = user.feedbackSessions.some(
        (feedback) => feedback.assignmentId === submission.assignment.id
      );

      if (!hasFeedback) {
        overdueMap.set(user.id, (overdueMap.get(user.id) || 0) + 1);
      }
    }
  }

  // 3. Update users
  const updates = Array.from(overdueMap.entries()).map(([userId, overDues]) =>
    prisma.user.update({
      where: { id: userId },
      data: {
        overDues,
        isActive: overDues > 3 ? false : true
      }
    })
  );

  await prisma.$transaction(updates);

  return {
    success: true,
    message: "Overdues updated successfully"
  };
};
