'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { z } from 'zod'
import { createClient } from '@/utils/supabase/server'
import { prisma } from '@/utils/prisma/prisma'
import { User } from '@prisma/client'

interface UserRole {
  role: "ADMIN" | "USER" | "GUEST"
}

export async function login(email: string, password: string) {
  const supabase = await createClient()

  const loginSchema = z.object({
    email: z.string().email(),
    password: z.string().min(8),
  })

  const { error: loginError } = loginSchema.safeParse({ email, password })

  if (loginError) {
    return { error: loginError.message }
  }

  const data = {
    email: email,
    password: password,
  }

  const { data: { user }, error } = await supabase.auth.signInWithPassword(data)

  if (error) {
    return { error: error.message }
  }

  if (user?.user_metadata.role === 'GUEST') {
    await supabase.auth.signOut()
    return { error: 'You are not authorized to access this application' }
  }

  revalidatePath('/', 'layout')
  redirect('/dashboard')
}

export async function loginWithGoogle() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`,
    },
  })

  if (error) {
    return { error: error.message }
  }

  redirect(data.url)
}


export async function logout() {
  const supabase = await createClient();
  await supabase.auth.signOut();
  revalidatePath('/', 'layout');
  redirect('/login');
}

export async function getUser() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  return user;
}

export async function getUserRole() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  return user?.user_metadata.role;
}

export async function getUserDetails(): Promise<{
  user: User | null,
  error: string | null
}> {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    return null;
  }
  try {
    const User = await prisma.user.findUnique({
      where: {
        email: user.email as string
      }
    })
    return { user: User, error: null };
  } 
  catch (error) {
    return { user: null, error: 'Error fetching user details' };
  }
}

export async function requireRole(role: string[]): Promise<{
  success: boolean;
  error: string | null;
}> {
  return { success: true, error: null };
  const userRole = await getUserRole();
  if (!role.includes(userRole)) {
    return { success: false, error: 'Insufficient permissions' };
  }
  return { success: true, error: null };
}