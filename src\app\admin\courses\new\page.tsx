import { redirect } from "next/navigation"
import { createCourseAction } from "@/data-access/course/admin-course"

import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { CourseForm } from "@/components/Admin/Course/CourseForm"

export default function NewCoursePage() {
  async function handleCreateCourse(formData: FormData) {
    "use server"
    
    const result = await createCourseAction(formData)
    
    if (result.success && result.data?.courseId) {
      redirect(`/admin/courses/${result.data.courseId}`)
    } else {
      // Handle error - you could use cookies to pass error state
      throw new Error(result.error || 'Failed to create course')
    }
  }

  return (
    <div className="min-h-screen bg-white text-gray-700">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href="/admin/courses"
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Create New Course</h1>
              <p className="text-gray-600 mt-1">Set up a new course with basic information</p>
            </div>
          </div>
        </div>

        {/* Course Form */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <CourseForm action={handleCreateCourse} />
        </div>
      </div>
    </div>
  )
} 