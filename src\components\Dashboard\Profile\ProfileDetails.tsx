'use client'
import { UserProfile } from '@/types/profile'
import { useState, useEffect } from 'react'
import { CourseLevel } from '@prisma/client'

interface ProfileDetailsProps {
  user: UserProfile
  onUpdate?: (updates: Partial<UserProfile>) => Promise<void>
}

export function ProfileDetails({ user, onUpdate }: ProfileDetailsProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    country: '',
    vocalExperience: 'BEGINNER' as CourseLevel,
    preferredTimings: '',
    interests: ''
  })


  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date))
  }

  const getRoleBadge = () => {
    switch (user?.role) {
      case 'STUDENT':
        return 'bg-blue-100 text-blue-800'
      case 'COURSE_MANAGER':
        return 'bg-purple-100 text-purple-800'
      case 'ADMISSION_SUPPORT':
        return 'bg-green-100 text-green-800'
      case 'SUPER_ADMIN':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusBadge = () => {
    return user?.isActive 
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800'
  }

  const getExperienceBadge = (level: CourseLevel) => {
    switch (level) {
      case 'BEGINNER':
        return 'bg-green-100 text-green-800'
      case 'INTERMEDIATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'ADVANCED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatExperienceLevel = (level: CourseLevel) => {
    return level.charAt(0).toUpperCase() + level.slice(1).toLowerCase()
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      errors.name = 'Full name is required'
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Full name must be at least 2 characters'
    }
    
    if (formData.phone && formData.phone.trim()) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/
      if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
        errors.phone = 'Please enter a valid phone number'
      }
    }
    
    if (formData.country && formData.country.trim().length < 2) {
      errors.country = 'Country name must be at least 2 characters'
    }

    if (formData.interests.trim()) {
      const interestsList = formData.interests.split(',').map(i => i.trim()).filter(Boolean)
      if (interestsList.some(interest => interest.length < 2)) {
        errors.interests = 'Each interest must be at least 2 characters'
      }
    }
    
    setFieldErrors(errors)
    return Object.keys(errors).length === 0
  }

  const clearErrors = () => {
    setError(null)
    setFieldErrors({})
  }

  const handleInputChange = (field: string, value: string | CourseLevel) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear field-specific error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
    
    // Clear general error when user makes changes
    if (error) {
      setError(null)
    }
  }

  const handleSave = async () => {
    if (!onUpdate) {
      setError('Update function not available')
      return
    }
    
    clearErrors()
    
    if (!validateForm()) {
      return
    }
    
    setIsLoading(true)
    try {
      const interestsList = formData.interests.trim() 
        ? formData.interests.split(',').map(i => i.trim()).filter(Boolean)
        : []

      await onUpdate({
        name: formData.name.trim(),
        phone: formData.phone.trim() || null,
        country: formData.country.trim() || null,
        vocalExperience: formData.vocalExperience,
        preferredTimings: formData.preferredTimings.trim() || null,
        interests: interestsList
      })
      setIsEditing(false)
      clearErrors()
    } catch (error) {
      console.error('Failed to update profile:', error)
      if (error instanceof Error) {
        setError(error.message || 'Failed to update profile')
      } else {
        setError('Failed to update profile. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (user) {
      setFormData({
        name: user.name || '',
        phone: user.phone || '',
        country: user.country || '',
        vocalExperience: user.vocalExperience || 'BEGINNER',
        preferredTimings: user.preferredTimings || '',
        interests: user.interests?.join(', ') || ''
      })
    }
    setIsEditing(false)
    clearErrors()
  }

  const handleEdit = () => {
    setIsEditing(true)
    clearErrors()
  }

  const handleRetry = () => {
    clearErrors()
    handleSave()
  }

  // Show loading state while user data is not available
  if (!user) {
    return (
      <div className="bg-white rounded-lg text-gray-700 border border-gray-200 p-4 sm:p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {[...Array(6)].map((_, i) => (
                <div key={i}>
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-6 bg-gray-100 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg text-gray-700 border border-gray-200 p-4 sm:p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
          Profile Details
        </h2>
        <div className="flex gap-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getRoleBadge()}`}>
            {user.role?.replace('_', ' ')}
          </span>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadge()}`}>
            {user.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">Error updating profile</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <div className="mt-2 flex gap-2">
                <button
                  onClick={handleRetry}
                  disabled={isLoading}
                  className="text-sm text-red-800 underline hover:no-underline disabled:opacity-50"
                >
                  Try Again
                </button>
                <button
                  onClick={() => setError(null)}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-600">Full Name</label>
            {isEditing ? (
              <div>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none text-sm ${
                    fieldErrors.name 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter your full name"
                />
                {fieldErrors.name && (
                  <p className="text-sm text-red-600 mt-1">{fieldErrors.name}</p>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-900 mt-1 break-words">{user.name}</p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Email Address</label>
            <p className="text-sm text-gray-900 mt-1 break-words">{user.email}</p>
            {isEditing && (
              <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Phone Number</label>
            {isEditing ? (
              <div>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none text-sm ${
                    fieldErrors.phone 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter your phone number"
                />
                {fieldErrors.phone && (
                  <p className="text-sm text-red-600 mt-1">{fieldErrors.phone}</p>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-900 mt-1">
                {user.phone || 'Not provided'}
              </p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Country</label>
            {isEditing ? (
              <div>
                <input
                  type="text"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none text-sm ${
                    fieldErrors.country 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter your country"
                />
                {fieldErrors.country && (
                  <p className="text-sm text-red-600 mt-1">{fieldErrors.country}</p>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-900 mt-1">
                {user.country || 'Not provided'}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium text-gray-600">Vocal Experience</label>
            {isEditing ? (
              <select
                value={formData.vocalExperience}
                onChange={(e) => handleInputChange('vocalExperience', e.target.value as CourseLevel)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value={CourseLevel.BEGINNER}>Beginner</option>
                <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
                <option value={CourseLevel.ADVANCED}>Advanced</option>
              </select>
            ) : (
              <div className="mt-1">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getExperienceBadge(user.vocalExperience || 'BEGINNER')}`}>
                  {formatExperienceLevel(user.vocalExperience || 'BEGINNER')}
                </span>
              </div>
            )}
          </div>

          <div>
            <label className="text-sm font-medium text-gray-600">Preferred Timings</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.preferredTimings}
                onChange={(e) => handleInputChange('preferredTimings', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="e.g., Evenings 6-8 PM, Weekends"
              />
            ) : (
              <p className="text-sm text-gray-900 mt-1">
                {user.preferredTimings || 'Not provided'}
              </p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Account Type</label>
            <p className="text-sm text-gray-900 mt-1">
              {user.role?.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Member Since</label>
            <p className="text-sm text-gray-900 mt-1">
              {user.createdAt ? formatDate(user.createdAt) : 'N/A'}
            </p>
          </div>
        </div>

        {/* Interests Section */}
        <div className="pt-4 border-t border-gray-100">
          <div>
            <label className="text-sm font-medium text-gray-600">Musical Interests</label>
            {isEditing ? (
              <div>
                <textarea
                  value={formData.interests}
                  onChange={(e) => handleInputChange('interests', e.target.value)}
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none text-sm ${
                    fieldErrors.interests 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  rows={3}
                  placeholder="Enter your interests separated by commas (e.g., Classical, Jazz, Pop)"
                />
                {fieldErrors.interests && (
                  <p className="text-sm text-red-600 mt-1">{fieldErrors.interests}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">Separate multiple interests with commas</p>
              </div>
            ) : (
              <div className="mt-1">
                {user.interests && user.interests.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {user.interests.map((interest, index) => (
                      <span 
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium"
                      >
                        {interest}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-900">No interests added</p>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="pt-4 border-t border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Profile ID: {user.id}
            </div>
            
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <button
                    onClick={handleCancel}
                    disabled={isLoading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isLoading || Object.keys(fieldErrors).length > 0}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isLoading && (
                      <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    Save Changes
                  </button>
                </>
              ) : (
                <button
                  onClick={handleEdit}
                  className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Edit Profile
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 