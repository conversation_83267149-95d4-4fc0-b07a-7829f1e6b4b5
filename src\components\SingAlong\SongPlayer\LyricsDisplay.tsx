import { useEffect, useRef, useState } from 'react';
import { Music } from 'lucide-react';
import { LyricsLine } from '@/types/sing-along';


interface LyricsDisplayProps {
  lyrics: LyricsLine[];
  currentTime: number;
}

export function LyricsDisplay({
  lyrics,
  currentTime,
}: LyricsDisplayProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [prevIndex, setPrevIndex] = useState(-1);
  
  // Find current line index
  const currentIndex = lyrics.findIndex((line) => line.time > currentTime) - 1;
  const activeIndex = currentIndex >= 0 ? currentIndex : 0;
  
  // Get the subset of lyrics to display (4 before and 4 after current line)
  const visibleLines = lyrics.slice(
    Math.max(0, activeIndex - 4),
    Math.min(lyrics.length, activeIndex + 5)
  );

  // Calculate offset to know which line in visibleLines is current
  const currentVisibleIndex = activeIndex - Math.max(0, activeIndex - 4);

  // Scroll to center when active line changes
  useEffect(() => {
    if (containerRef.current && prevIndex !== activeIndex) {
      setPrevIndex(activeIndex);
      
      const children = containerRef.current.children;
      if (children[currentVisibleIndex]) {
        children[currentVisibleIndex].scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }, [activeIndex, currentVisibleIndex, prevIndex]);

  // Calculate progress percentage for active line highlighting
  const getProgressPercentage = () => {
    if (activeIndex >= lyrics.length - 1) return 100;
    
    const currentLine = lyrics[activeIndex];
    const nextLine = lyrics[activeIndex + 1];
    
    const lineDuration = nextLine.time - currentLine.time;
    const elapsed = currentTime - currentLine.time;
    
    return Math.min(100, Math.max(0, (elapsed / lineDuration) * 100));
  };

  const progressPercentage = getProgressPercentage();

  if(lyrics.length === 0) {
    return (
      <div className="w-full max-w-3xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6">
            <div className="flex items-center justify-center">
              <Music className="h-6 w-6 text-white mr-3" />
              <h2 className="text-xl font-bold text-white">Lyrics</h2>
            </div>
          </div>
          
          {/* Empty state */}
          <div className="p-12 text-center">
            <div className="w-20 h-20 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Music className="h-10 w-10 text-slate-400" />
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">No Lyrics Available</h3>
            <p className="text-slate-600">Lyrics will appear here when you play a song with lyrics data.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6">
          <div className="flex items-center justify-center">
            <Music className="h-6 w-6 text-white mr-3" />
            <h2 className="text-xl font-bold text-white">Lyrics</h2>
          </div>
        </div>
        
        {/* Lyrics container */}
        <div className="relative bg-white">
          <div 
            ref={containerRef}
            className="h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-transparent"
          >
            <div className="px-8 py-6 space-y-4">
              {visibleLines.map((line, index) => {
                const isActive = index === currentVisibleIndex;
                const isPrevious = index < currentVisibleIndex;
                const isNext = index > currentVisibleIndex;
                
                return (
                  <div
                    key={`${line.time}-${index}`}
                    className={`transition-all duration-500 ease-out transform ${
                      isActive 
                        ? 'scale-105' 
                        : 'scale-100'
                    }`}
                  >
                    {isActive ? (
                      <div className="relative">
                        {/* Active line background */}
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl -m-4 p-4"></div>
                        
                        {/* Active line content */}
                        <div className="relative z-10 text-center p-4">
                          <div className="text-2xl md:text-3xl font-bold text-slate-800 leading-relaxed mb-3">
                            {line.text}
                          </div>
                          
                          {/* Progress bar */}
                          <div className="w-full max-w-md mx-auto">
                            <div className="h-1 bg-slate-200 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full transition-all duration-300 ease-out"
                                style={{ width: `${progressPercentage}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className={`text-center py-2 transition-all duration-300 ${
                        isPrevious 
                          ? 'text-slate-500' 
                          : isNext 
                            ? 'text-slate-400' 
                            : 'text-slate-300'
                      }`}>
                        <div className={`${
                          Math.abs(index - currentVisibleIndex) === 1 
                            ? 'text-lg md:text-xl font-medium' 
                            : 'text-base md:text-lg'
                        } leading-relaxed`}>
                          {line.text}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* Gradient overlays for smooth scroll effect */}
          <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-slate-50 via-slate-50/80 to-transparent pointer-events-none z-10"></div>
          <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-slate-50 via-slate-50/80 to-transparent pointer-events-none z-10"></div>
        </div>
        
        {/* Footer info */}
        <div className="bg-slate-100 px-8 py-4 border-t border-slate-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-600">
              Line {activeIndex + 1} of {lyrics.length}
            </span>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-white0 rounded-full animate-pulse"></div>
              <span className="text-slate-600">Following lyrics</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 