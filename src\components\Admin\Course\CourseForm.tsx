import { CourseLevel } from "@prisma/client"
import { CourseDetails } from "@/types/course"
import { Save } from "lucide-react"

interface CourseFormProps {
  action: (formData: FormData) => Promise<void>
  course?: CourseDetails
  submitLabel?: string
}

export function CourseForm({ action, course, submitLabel = "Create Course" }: CourseFormProps) {
  return (
    <form action={action} className="p-8">
      <div className="space-y-8">
        {/* Basic Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Course Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                defaultValue={course?.name || ""}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter course name"
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              defaultValue={course?.description || ""}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
              placeholder="Describe your course..."
              required
            />
          </div>
        </div>

        {/* Course Settings */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Course Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-2">
                Level
              </label>
              <select
                id="level"
                name="level"
                defaultValue={course?.level || CourseLevel.BEGINNER}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value={CourseLevel.BEGINNER}>Beginner</option>
                <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
                <option value={CourseLevel.ADVANCED}>Advanced</option>
              </select>
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                Price
              </label>
              <input
                type="number"
                id="price"
                name="price"
                defaultValue={course?.price || 0}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label htmlFor="auditionFee" className="block text-sm font-medium text-gray-700 mb-2">
                Audition Fee
              </label>
              <input
                type="number"
                id="auditionFee"
                name="auditionFee"
                defaultValue={course?.auditionFee || 0}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <select
                id="currency"
                name="currency"
                defaultValue={course?.currency || "USD"}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="USD">USD</option>
                <option value="INR">INR</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
          </div>
        </div>

        {/* Instructor Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Instructor Information</h2>
          <div>
            <label htmlFor="instructorEmail" className="block text-sm font-medium text-gray-700 mb-2">
              Instructor Email *
            </label>
            <input
              type="email"
              id="instructorEmail"
              name="instructorEmail"
              defaultValue={course?.instructorEmail || ""}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="<EMAIL>"
              required
            />
          </div>
        </div>

        {/* Publishing Options */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Publishing Options</h2>
          <div className="flex items-start gap-3 p-4 bg-white rounded-lg border border-blue-200">
            <input
              type="checkbox"
              id="isPublished"
              name="isPublished"
              value="true"
              defaultChecked={course?.isPublished || false}
              className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <div>
              <label htmlFor="isPublished" className="text-sm font-medium text-gray-900 cursor-pointer">
                Publish course immediately
              </label>
              <p className="text-sm text-gray-600 mt-1">
                Published courses will be visible to students. You can change this later.
              </p>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg flex items-center gap-2 transition-colors font-medium shadow-sm"
          >
            <Save className="w-4 h-4" />
            {submitLabel}
          </button>
        </div>
      </div>
    </form>
  )
} 