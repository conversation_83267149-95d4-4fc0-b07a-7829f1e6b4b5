"use client"
import { getAvailableSongs } from '@/data-access/sing-along/songs';
import { CourseLevel, Music } from '@prisma/client';
import { Search, Filter, Play, Clock, Star, Heart, Music4 } from 'lucide-react';
import { useEffect, useState } from 'react';



const genres = ["All", "Classical", "Semi-Classical", "Devotional", "Patriotic", "Folk"];
const difficulties = ["All", "Beginner", "Intermediate", "Advanced"];

export default function SongLibrary() {
  const [songs, setSongs] = useState<Music[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedGenre, setSelectedGenre] = useState("All");
  const [selectedDifficulty, setSelectedDifficulty] = useState("All");
  const [showFilters, setShowFilters] = useState(false);


  useEffect(() => {
    const fetchSongs = async () => {
      const {songs, error} = await getAvailableSongs();
      if (error) {
        console.error(error);
      }
      setSongs(songs);
    };
    fetchSongs();
  }, []);

  const filteredSongs = songs.filter(song => {
    const matchesSearch = song.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === "All" || song.genre === selectedGenre;
    const matchesDifficulty = selectedDifficulty === "All" || song.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesGenre && matchesDifficulty;
  });

  return (
    <div className="bg-white rounded-xl shadow-xl border border-blue-100 text-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Music4 size={24} className="mr-3 text-blue-600" />
          <h3 className="text-2xl font-bold text-slate-800">Song Library</h3>
        </div>
        <span className="inline-block bg-blue-100 text-blue-700 text-xs font-semibold px-2.5 py-1 rounded-full">
          {filteredSongs.length} songs
        </span>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-3 h-5 w-5 text-slate-400" />
          <input
            type="text"
            placeholder="Search songs, artists..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 px-4 py-2 text-slate-600 border border-slate-200 rounded-lg hover:bg-white hover:border-blue-300 transition-colors"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white rounded-lg border border-blue-100">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Genre</label>
              <select
                value={selectedGenre}
                onChange={(e) => setSelectedGenre(e.target.value)}
                className="w-full px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {genres.map(genre => (
                  <option key={genre} value={genre}>{genre}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Difficulty</label>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="w-full px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {difficulties.map(difficulty => (
                  <option key={difficulty} value={difficulty}>{difficulty}</option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Songs Grid */}
      <div className="space-y-3">
        {filteredSongs.map(song => (
          <div key={song.id} className="group bg-white rounded-lg p-4 hover:bg-white hover:border-blue-200 transition-all duration-300 cursor-pointer border border-transparent">
            <div className="flex items-center space-x-4">
              <div className="relative w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center overflow-hidden shadow-md">
                <Play className="h-6 w-6 text-white opacity-80 group-hover:opacity-100 transition-opacity" />
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-slate-800 truncate">{song.title}</h4>
                <p className="text-xs text-slate-500 truncate">{song.genre}</p>
                <div className="flex items-center space-x-3 mt-1">
                  <span className="text-xs text-slate-400 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {song.duration}
                  </span>
                </div>
              </div>

              <div className="flex flex-col items-end space-y-2">
                <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                  song.difficulty === CourseLevel.BEGINNER ? 'bg-green-100 text-green-700' :
                  song.difficulty === CourseLevel.INTERMEDIATE ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {song.difficulty}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredSongs.length === 0 && (
        <div className="text-center py-12">
          <div className="text-slate-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h4 className="text-lg font-medium text-slate-600 mb-2">No songs found</h4>
          <p className="text-slate-500">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
} 