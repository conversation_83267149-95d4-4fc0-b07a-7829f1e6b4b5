import { getCourses } from '@/data-access/course/course'
import CourseCard from '@/components/Courses/CourseCard'
import FilterForm from '@/components/Courses/FilterForm'
import { CourseLevel } from '@prisma/client'
import { createClient } from '@/utils/supabase/server'

interface CoursesPageProps {
  searchParams: Promise<{
    search?: string
    level?: CourseLevel
  }>
}

export default async function CoursesPage({ searchParams }: CoursesPageProps) {
  const supabase = await createClient();
  const {data: {user}, error: error1} = await supabase.auth.getUser();
  if (error1 || !user) {
    return <div>Error: {error1?.message || 'User not found'}</div>;
  }

  const { data: courseStats, error: error2 } = await getCourses(user.email)
  if(!courseStats || error2) {
    return <div>Error: {error2 || 'No courses found'}</div>;
  }

  const error = error2 || null
  const searchQuery = (await searchParams).search || ''
  const levelFilter = (await searchParams).level || 'ALL'

  const filteredCourses = courseStats.filter(course => {
    const matchesSearch = course.name.toLowerCase().includes(searchQuery.toLowerCase()) || course.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesLevel = levelFilter === 'ALL' || course.level === levelFilter
    return matchesSearch && matchesLevel
  })

  if (error) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-[60vh]">
            <div className="text-center p-4">
              <p className="text-red-500 mb-2">{error}</p>
              <p className="text-sm text-gray-600">Please try refreshing the page or contact support if the problem persists.</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">All Enrolled Courses</h1>
          <p className="text-gray-600">Explore our comprehensive collection of music courses</p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <FilterForm selectedLevel={levelFilter} searchQuery={searchQuery} />
        </div>

        {/* Course Grid */}
        {filteredCourses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600">No courses found matching your criteria</p>
          </div>
        )}
      </div>
    </div>
  )
}
