'use client'

import { useMemo } from 'react'
import { AccessableCourseDetails } from '@/types/course'

export const useCourseStats = (
  course: AccessableCourseDetails | null,
  completedLessons: Set<string>,
  completedAssignments: Set<string>
) => {
  const progressStats = useMemo(() => {
    if (!course) {
      return {
        totalLessons: 0,
        completedLessonsCount: 0,
        totalAssignments: 0,
        completedAssignmentsCount: 0,
        totalModules: 0,
        progressPercentage: 0
      }
    }

    const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
    const totalAssignments = course.modules.reduce((acc, module) => acc + module.assignments.length, 0)
    const totalItems = totalLessons + totalAssignments
    const completedItems = completedLessons.size + completedAssignments.size
    
    return {
      totalLessons,
      completedLessonsCount: completedLessons.size,
      totalAssignments,
      completedAssignmentsCount: completedAssignments.size,
      totalModules: course.modules.length,
      progressPercentage: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0
    }
  }, [course, completedLessons, completedAssignments])

  return progressStats
} 