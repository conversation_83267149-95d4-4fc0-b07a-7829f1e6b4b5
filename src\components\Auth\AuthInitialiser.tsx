"use client";

import { useAuthInit } from "@/hooks/useAuth";

export function AuthInitializer() {
  const { isLoading } = useAuthInit();

  // Show loading spinner during auth initialization
  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return null;
}