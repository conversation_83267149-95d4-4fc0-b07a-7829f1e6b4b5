'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  FileText, 
  Upload, 
  CheckCircle2, 
  Clock, 
  Calendar,
  AlertCircle,
  X,
  BookOpen,
  MessageSquare,
  Music,
  Play,
  Search,
  Download,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Assignment, SubmissionStatus, Music as MusicType } from '@prisma/client'
import { getSignedUrl } from '@/data-access/sing-along/storage'
import { AssignmentSubmission } from '@prisma/client'
import { CustomAudioPlayer } from '@/components/SingAlong/SongPlayer/CustomAudioPlayer'
import Recorder from '@/components/SingAlong/SongPlayer/Recorder'
import { getAvailableSongs, getAudioUrl } from '@/data-access/sing-along/songs'
import Link from 'next/link'

interface AssignmentViewProps {
  assignment: Assignment
  isCompleted: boolean
  onComplete: () => void
  onSubmit: (file: File) => void
  submissionFile?: File | null
  submission?: AssignmentSubmission | null
}

export default function AssignmentView({
  assignment,
  isCompleted,
  onComplete,
  onSubmit,
  submissionFile,
  submission
}: AssignmentViewProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showRecorder, setShowRecorder] = useState(false)
  const [showMusicSelector, setShowMusicSelector] = useState(false)
  const [availableSongs, setAvailableSongs] = useState<MusicType[]>([])
  const [selectedSong, setSelectedSong] = useState<MusicType | null>(null)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const audioRef = useRef<HTMLAudioElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = useState(false)

  // Load available songs on component mount
  useEffect(() => {
    const fetchSongs = async () => {
      const { songs, error } = await getAvailableSongs()
      if (!error) {
        setAvailableSongs(songs)
      }
    }
    fetchSongs()
  }, [])

  // Filter songs based on search term only
  const filteredSongs = availableSongs.filter(song => 
    song.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const isOverdue = new Date() > assignment.deadline
  const timeLeft = assignment.deadline.getTime() - new Date().getTime()
  const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24))

  // Status helpers
  const getStatusColor = (status: SubmissionStatus) => {
    switch (status) {
      case 'SUBMITTED':
        return 'text-blue-700 bg-blue-50 border-blue-200'
      case 'GRADED':
        return 'text-green-700 bg-green-50 border-green-200'
      case 'NEEDS_REVISION':
        return 'text-amber-700 bg-amber-50 border-amber-200'
      case 'OVERDUE':
        return 'text-red-700 bg-red-50 border-red-200'
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200'
    }
  }

  const getStatusText = (status: SubmissionStatus) => {
    switch (status) {
      case 'SUBMITTED':
        return 'Under Review'
      case 'GRADED':
        return 'Graded'
      case 'NEEDS_REVISION':
        return 'Needs Revision'
      case 'OVERDUE':
        return 'Overdue'
      default:
        return 'Unknown'
    }
  }

  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'audio/mpeg',
    'audio/wav',
    'audio/mp3',
    'video/mp4',
    'video/avi',
    'video/mov',
    'audio/x-wav',
    'video/quicktime'
  ]

  const validateFile = (file: File) => {
    console.log('File selected:', file.name, 'Type:', file.type, 'Size:', file.size)
    
    if (!allowedTypes.includes(file.type)) {
      console.error('Invalid file type:', file.type)
      alert(`Please select a valid file type. Selected: ${file.type}\nAllowed: PDF, DOC, DOCX, TXT, MP3, WAV, MP4, AVI, MOV`)
      return false
    }
    
    if (file.size > 10 * 1024 * 1024) {
      console.error('File too large:', file.size)
      alert(`File size must be less than 10MB. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`)
      return false
    }
    
    console.log('File validation passed')
    return true
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input changed')
    const file = event.target.files?.[0]
    
    if (!file) {
      console.log('No file selected')
      return
    }
    
    console.log('File selected, validating...')
    if (validateFile(file)) {
      console.log('Setting selected file:', file.name)
      setSelectedFile(file)
    } else {
      console.log('File validation failed')
      // Reset the input
      event.target.value = ''
    }
  }

  const handleFileInputClick = () => {
    console.log('File input clicked')
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const clearSelectedFile = () => {
    setSelectedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      console.log('File dropped:', file.name)
      if (validateFile(file)) {
        setSelectedFile(file)
      }
    }
  }

  const handleAudioSave = (blob: Blob) => {
    const fileName = `${assignment.title}_recording.mp3`
    const audioFile = new File([blob], fileName, { type: blob.type })
    setSelectedFile(audioFile)
  }

  const handleSongSelect = async (song: MusicType) => {
    setSelectedSong(song)
    setIsLoadingAudio(true)
    setShowMusicSelector(false)
    
    try {
      const { audioUrl: url, error } = await getAudioUrl(song.title)
      if (url && !error) {
        setAudioUrl(url)
      } else {
        alert(`Failed to load audio: ${error}`)
      }
    } catch (error) {
      console.error('Error loading audio:', error)
      alert('Failed to load audio file')
    } finally {
      setIsLoadingAudio(false)
    }
  }

  const handleAssignmentDownload = async () => {
    const {signedUrl, error} = await getSignedUrl(`${assignment.assignmentPath}`)
    if(!signedUrl) {
      alert(error)
    }
    window.open(signedUrl, '_blank')
  }

  const handleSubmit = async () => {
    if (!selectedFile) return
    
    setIsSubmitting(true)
    try {
      await onSubmit(selectedFile)
      onComplete()
      setSelectedFile(null)
    } catch (error) {
      console.error('Submission failed:', error)
      alert('Failed to submit assignment. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Content Container */}
      <div className="flex-1 p-6 space-y-6">
        {/* Assignment Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-2xl font-semibold text-gray-900 mb-4">
                {assignment.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>Due {assignment.deadline.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>
                    {isOverdue ? 'Overdue' : daysLeft === 0 ? 'Due today' : `${daysLeft} days left`}
                  </span>
                </div>
                {submission && (
                  <div className={cn(
                    "flex items-center gap-2 px-3 py-1 rounded-lg border text-xs font-medium",
                    getStatusColor(submission.status)
                  )}>
                    <span>{getStatusText(submission.status)}</span>
                  </div>
                )}
              </div>

              {/* Status Alert */}
              {submission?.status === 'NEEDS_REVISION' && (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg text-amber-800 mb-6">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Revision Required</h3>
                      <p className="text-sm">Your instructor has requested revisions. Please review the feedback and resubmit.</p>
                    </div>
                  </div>
                </div>
              )}

              {submission?.status === 'GRADED' && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-green-800 mb-6">
                  <div className="flex items-start gap-3">
                    <CheckCircle2 className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Assignment Graded</h3>
                      <p className="text-sm">Your assignment has been graded. Check the feedback section below.</p>
                    </div>
                  </div>
                </div>
              )}

              {submission?.status === 'SUBMITTED' && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-800 mb-6">
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">Under Review</h3>
                      <p className="text-sm">Your assignment is being reviewed by your instructor.</p>
                    </div>
                  </div>
                </div>
              )}

              {!isCompleted && !submission && (
                <div className={cn(
                  "p-4 rounded-lg border",
                  isOverdue 
                    ? "bg-red-50 border-red-200 text-red-800"
                    : daysLeft <= 3
                    ? "bg-amber-50 border-amber-200 text-amber-800"
                    : "bg-blue-50 border-blue-200 text-blue-800"
                )}>
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="text-sm font-medium mb-1">
                        {isOverdue ? 'Assignment Overdue' : daysLeft <= 3 ? 'Due Soon' : 'Assignment Active'}
                      </h3>
                      <p className="text-sm">
                        {isOverdue 
                          ? 'This assignment is past its due date.'
                          : daysLeft <= 3
                          ? `This assignment is due in ${daysLeft === 0 ? 'less than a day' : `${daysLeft} day${daysLeft > 1 ? 's' : ''}`}.`
                          : 'Complete this assignment to unlock the next module.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Assignment Instructions */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Instructions</h2>
            {assignment.assignmentPath && (
              <button
                onClick={handleAssignmentDownload}
                className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                Download Questions
              </button>
            )}
          </div>
          
          {assignment.description ? (
            <div className="prose prose-sm max-w-none">
              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                {assignment.description}
              </p>
            </div>
          ) : (
            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
              <BookOpen className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
              <p className="text-gray-600 text-sm">
                No detailed instructions provided. Please follow the general assignment guidelines.
              </p>
            </div>
          )}
        </div>

        {/* Conditional Content Based on Submission Status */}
        {/* NOT SUBMITTED YET - Show practice and submission */}
        {!submission && (
          <>
            {/* Audio Practice Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-3 mb-6">
                <Music className="w-5 h-5 text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">Practice Along</h2>
              </div>

              {/* Music Selection */}
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-sm font-medium text-gray-700">Select Practice Music</h3>
                  <button
                    onClick={() => setShowMusicSelector(!showMusicSelector)}
                    className={cn(
                      "inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors text-sm",
                      showMusicSelector 
                        ? "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    )}
                  >
                    <Music className="w-4 h-4" />
                    {showMusicSelector ? 'Hide Library' : 'Browse Library'}
                  </button>
                </div>

                {/* Selected Song Display */}
                {selectedSong && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between gap-3">
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <Music className="w-5 h-5 text-blue-600 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-blue-900 truncate">{selectedSong.title}</p>
                          <p className="text-xs text-blue-700">Duration: {selectedSong.duration}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedSong(null)
                          setAudioUrl(null)
                        }}
                        className="p-1 hover:bg-blue-200 rounded transition-colors flex-shrink-0"
                      >
                        <X className="w-4 h-4 text-blue-600" />
                      </button>
                    </div>
                  </div>
                )}

                {/* Music Selector */}
                {showMusicSelector && (
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    {/* Search Bar */}
                    <div className="relative mb-4">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search songs..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                      />
                    </div>

                    {/* Songs List */}
                    <div className="max-h-64 overflow-y-auto">
                      {filteredSongs.length === 0 ? (
                        <div className="text-center py-8">
                          <Music className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">No songs found</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {filteredSongs.map((song) => (
                            <button
                              key={song.id}
                              onClick={() => handleSongSelect(song)}
                              className="w-full p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                            >
                              <div className="flex items-center gap-3">
                                <Play className="w-4 h-4 text-gray-400 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium text-gray-900 truncate">{song.title}</p>
                                  <p className="text-xs text-gray-500">Duration: {song.duration}</p>
                                </div>
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Audio Player */}
              {audioUrl && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Audio Player</h3>
                  {isLoadingAudio ? (
                    <div className="p-4 bg-gray-50 rounded-lg text-center">
                      <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-sm text-gray-600">Loading audio...</p>
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <CustomAudioPlayer 
                        src={audioUrl} 
                        duration={selectedSong?.duration || "0:00"}
                        compact={false}
                        audioRef={audioRef}
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Recording Section */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                  <h3 className="text-sm font-medium text-gray-700">Record Your Practice</h3>
                  <button
                    onClick={() => setShowRecorder(!showRecorder)}
                    disabled={!selectedSong || !audioUrl}
                    className={cn(
                      "inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors text-sm",
                      (!selectedSong || !audioUrl)
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : showRecorder
                        ? "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        : "bg-purple-600 text-white hover:bg-purple-700"
                    )}
                  >
                    {showRecorder ? 'Hide Recorder' : 'Show Recorder'}
                  </button>
                </div>

                {showRecorder && selectedSong && audioUrl ? (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <Recorder 
                      songName={selectedSong?.title || ''}
                      audioElement={audioRef.current}
                      OnSave={handleAudioSave}
                    />
                  </div>
                ) : (
                  <div className="p-4 bg-gray-50 rounded-lg text-center">
                    <p className="text-sm text-gray-600">
                      {selectedSong 
                        ? "Click 'Show Recorder' to start recording your practice session."
                        : "Select a practice track first to enable recording."
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Submission Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                Submit Assignment
              </h2>

              {/* File Upload Area */}
              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-8 text-center transition-all",
                  isDragging 
                    ? "border-blue-500 bg-blue-100/50"
                    : selectedFile 
                    ? "border-green-300 bg-green-50/50"
                    : "border-gray-300 hover:border-blue-400 hover:bg-blue-50/50"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="max-w-md mx-auto">
                  <Upload className={cn(
                    "w-10 h-10 mx-auto mb-4",
                    selectedFile ? "text-green-500" : isDragging ? "text-blue-500" : "text-gray-400"
                  )} />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {selectedFile ? "File Selected" : isDragging ? "Drop File Here" : "Upload Your Assignment"}
                  </h3>
                  <p className="text-sm text-gray-500 mb-6">
                    {selectedFile 
                      ? "Your file is ready to be submitted"
                      : isDragging
                      ? "Release to upload your file"
                      : "Drag and drop your file here, or click to browse"}
                  </p>

                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.txt,.mp3,.wav,.mp4,.avi,.mov"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                    ref={fileInputRef}
                  />
                  <button
                    type="button"
                    onClick={handleFileInputClick}
                    className={cn(
                      "inline-flex items-center gap-2 px-6 py-3 rounded-lg transition-colors text-sm font-medium",
                      selectedFile
                        ? "bg-green-100 text-green-700 hover:bg-green-200"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    )}
                  >
                    <Upload className="w-4 h-4" />
                    {selectedFile ? "Change File" : "Choose File"}
                  </button>

                  {selectedFile && (
                    <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 min-w-0 flex-1">
                          <FileText className="w-5 h-5 text-gray-600 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-gray-900 truncate">{selectedFile.name}</p>
                            <p className="text-xs text-gray-500">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                          </div>
                        </div>
                        <button
                          onClick={clearSelectedFile}
                          className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                        >
                          <X className="w-4 h-4 text-gray-500" />
                        </button>
                      </div>
                    </div>
                  )}

                  <p className="text-xs text-gray-500 mt-4">
                    Supported: PDF, DOC, DOCX, TXT, MP3, WAV, MP4, AVI, MOV (Max: 10MB)
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <button
                onClick={handleSubmit}
                disabled={!selectedFile || isSubmitting}
                className={cn(
                  "w-full mt-6 py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2",
                  selectedFile && !isSubmitting
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                )}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-white rounded-full animate-spin"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="w-4 h-4" />
                    Submit Assignment
                  </>
                )}
              </button>
            </div>
          </>
        )}

        {/* NEEDS REVISION - Show feedback and resubmission */}
        {submission?.status === 'NEEDS_REVISION' && (
          <>
            {/* Feedback Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-amber-600" />
                Instructor Feedback - Revision Required
              </h2>
              
              <div className="p-4 bg-amber-50 rounded-lg border border-amber-200 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <p className="text-sm font-medium text-amber-900">Feedback</p>
                  <span className="px-3 py-1 rounded-full text-xs font-medium border bg-amber-50 text-amber-700 border-amber-200">
                    Needs Revision
                  </span>
                </div>
                <p className="text-amber-800 text-sm leading-relaxed whitespace-pre-wrap">
                  {submission.feedback}
                </p>
                <p className="text-xs text-amber-600 mt-3">
                  {submission.updatedAt.toLocaleDateString()} at {submission.updatedAt.toLocaleTimeString()}
                </p>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">Action Required</h3>
                    <p className="text-sm text-blue-800">
                      Please address the feedback above and resubmit your assignment below.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Resubmission Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                Resubmit Assignment
              </h2>

              {/* File Upload Area */}
              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-8 text-center transition-all",
                  isDragging 
                    ? "border-amber-500 bg-amber-100/50"
                    : selectedFile 
                    ? "border-green-300 bg-green-50/50"
                    : "border-gray-300 hover:border-amber-400 hover:bg-amber-50/50"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="max-w-md mx-auto">
                  <Upload className={cn(
                    "w-10 h-10 mx-auto mb-4",
                    selectedFile ? "text-green-500" : isDragging ? "text-amber-500" : "text-gray-400"
                  )} />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {selectedFile ? "Revised File Selected" : isDragging ? "Drop Revised File Here" : "Upload Revised Assignment"}
                  </h3>
                  <p className="text-sm text-gray-500 mb-6">
                    {selectedFile 
                      ? "Your revised file is ready to be submitted"
                      : isDragging
                      ? "Release to upload your revised file"
                      : "Upload your revised assignment addressing the feedback"}
                  </p>

                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.txt,.mp3,.wav,.mp4,.avi,.mov"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload-revision"
                    ref={fileInputRef}
                  />
                  <button
                    type="button"
                    onClick={handleFileInputClick}
                    className={cn(
                      "inline-flex items-center gap-2 px-6 py-3 rounded-lg transition-colors text-sm font-medium",
                      selectedFile
                        ? "bg-green-100 text-green-700 hover:bg-green-200"
                        : "bg-amber-600 text-white hover:bg-amber-700"
                    )}
                  >
                    <Upload className="w-4 h-4" />
                    {selectedFile ? "Change File" : "Choose Revised File"}
                  </button>

                  {selectedFile && (
                    <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 min-w-0 flex-1">
                          <FileText className="w-5 h-5 text-gray-600 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-gray-900 truncate">{selectedFile.name}</p>
                            <p className="text-xs text-gray-500">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                          </div>
                        </div>
                        <button
                          onClick={clearSelectedFile}
                          className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                        >
                          <X className="w-4 h-4 text-gray-500" />
                        </button>
                      </div>
                    </div>
                  )}

                  <p className="text-xs text-gray-500 mt-4">
                    Supported: PDF, DOC, DOCX, TXT, MP3, WAV, MP4, AVI, MOV (Max: 10MB)
                  </p>
                </div>
              </div>

              {/* Resubmit Button */}
              <button
                onClick={handleSubmit}
                disabled={!selectedFile || isSubmitting}
                className={cn(
                  "w-full mt-6 py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2",
                  selectedFile && !isSubmitting
                    ? "bg-amber-600 text-white hover:bg-amber-700"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                )}
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-white rounded-full animate-spin"></div>
                    Resubmitting...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="w-4 h-4" />
                    Resubmit Assignment
                  </>
                )}
              </button>
            </div>
          </>
        )}

        {/* SUBMITTED - Show submission status and feedback link */}
        {submission?.status === 'SUBMITTED' && (
          <>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CheckCircle2 className="w-5 h-5 text-blue-600" />
                Assignment Submitted
              </h2>
              
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 mb-4">
                <div className="flex items-start gap-3">
                  <Clock className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">Under Review</h3>
                    <p className="text-sm text-blue-800">
                      Your assignment has been submitted and is currently being reviewed by your instructor.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg mb-6">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Submission Details</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Submitted:</span>
                    <span>{submission.submittedAt.toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className="px-2 py-1 rounded text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200">
                      Under Review
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start gap-3">
                  <Clock className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-900 mb-1">What's Next?</h3>
                    <p className="text-sm text-yellow-800">
                      Your instructor will review your submission and provide feedback. You'll be notified once grading is complete.
                    </p>
                    <p className="text-xs text-yellow-700 mt-2">
                      Typical feedback time: 2-3 business days
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Feedback Session Link */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Schedule Feedback Session</h3>
              </div>
              
              <p className="text-sm text-gray-600 mb-6">
                Book a one-on-one session with your instructor for personalized feedback on your submission.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={`/dashboard/feedback?assignment=${assignment.id}`}
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <Calendar className="w-4 h-4" />
                  Schedule Feedback Session
                  <ExternalLink className="w-4 h-4" />
                </Link>
                
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Sessions available Monday-Friday, 9 AM - 5 PM</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* GRADED - Show final feedback and optional additional session */}
        {submission?.status === 'GRADED' && (
          <>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CheckCircle2 className="w-5 h-5 text-green-600" />
                Assignment Graded
              </h2>
              
              <div className="p-4 bg-green-50 rounded-lg border border-green-200 mb-4">
                <div className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-green-900 mb-1">Assignment Complete</h3>
                    <p className="text-sm text-green-800">
                      Your assignment has been graded. You can now proceed to the next module.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Submission Details</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Submitted:</span>
                    <span>{submission.submittedAt.toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Graded:</span>
                    <span>{submission.updatedAt.toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className="px-2 py-1 rounded text-xs font-medium border bg-green-50 text-green-700 border-green-200">
                      Graded
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Feedback Section */}
            {submission?.feedback && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-green-600" />
                  Instructor Feedback
                </h2>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-medium text-green-900">Final Feedback</p>
                    <span className="px-3 py-1 rounded-full text-xs font-medium border bg-green-50 text-green-700 border-green-200">
                      Graded
                    </span>
                  </div>
                  <p className="text-green-800 text-sm leading-relaxed whitespace-pre-wrap">
                    {submission.feedback}
                  </p>
                  <p className="text-xs text-green-600 mt-3">
                    {submission.updatedAt.toLocaleDateString()} at {submission.updatedAt.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            )}

            {/* Feedback Session Link */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Schedule Additional Feedback Session</h3>
              </div>
              
              <p className="text-sm text-gray-600 mb-6">
                Want to discuss your graded assignment further? Book an additional session with your instructor.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={`/dashboard/feedback?assignment=${assignment.id}`}
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <Calendar className="w-4 h-4" />
                  Schedule Additional Session
                  <ExternalLink className="w-4 h-4" />
                </Link>
                
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Sessions available Monday-Friday, 9 AM - 5 PM</span>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
} 