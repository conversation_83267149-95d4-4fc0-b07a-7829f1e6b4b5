'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useCourseDetails } from '@/hooks/useCourseDetails'
import { useCourseProgress } from '@/hooks/useCourseProgress'
import { useCourseNavigation } from '@/hooks/useCourseNavigation'
import { useCourseStats } from '@/hooks/useCourseStats'
import CourseContentSidebar from '@/components/Courses/CoursePage/CourseContentSidebar'
import AssignmentView from '@/components/Courses/CoursePage/AssignmentView'
import LessonView from '@/components/Courses/CoursePage/LessonView'
import { 
  CourseLoading, 
  CourseError, 
  CourseNotFound, 
  EmptyContent 
} from '@/components/Courses/CoursePage/CoursePageStates'
import { useUserDetails } from '@/hooks/useUserDetails'
import { AssignmentSubmission, User } from '@prisma/client'
import { createClient } from '@/utils/supabase/client'
import { createAssignmentSubmission } from '@/data-access/course/course'

export default function CoursePage() {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [userError, setUserError] = useState<string | null>(null)
  const [submitError, setSubmitError] = useState<string | null>(null)
  
  const params = useParams()
  const courseId = params.id as string

  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const { user, error: userError } = await useUserDetails()
        if (user) {
          setUser(user)
          setUserError(null)
        }
        if (userError) {
          setUserError('Failed to load user details. Please refresh the page.')
        }
      } catch (error) {
        setUserError('An unexpected error occurred while loading user details.')
      }
    }
    fetchUserDetails()
  }, [])

  // Data hooks
  const { course, isPending: courseLoading, error: courseError, refreshCourse } = useCourseDetails(courseId)
  const {
    completedLessons,
    completedAssignments,
    accessibleLessons,
    accessibleAssignments,
    markLessonComplete,
    markAssignmentComplete,
    canAccessLesson,
    canAccessAssignment,
    isPending: progressPending
  } = useCourseProgress(course)

  // Navigation hook
  const {
    selectedLesson,
    selectedAssignment,
    videoProgress,
    currentAssignment,
    canGoPrevious,
    canGoNext,
    handleLessonSelect,
    handleAssignmentSelect,
    handlePreviousLesson,
    handleNextLesson,
    handleVideoProgress
  } = useCourseNavigation(course, canAccessLesson, canAccessAssignment)

  // Stats hook
  const progressStats = useCourseStats(course, completedLessons, completedAssignments)

  // Event handlers
  const handleVideoComplete = () => {
    if (selectedLesson && !completedLessons.has(selectedLesson.id) && !progressPending) {
      markLessonComplete(selectedLesson.id)
    }
  }

  const handleMarkComplete = () => {
    if (selectedLesson && videoProgress >= 70 && !completedLessons.has(selectedLesson.id) && !progressPending) {
      markLessonComplete(selectedLesson.id)
    }
  }

  const handleAssignmentSubmit = async (file: File) => {
    if (!user) {
      setSubmitError('User not found. Please refresh the page and try again.')
      return
    }

    setSubmitError(null)

    try {
      const supabase = createClient()
      const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-private'
      
      const { data, error: uploadError } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(`assignment-submissions/${user.email}/${selectedAssignment.id}`, file, {
          upsert: true
        })

      if (uploadError) {
        setSubmitError('Failed to upload your assignment file. Please try again.')
        return
      }

      const { success, error: submissionError } = await createAssignmentSubmission(selectedAssignment.id, data.path)
      
      if (!success) {
        setSubmitError(submissionError || 'Failed to submit your assignment. Please try again.')
        return
      }

      // Small delay to ensure database transaction is committed
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Refresh course data to get updated submission status
      refreshCourse()
      
      // Mark assignment as completed in local state
      markAssignmentComplete(selectedAssignment.id)
      setSubmitError(null)

    } 
    catch (error) {
      setSubmitError('An unexpected error occurred while submitting your assignment. Please try again.')
    }
  }

  const handleAssignmentComplete = () => {
    if (selectedAssignment && !completedAssignments.has(selectedAssignment.id)) {
      markAssignmentComplete(selectedAssignment.id)
    }
  }

  // Computed values
  const isCurrentLessonComplete = selectedLesson ? completedLessons.has(selectedLesson.id) : false
  const isCurrentAssignmentComplete = selectedAssignment ? completedAssignments.has(selectedAssignment.id) : false
  const canMarkComplete = videoProgress >= 70 && !isCurrentLessonComplete

  // Render states
  if (courseLoading) return <CourseLoading />
  if (courseError) return <CourseError error={courseError} />
  if (!course) return <CourseNotFound />

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Error Messages */}
      {userError && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4 mx-4 mt-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {userError}
          </div>
        </div>
      )}

      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Sidebar Component handles mobile header and overlay */}
        <CourseContentSidebar
          course={course}
          selectedLesson={selectedLesson}
          selectedAssignment={selectedAssignment}
          onLessonSelect={handleLessonSelect}
          onAssignmentSelect={handleAssignmentSelect}
          progress={{
            completedLessons: progressStats.completedLessonsCount,
            totalLessons: progressStats.totalLessons,
            currentModule: 1,
            totalModules: progressStats.totalModules
          }}
          isMobileSidebarOpen={isMobileSidebarOpen}
          setIsMobileSidebarOpen={setIsMobileSidebarOpen}
          completedLessons={completedLessons}
          completedAssignments={completedAssignments}
          accessibleLessons={accessibleLessons}
          accessibleAssignments={accessibleAssignments}
        />

        {/* Mobile Main Content */}
        <div className="w-full">
          {selectedLesson ? (
            <LessonView
              lesson={selectedLesson}
              videoProgress={videoProgress}
              isCompleted={isCurrentLessonComplete}
              canMarkComplete={canMarkComplete}
              canGoPrevious={canGoPrevious}
              canGoNext={canGoNext}
              isPending={progressPending}
              onVideoProgress={handleVideoProgress}
              onVideoComplete={handleVideoComplete}
              onMarkComplete={handleMarkComplete}
              onPreviousLesson={handlePreviousLesson}
              onNextLesson={handleNextLesson}
            />
          ) : selectedAssignment ? (
            <div className="flex flex-col min-h-screen">
              {/* Assignment Submission Error */}
              {submitError && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4 mx-4 mt-4">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    {submitError}
                  </div>
                </div>
              )}
              
              <AssignmentView
                assignment={currentAssignment!}
                isCompleted={isCurrentAssignmentComplete}
                onComplete={handleAssignmentComplete}
                onSubmit={handleAssignmentSubmit}
                submission={currentAssignment?.submission as AssignmentSubmission}
              />
            </div>
          ) : (
            <EmptyContent onOpenSidebar={() => setIsMobileSidebarOpen(true)} />
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex min-h-screen">
        {/* Desktop Sidebar */}
        <CourseContentSidebar
          course={course}
          selectedLesson={selectedLesson}
          selectedAssignment={selectedAssignment}
          onLessonSelect={handleLessonSelect}
          onAssignmentSelect={handleAssignmentSelect}
          progress={{
            completedLessons: progressStats.completedLessonsCount,
            totalLessons: progressStats.totalLessons,
            currentModule: 1,
            totalModules: progressStats.totalModules
          }}
          isMobileSidebarOpen={isMobileSidebarOpen}
          setIsMobileSidebarOpen={setIsMobileSidebarOpen}
          completedLessons={completedLessons}
          completedAssignments={completedAssignments}
          accessibleLessons={accessibleLessons}
          accessibleAssignments={accessibleAssignments}
        />

        {/* Desktop Main Content */}
        <div className="flex-1">
          {selectedLesson ? (
            <LessonView
              lesson={selectedLesson}
              videoProgress={videoProgress}
              isCompleted={isCurrentLessonComplete}
              canMarkComplete={canMarkComplete}
              canGoPrevious={canGoPrevious}
              canGoNext={canGoNext}
              isPending={progressPending}
              onVideoProgress={handleVideoProgress}
              onVideoComplete={handleVideoComplete}
              onMarkComplete={handleMarkComplete}
              onPreviousLesson={handlePreviousLesson}
              onNextLesson={handleNextLesson}
            />
          ) : selectedAssignment ? (
            <div className="flex flex-col min-h-screen">
              {/* Assignment Submission Error */}
              {submitError && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4 mx-4 mt-4">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    {submitError}
                  </div>
                </div>
              )}
              
              <AssignmentView
                assignment={currentAssignment!}
                isCompleted={isCurrentAssignmentComplete}
                onComplete={handleAssignmentComplete}
                onSubmit={handleAssignmentSubmit}
                submission={currentAssignment?.submission as AssignmentSubmission}
              />
            </div>
          ) : (
            <EmptyContent onOpenSidebar={() => setIsMobileSidebarOpen(true)} />
          )}
        </div>
      </div>
    </div>
  )
}