import { handleError } from "@/data-access/helpers";
import { overdueUpdator } from "@/data-access/overdue/overdues";
import { NextRequest, NextResponse } from "next/server";


export async function POST(request: NextRequest) {
    
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        await overdueUpdator();
        return NextResponse.json({
            success: true,
            message: "Overdues updated successfully"
        });
    }
    catch(error){
        return NextResponse.json({
            success: false,
            message: "Error updating overdues"
        }, { status: 500 });
    }
}