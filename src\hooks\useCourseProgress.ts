import { useState, useEffect, useMemo, useTransition } from 'react'
import { AccessableCourseDetails } from '@/types/course'
import { markLessonAsCompleted } from '@/data-access/course/course'

export const useCourseProgress = (course: AccessableCourseDetails | null) => {
  const [completedLessons, setCompletedLessons] = useState<Set<string>>(new Set())
  const [completedAssignments, setCompletedAssignments] = useState<Set<string>>(new Set())
  const [isPending, startTransition] = useTransition()
  
  // Initialize with server data
  useEffect(() => {
    if (!course) return
    
    const completed = new Set<string>()
    const assignments = new Set<string>()
    
    course.modules.forEach(module => {
      module.lessons.forEach(lesson => {
        if (lesson.completed) {
          completed.add(lesson.id)
        }
      })
      
      module.assignments?.forEach(assignment => {
        // An assignment is considered completed if there's any submission, regardless of feedback status
        if (assignment.submission) {
          assignments.add(assignment.id)
        }
      })
    })
    
    setCompletedLessons(completed)
    setCompletedAssignments(assignments)
  }, [course])
  
  // Calculate accessible content
  const accessibleLessons = useMemo(() => {
    if (!course) return new Set<string>()
    
    const accessible = new Set<string>()
    
    // Process modules in order to enforce proper progression
    for (let moduleIndex = 0; moduleIndex < course.modules.length; moduleIndex++) {
      const module = course.modules[moduleIndex]
      
      // For the first module, first lesson is always accessible
      if (moduleIndex === 0 && module.lessons.length > 0) {
        accessible.add(module.lessons[0].id)
      }
      
      // Check if previous module is fully completed (all lessons + assignments)
      const previousModule = moduleIndex > 0 ? course.modules[moduleIndex - 1] : null
      let canAccessCurrentModule = true
      
      if (previousModule) {
        // All lessons in previous module must be completed
        const allPreviousLessonsComplete = previousModule.lessons.every(l => 
          completedLessons.has(l.id)
        )
        
        // All assignments in previous module must be completed
        const allPreviousAssignmentsComplete = previousModule.assignments?.every(a => 
          completedAssignments.has(a.id)
        ) ?? true // If no assignments, consider complete
        
        canAccessCurrentModule = allPreviousLessonsComplete && allPreviousAssignmentsComplete
      }
      
      if (canAccessCurrentModule) {
        // Within current module, unlock lessons sequentially
        for (let lessonIndex = 0; lessonIndex < module.lessons.length; lessonIndex++) {
          const lesson = module.lessons[lessonIndex]
          
          if (lessonIndex === 0) {
            // First lesson of accessible module is always accessible
            accessible.add(lesson.id)
          } else {
            // Subsequent lessons require previous lesson completion
            const previousLesson = module.lessons[lessonIndex - 1]
            if (completedLessons.has(previousLesson.id)) {
              accessible.add(lesson.id)
            }
          }
        }
      }
    }
    
    return accessible
  }, [course, completedLessons, completedAssignments])
  
  const accessibleAssignments = useMemo(() => {
    if (!course) return new Set<string>()
    
    const accessible = new Set<string>()
    
    course.modules.forEach(module => {
      const allLessonsCompleted = module.lessons.every(l => completedLessons.has(l.id))
      
      if (allLessonsCompleted && module.assignments) {
        module.assignments.forEach(a => accessible.add(a.id))
      }
    })
    
    return accessible
  }, [course, completedLessons])
  
  // Actions with API integration
  const markLessonComplete = async (lessonId: string) => {
    // Prevent duplicate calls
    if (!accessibleLessons.has(lessonId) || completedLessons.has(lessonId) || isPending) {
      return
    }
    
    // Optimistic update
    setCompletedLessons(prev => new Set([...prev, lessonId]))
    
    startTransition(async () => {
      try {
        const result = await markLessonAsCompleted(lessonId)
        if (!result.success) {
          setCompletedLessons(prev => {
            const newSet = new Set(prev)
            newSet.delete(lessonId)
            return newSet
          })
          console.error('Failed to mark lesson as completed:', result.error)
        }
      } catch (error) {
        setCompletedLessons(prev => {
          const newSet = new Set(prev)
          newSet.delete(lessonId)
          return newSet
        })
        console.error('Error marking lesson as completed:', error)
      }
    })
  }
  
  const markAssignmentComplete = (assignmentId: string) => {
    if (!accessibleAssignments.has(assignmentId)) return
    
    setCompletedAssignments(prev => new Set([...prev, assignmentId]))
  }
  
  const canAccessLesson = (lessonId: string) => accessibleLessons.has(lessonId)
  const canAccessAssignment = (assignmentId: string) => accessibleAssignments.has(assignmentId)
  
  return {
    completedLessons,
    completedAssignments,
    accessibleLessons,
    accessibleAssignments,
    markLessonComplete,
    markAssignmentComplete,
    canAccessLesson,
    canAccessAssignment,
    isPending
  }
}