import Link from 'next/link'
import { EnrollmentWithCourse } from '@/types/profile'

interface EnrollmentsListProps {
  enrollments: EnrollmentWithCourse[]
}

export function EnrollmentsList({ enrollments }: EnrollmentsListProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(date))
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BEGINNER':
        return 'bg-green-100 text-green-800'
      case 'INTERMEDIATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'ADVANCED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500'
    if (percentage >= 60) return 'bg-white0'
    if (percentage >= 40) return 'bg-yellow-500'
    return 'bg-gray-400'
  }

  if (enrollments.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
          My Enrollments
        </h2>
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No enrollments yet</h3>
          <p className="mt-2 text-sm text-gray-600">
            You haven't enrolled in any courses yet. Start your learning journey today!
          </p>
          <Link
            href="/courses"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Browse Courses
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
          My Enrollments ({enrollments.length})
        </h2>
        <Link
          href="/dashboard/courses"
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          Browse More Courses
        </Link>
      </div>

      <div className="space-y-4">
        {enrollments.map((enrollment) => (
          <div key={enrollment.id} className="border border-gray-100 rounded-lg p-4 hover:bg-white transition-colors">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-base font-semibold text-gray-900 truncate">
                    {enrollment.course.name}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(enrollment.course.level)}`}>
                    {enrollment.course.level}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                  {enrollment.course.description}
                </p>

                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-xs text-gray-500">
                  <span>Enrolled: {formatDate(enrollment.enrolledAt)}</span>
                  {enrollment.completedAt && (
                    <>
                      <span className="hidden sm:inline">•</span>
                      <span>Completed: {formatDate(enrollment.completedAt)}</span>
                    </>
                  )}
                  <span className="hidden sm:inline">•</span>
                  <span>Instructor: {enrollment.course.instructorEmail}</span>
                </div>
              </div>

              <div className="flex flex-col items-end gap-2">
                {enrollment.completedAt ? (
                  <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Completed
                  </span>
                ) : (
                  <span className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    In Progress
                  </span>
                )}
                
                <Link
                  href={`/courses/${enrollment.course.id}`}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  View Course
                </Link>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600">Overall Progress</span>
                <span className="font-medium text-gray-900">
                  {enrollment.progress.progressPercentage}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(enrollment.progress.progressPercentage)}`}
                  style={{ width: `${enrollment.progress.progressPercentage}%` }}
                />
              </div>
              
              {/* Detailed Progress Breakdown */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs">
                {/* Lesson Progress */}
                <div className="bg-white rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-600 font-medium">Lessons</span>
                    <span className="text-gray-900 font-semibold">
                      {enrollment.progress.lessonProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5 mb-1">
                    <div
                      className="bg-white0 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${enrollment.progress.lessonProgress}%` }}
                    />
                  </div>
                  <span className="text-gray-500">
                    {enrollment.progress.completedLessons} of {enrollment.progress.totalLessons} completed
                  </span>
                </div>

                {/* Assignment Progress */}
                <div className="bg-white rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-600 font-medium">Assignments</span>
                    <span className="text-gray-900 font-semibold">
                      {enrollment.progress.assignmentProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5 mb-1">
                    <div
                      className="bg-purple-500 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${enrollment.progress.assignmentProgress}%` }}
                    />
                  </div>
                  <span className="text-gray-500">
                    {enrollment.progress.totalAssignments > 0 
                      ? `${enrollment.progress.completedAssignments} of ${enrollment.progress.totalAssignments} completed`
                      : 'No assignments'
                    }
                  </span>
                </div>
              </div>
              
              {/* Module Progress */}
              <div className="mt-3 text-center">
                <span className="text-xs text-gray-500">
                  Modules: {enrollment.progress.completedModules} of {enrollment.progress.totalModules} completed
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 