'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Search, X } from 'lucide-react';
import { CourseLevel } from '@prisma/client';

interface MusicLibraryFiltersProps {
  genres: string[];
}

export default function MusicLibraryFilters({ genres }: MusicLibraryFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const searchTerm = searchParams.get('search') || '';
  const selectedGenre = searchParams.get('genre') || 'All';
  const selectedDifficulty = searchParams.get('difficulty') || 'All';
  
  const updateSearchParams = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value === 'All' || value === '') {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    router.push(`?${params.toString()}`);
  };

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const search = formData.get('search') as string;
    updateSearchParams('search', search);
  };

  const clearSearch = () => {
    updateSearchParams('search', '');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              name="search"
              placeholder="Search songs..."
              defaultValue={searchTerm}
              className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </form>
        </div>

        {/* Genre Filter */}
        <div className="md:w-48">
          <select
            value={selectedGenre}
            onChange={(e) => updateSearchParams('genre', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {genres.map(genre => (
              <option key={genre} value={genre}>{genre}</option>
            ))}
          </select>
        </div>

        {/* Difficulty Filter */}
        <div className="md:w-48">
          <select
            value={selectedDifficulty}
            onChange={(e) => updateSearchParams('difficulty', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="All">All Difficulties</option>
            <option value={CourseLevel.BEGINNER}>Beginner</option>
            <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
            <option value={CourseLevel.ADVANCED}>Advanced</option>
          </select>
        </div>
      </div>
    </div>
  );
} 