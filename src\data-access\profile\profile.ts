"use server"

import { prisma } from '@/utils/prisma/prisma'
import { PaymentStatus, SubmissionStatus } from '@prisma/client'
import type {
  ProfileData,
  UserProfile,
  EnrollmentWithCourse,
  CertificateWithCourse,
  PaymentHistory,
  ProfileStats
} from '@/types/profile'
import { getUserDetails } from '../auth'
import { revalidatePath } from 'next/cache'

type DataAccessResult<T> = {
  success: boolean
  data?: T
  error?: string
}

export async function getUserProfile(email: string): Promise<DataAccessResult<UserProfile>> {
  try {

    const user = await prisma.user.findUnique({
      where: { email: email }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    return { success: true, data: user }
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return { success: false, error: 'Failed to fetch user profile' }
  }
}

export async function getUserEnrollments(email: string): Promise<DataAccessResult<EnrollmentWithCourse[]>> {
  try {
    const user = await prisma.user.findUnique({
      where: { email: email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const enrollments = await prisma.enrollment.findMany({
      where: { userId: user.id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            description: true,
            level: true,
            instructorEmail: true,
            modules: {
              select: {
                id: true,
                lessons: {
                  select: { id: true }
                },
                assignments: {
                  select: { id: true }
                }
              }
            }
          }
        }
      },
      orderBy: { enrolledAt: 'desc' }
    })

    // Calculate progress for each enrollment
    const enrollmentsWithProgress: EnrollmentWithCourse[] = await Promise.all(
      enrollments.map(async (enrollment) => {
        const totalModules = enrollment.course.modules.length
        const totalLessons = enrollment.course.modules.reduce(
          (acc, module) => acc + module.lessons.length, 0
        )
        const totalAssignments = enrollment.course.modules.reduce(
          (acc, module) => acc + module.assignments.length, 0
        )

        // Get completed modules, lessons, and assignments for this user
        const completedData = await prisma.user.findUnique({
          where: { id: user.id },
          select: {
            completedModules: {
              where: { courseId: enrollment.course.id },
              select: { id: true }
            },
            completedLessons: {
              where: {
                module: { courseId: enrollment.course.id }
              },
              select: { id: true }
            },
            assignments: {
              where: {
                assignment: {
                  module: { courseId: enrollment.course.id }
                },
                status: { in: [SubmissionStatus.GRADED, SubmissionStatus.SUBMITTED] }
              },
              select: { id: true }
            }
          }
        })

        const completedModulesCount = completedData?.completedModules.length || 0
        const completedLessonsCount = completedData?.completedLessons.length || 0
        const completedAssignmentsCount = completedData?.assignments.length || 0
        
        const lessonProgress = totalLessons > 0 
          ? Math.round((completedLessonsCount / totalLessons) * 100)
          : 0
        
        const assignmentProgress = totalAssignments > 0 
          ? Math.round((completedAssignmentsCount / totalAssignments) * 100)
          : 0
        
        // Overall progress based on both lessons and assignments
        const totalItems = totalLessons + totalAssignments
        const completedItems = completedLessonsCount + completedAssignmentsCount
        const progressPercentage = totalItems > 0 
          ? Math.round((completedItems / totalItems) * 100)
          : 0

        return {
          id: enrollment.id,
          enrolledAt: enrollment.enrolledAt,
          completedAt: enrollment.completedAt,
          course: {
            id: enrollment.course.id,
            name: enrollment.course.name,
            description: enrollment.course.description,
            level: enrollment.course.level,
            instructorEmail: enrollment.course.instructorEmail
          },
          progress: {
            totalModules,
            completedModules: completedModulesCount,
            totalLessons,
            completedLessons: completedLessonsCount,
            totalAssignments,
            completedAssignments: completedAssignmentsCount,
            progressPercentage,
            lessonProgress,
            assignmentProgress
          }
        }
      })
    )

    return { success: true, data: enrollmentsWithProgress }
  } catch (error) {
    console.error('Error fetching user enrollments:', error)
    return { success: false, error: 'Failed to fetch enrollments' }
  }
}

export async function getUserCertificates(email: string): Promise<DataAccessResult<CertificateWithCourse[]>> {
  try {

    const user = await prisma.user.findUnique({
      where: { email: email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const certificates = await prisma.certificate.findMany({
      where: { userId: user.id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            level: true
          }
        }
      },
      orderBy: { issuedAt: 'desc' }
    })

    const certificatesWithCourse: CertificateWithCourse[] = certificates.map(cert => ({
      id: cert.id,
      pdfPath: cert.pdfPath,
      issuedAt: cert.issuedAt,
      course: cert.course
    }))

    return { success: true, data: certificatesWithCourse }
  } catch (error) {
    console.error('Error fetching user certificates:', error)
    return { success: false, error: 'Failed to fetch certificates' }
  }
}

export async function getUserPayments(email: string): Promise<DataAccessResult<PaymentHistory[]>> {
  try {

    const user = await prisma.user.findUnique({
      where: { email: email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const payments = await prisma.payment.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' }
    })

    // Get course names for payments that have courseId
    const paymentHistories: PaymentHistory[] = await Promise.all(
      payments.map(async (payment) => {
        let courseName: string | undefined

        if (payment.courseId) {
          const course = await prisma.course.findUnique({
            where: { id: payment.courseId },
            select: { name: true }
          })
          courseName = course?.name
        }

        return {
          id: payment.id,
          amount: Number(payment.amount),
          currency: payment.currency,
          status: payment.status,
          createdAt: payment.createdAt,
          courseId: payment.courseId,
          courseName,
          razorpayOrderId: payment.razorpayOrderId,
          razorpayPaymentId: payment.razorpayPaymentId
        }
      })
    )

    return { success: true, data: paymentHistories }
  } catch (error) {
    console.error('Error fetching user payments:', error)
    return { success: false, error: 'Failed to fetch payment history' }
  }
}

export async function getProfileStats(email: string): Promise<DataAccessResult<ProfileStats>> {
  try {

    const user = await prisma.user.findUnique({
      where: { email: email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Get all counts in parallel
    const [
      totalEnrollments,
      completedCourses,
      totalCertificates,
      totalPayments,
      completedPayments,
      overdueItems
    ] = await Promise.all([
      prisma.enrollment.count({
        where: { userId: user.id }
      }),
      prisma.enrollment.count({
        where: { 
          userId: user.id,
          completedAt: { not: null }
        }
      }),
      prisma.certificate.count({
        where: { userId: user.id }
      }),
      prisma.payment.count({
        where: { userId: user.id }
      }),
      prisma.payment.findMany({
        where: { 
          userId: user.id,
          status: PaymentStatus.COMPLETED
        },
        select: { amount: true }
      }),
      prisma.user.findUnique({
        where: { id: user.id },
        select: { overDues: true }
      })
    ])

    const activeCourses = totalEnrollments - completedCourses
    const totalAmountPaid = completedPayments.reduce(
      (sum, payment) => sum + Number(payment.amount), 0
    )

    const stats: ProfileStats = {
      totalEnrollments,
      completedCourses,
      activeCourses,
      totalCertificates,
      totalPayments,
      totalAmountPaid,
      overdueItems: overdueItems?.overDues || 0
    }

    return { success: true, data: stats }
  } catch (error) {
    console.error('Error fetching profile stats:', error)
    return { success: false, error: 'Failed to fetch profile statistics' }
  }
}

export async function getCompleteProfileData(email: string): Promise<DataAccessResult<ProfileData>> {
 
  try {
    const [userResult, enrollmentsResult, certificatesResult, paymentsResult, statsResult] = 
      await Promise.all([
        getUserProfile(email),
        getUserEnrollments(email),
        getUserCertificates(email),
        getUserPayments(email),
        getProfileStats(email)
      ])

    if (!userResult.success) {
      return { success: false, error: userResult.error }
    }

    if (!enrollmentsResult.success) {
      return { success: false, error: enrollmentsResult.error }
    }

    if (!certificatesResult.success) {
      return { success: false, error: certificatesResult.error }
    }

    if (!paymentsResult.success) {
      return { success: false, error: paymentsResult.error }
    }

    if (!statsResult.success) {
      return { success: false, error: statsResult.error }
    }

    const profileData: ProfileData = {
      user: userResult.data!,
      enrollments: enrollmentsResult.data!,
      certificates: certificatesResult.data!,
      payments: paymentsResult.data!,
      stats: statsResult.data!
    }

    return { success: true, data: profileData }
  } catch (error) {
    console.error('Error fetching complete profile data:', error)
    return { success: false, error: 'Failed to fetch profile data' }
  }
} 


export async function updateUserProfile(updates: Partial<UserProfile>): Promise<DataAccessResult<UserProfile>> {
  const {user:{id}, error} = await getUserDetails()
  if (error) {
    return { success: false, error: error }
  }
  try {
    const user = await prisma.user.update({
      where: { id: id },
      data: updates
    })
    revalidatePath('/dashboard/profile')
    return { success: true, data: user }
  } 
  catch (error) {
    console.error('Error updating user profile:', error)
    return { success: false, error: 'Failed to update profile' }
  }
}