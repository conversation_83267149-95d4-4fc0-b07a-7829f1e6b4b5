import { Course as PrismaCourse, Prisma, CourseLevel as PrismaCourseLevel, AssignmentSubmission, SubmissionStatus} from "@prisma/client"

export type Course = PrismaCourse

export type CourseLevel = PrismaCourseLevel;

export type CourseDetails = Prisma.CourseGetPayload<{
    include: {
        modules: {
            include: {
                lessons: {
                    include: {
                        video: true
                    }
                },
                assignments: true
            }
        }
    }
}>;

export type CourseStat = Course & {
    _count: {
        modules: number
        applications: number
    }
}

export type Module = Prisma.ModuleGetPayload<{
    include: {
        lessons: {
            include: {
                video: true
            }
        },
        assignments: true
    }
}>;

export type Lesson = Prisma.LessonGetPayload<{
    include: {
        video: true
    }
}>;

export type Assignment = Prisma.AssignmentGetPayload<{}>;


export type AccessableCourseDetails = Omit<CourseDetails, 'modules'> & {
    modules: Array<Omit<Module, 'lessons' | 'assignments'> & {
        lessons: Array<Lesson & {
            completed: boolean
        }>
        assignments: Array<Assignment & {
            submitted: boolean
            submission: Partial<AssignmentSubmission> | null
        }>
    }>
};

import { Video } from '@prisma/client'

export interface SelectedLesson {
  id: string
  title: string
  video: Video | null
}

export interface SelectedAssignment {
  id: string
  title: string
}