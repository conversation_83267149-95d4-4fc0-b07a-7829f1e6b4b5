'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { Check, X, MessageCircle, Calendar, Search, Filter } from 'lucide-react'
import { FeedbackSessionWithDetails } from '@/types/feedback'
import { SessionStatus } from '@prisma/client'
import { updateSessionStatus, addSessionNotes } from '@/data-access/feedback/admin'

interface AdminFeedbackDashboardProps {
  initialSessions: FeedbackSessionWithDetails[]
}

export default function AdminFeedbackDashboard({ initialSessions }: AdminFeedbackDashboardProps) {
  const [sessions, setSessions] = useState<FeedbackSessionWithDetails[]>(initialSessions)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<SessionStatus | 'ALL'>('ALL')
  const [selectedSession, setSelectedSession] = useState<FeedbackSessionWithDetails | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [notes, setNotes] = useState('')
  const [error, setError] = useState<string | null>(null)

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = searchTerm === '' || 
      session.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.assignment?.title?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'ALL' || session.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleStatusChange = async (sessionId: string, newStatus: SessionStatus) => {
    setIsUpdating(true)
    setError(null)
    
    try {
      const result = await updateSessionStatus(sessionId, newStatus)
      
      if (result.success) {
        setSessions(prev => prev.map(session => 
          session.id === sessionId 
            ? { ...session, status: newStatus } 
            : session
        ))
      } else {
        setError(result.error || 'Failed to update status')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleNotesSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedSession) return
    
    setIsUpdating(true)
    setError(null)
    
    try {
      const result = await addSessionNotes(selectedSession.id, notes)
      
      if (result.success) {
        setSessions(prev => prev.map(session => 
          session.id === selectedSession.id 
            ? { ...session, notes } 
            : session
        ))
        setSelectedSession(null)
      } else {
        setError(result.error || 'Failed to add notes')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsUpdating(false)
    }
  }

  const getStatusBadge = (status: SessionStatus) => {
    switch (status) {
      case SessionStatus.SCHEDULED:
        return <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Scheduled</span>
      case SessionStatus.COMPLETED:
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Completed</span>
      case SessionStatus.CANCELLED:
        return <span className="px-2 py-1 text-xs rounded-full bg-amber-100 text-amber-800">Cancelled</span>
      case SessionStatus.NO_SHOW:
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">No Show</span>
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Feedback Sessions</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
          {error}
        </div>
      )}
      
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search by student, course, or assignment..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm"
          />
        </div>
        
        <div className="sm:w-48">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter className="h-4 w-4 text-gray-400" />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as SessionStatus | 'ALL')}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm"
            >
              <option value="ALL">All Statuses</option>
              <option value={SessionStatus.SCHEDULED}>Scheduled</option>
              <option value={SessionStatus.COMPLETED}>Completed</option>
              <option value={SessionStatus.CANCELLED}>Cancelled</option>
              <option value={SessionStatus.NO_SHOW}>No Show</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Sessions Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignment</th>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredSessions.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                  No feedback sessions found
                </td>
              </tr>
            ) : (
              filteredSessions.map(session => (
                <tr key={session.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium text-gray-900">{session.user?.name}</span>
                      <span className="text-sm text-gray-500">{session.user?.email}</span>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{session.course.name}</span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {session.assignment?.title || 'General Feedback'}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {format(new Date(session.scheduledAt), 'MMM d, yyyy')}
                    </span>
                    <br />
                    <span className="text-sm text-gray-500">
                      {format(new Date(session.scheduledAt), 'h:mm a')}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {getStatusBadge(session.status)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleStatusChange(session.id, SessionStatus.COMPLETED)}
                        disabled={isUpdating || session.status === SessionStatus.COMPLETED}
                        className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Mark as Completed"
                      >
                        <Check className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleStatusChange(session.id, SessionStatus.NO_SHOW)}
                        disabled={isUpdating || session.status === SessionStatus.NO_SHOW}
                        className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Mark as No Show"
                      >
                        <X className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedSession(session)
                          setNotes(session.notes || '')
                        }}
                        disabled={isUpdating}
                        className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Add Notes"
                      >
                        <MessageCircle className="h-5 w-5" />
                      </button>
                      {session.calEventId && (
                        <a
                          href={`https://cal.com/bookings/${session.calEventId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-indigo-600 hover:text-indigo-800"
                          title="View in Cal.com"
                        >
                          <Calendar className="h-5 w-5" />
                        </a>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Notes Modal */}
      {selectedSession && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Add Notes for {selectedSession.user?.name}
            </h3>
            
            <form onSubmit={handleNotesSubmit}>
              <div className="mb-4">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Feedback Notes
                </label>
                <textarea
                  id="notes"
                  rows={5}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter feedback notes here..."
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setSelectedSession(null)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isUpdating}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? 'Saving...' : 'Save Notes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
} 