'use client';

import { useState } from 'react';
import { Plus, List } from 'lucide-react';
import { Music } from '@prisma/client';

import AddMusicForm from '@/components/Admin/SingAlong/AddMusicForm';
import MusicLibrary from '@/components/Admin/SingAlong/MusicLibrary/MusicLibrary';

type TabType = 'add-music' | 'manage-music';

interface AdminDashboardClientProps {
  initialSongs: Music[];
  searchParams: {
    search?: string;
    genre?: string;
    difficulty?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default function AdminDashboardClient({ initialSongs, searchParams }: AdminDashboardClientProps) {
  const [activeTab, setActiveTab] = useState<TabType>('manage-music');
  const [songs, setSongs] = useState<Music[]>(initialSongs);

  const tabs = [
    { id: 'manage-music' as TabType, label: 'Music Library', icon: List },
    { id: 'add-music' as TabType, label: 'Add Music', icon: Plus },
  ];

  return (
    <div className="space-y-6">
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-6 py-3 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div>
        {activeTab === 'add-music' && (
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200 bg-white">
              <h2 className="text-xl font-semibold text-gray-900">Add New Song</h2>
              <p className="text-gray-600 mt-1">Upload and configure a new song for the karaoke library</p>
            </div>
            <div className="p-6">
              <AddMusicForm />
            </div>
          </div>
        )}

        {activeTab === 'manage-music' && (
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200 bg-white">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Music Library</h2>
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {songs.length} song{songs.length !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
            <div className="p-6">
              <MusicLibrary songs={songs} onSongsUpdate={setSongs} searchParams={searchParams} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 