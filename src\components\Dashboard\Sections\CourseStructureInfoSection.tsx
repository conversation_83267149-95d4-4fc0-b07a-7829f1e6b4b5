// app/components/sections/CourseStructureInfoSection.tsx
import { CheckCircle2, FileText, Users, BarChart3, Lightbulb } from 'lucide-react';

export default function CourseStructureInfoSection() {
  return (
    <section className="mb-16 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 md:p-10 border border-blue-100">
      <div className="flex items-center mb-6">
        <Lightbulb size={32} className="mr-3 text-blue-600" />
        <h2 className="text-3xl font-bold text-slate-800">Course Structure & Progression</h2>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        <div className="lg:w-1/2">
          <h3 className="text-xl font-semibold text-slate-800 mb-3 flex items-center">
            <BarChart3 size={22} className="mr-2 text-blue-500" />
            How Our Courses Work
          </h3>
          <p className="text-slate-600 mb-6 text-base leading-relaxed">
            Each course is meticulously designed with structured modules, comprising engaging video lessons.
            You'll progress sequentially, with practical assignments and valuable feedback sessions at key milestones to ensure comprehensive learning.
          </p>

          <div className="bg-white rounded-xl shadow-md p-6 border border-slate-200">
            <h4 className="font-semibold text-slate-800 mb-3 text-lg">Progression Requirements:</h4>
            <ul className="space-y-3 text-sm text-slate-600">
              {[
                "Complete 70% of each video lesson before unlocking the next.",
                "Submit all required assignments for each module.",
                "Attend scheduled feedback sessions with your instructor."
              ].map((item, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2.5 flex-shrink-0 mt-0.5" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="lg:w-1/2">
          <h3 className="text-xl font-semibold text-slate-800 mb-3 flex items-center">
            <LayersIcon className="mr-2 text-blue-500" />
            Example Course: Carnatic Vocals Foundation
          </h3>
          <div className="bg-white rounded-xl shadow-md border border-slate-200 overflow-hidden">
            <div className="border-b border-slate-200 p-5 bg-white">
              <h4 className="font-semibold text-blue-700 text-lg">Module 1: Introduction to Carnatic Music</h4>
            </div>
            <div className="p-5 space-y-2">
              <ul className="space-y-1.5 text-sm text-slate-700">
                {["History and Evolution", "Basic Terminology", "Introduction to Swaras (Notes)"].map((lesson, i) => (
                  <li key={i} className="flex items-center">
                    <span className="h-2 w-2 rounded-full bg-white0 mr-2.5"></span>
                    {lesson}
                  </li>
                ))}
              </ul>
            </div>

            <div className="border-t border-slate-200 p-5 bg-white">
              <h5 className="font-semibold text-blue-700 text-lg">Module 2: Basic Vocal Techniques</h5>
            </div>
            <div className="p-5 space-y-2">
              <ul className="space-y-1.5 text-sm text-slate-700">
                {["Voice Training Basics", "Practicing Swaras", "Introduction to Alankaras (Patterns)"].map((lesson, i) => (
                  <li key={i} className="flex items-center">
                    <span className="h-2 w-2 rounded-full bg-white0 mr-2.5"></span>
                    {lesson}
                  </li>
                ))}
                <li className="flex items-center text-blue-600 font-medium pt-1">
                  <FileText className="h-4 w-4 mr-2" />
                  Assignment: Basic Swara Practice Recording
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Helper icon as Layers might conflict with lucide-react's Layers
const LayersIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
    <polyline points="2 17 12 22 22 17"></polyline>
    <polyline points="2 12 12 17 22 12"></polyline>
  </svg>
);