'use client';

import { useState } from 'react';
import { Upload, X, FileText, Music, AlertCircle, CheckCircle2, Loader2 } from 'lucide-react';
import { CourseLevel } from '@prisma/client';
import { addSong } from '@/data-access/sing-along/admin-singalong';


export default function AddMusicForm() {
  const [formData, setFormData] = useState({
    title: '',
    genre: '',
    duration: '',
    difficulty: CourseLevel.BEGINNER
  });
  
  const [musicFile, setMusicFile] = useState<File | null>(null);
  const [lrcFile, setLrcFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };

  const handleFileSelect = (type: 'music' | 'lrc', file: File) => {
    if (type === 'music') {
      if (!file.type.startsWith('audio/')) {
        setError('Please select a valid audio file');
        return;
      }
      setMusicFile(file);
      
      // Auto-fill title if empty
      if (!formData.title) {
        const fileName = file.name.replace(/\.[^/.]+$/, '');
        setFormData(prev => ({ ...prev, title: fileName }));
      }
    } else {
      if (!file.name.endsWith('.lrc') && file.type !== 'text/plain') {
        setError('Please select a valid LRC file');
        return;
      }
      setLrcFile(file);
    }
    setError(null);
  };

  const removeFile = (type: 'music' | 'lrc') => {
    if (type === 'music') {
      setMusicFile(null);
    } else {
      setLrcFile(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.genre || !formData.duration || !musicFile) {
      setError('Please fill in all required fields and select a music file');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Upload music file
      const musicFormData = new FormData();
      musicFormData.append('file', musicFile);
      musicFormData.append('fileName', `music/${formData.title}.mp3`);
      musicFormData.append('mimeType', 'audio/mpeg');

      const musicFileResponse = await fetch('/api/admin/upload', {
        method: 'POST',
        body: musicFormData
      });

      const musicResult = await musicFileResponse.json();
      if (!musicResult.success) {
        throw new Error('Failed to upload music file');
      }
  
      let lrcResult = null;
      if (lrcFile) {
        const lrcFormData = new FormData();
        lrcFormData.append('file', lrcFile);
        lrcFormData.append('fileName', `lyrics/${formData.title}.lrc`);
        lrcFormData.append('mimeType', 'text/plain');

        const lrcFileResponse = await fetch('/api/admin/upload', {
          method: 'POST',
          body: lrcFormData
        });

        const lrcResult = await lrcFileResponse.json();
        if (!lrcResult.success) {
          throw new Error('Failed to upload LRC file');
        }
      }

      // Create song data
      const songFormData = new FormData();
      songFormData.append('title', formData.title);
      songFormData.append('genre', formData.genre);
      songFormData.append('duration', formData.duration);
      songFormData.append('difficulty', formData.difficulty);

      const result = await addSong(songFormData);

      if (result.success) {
        setSuccess('Song added successfully!');
        
        // Reset form
        setFormData({
          title: '',
          genre: '',
          duration: '',
          difficulty: CourseLevel.BEGINNER
        });
        setMusicFile(null);
        setLrcFile(null);
      } else {
        setError(result.error || 'Failed to add song');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      {/* Messages */}
      {error && (
        <div className="flex items-center gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="flex items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle2 className="h-5 w-5 text-green-600 flex-shrink-0" />
          <p className="text-sm text-green-700">{success}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Song Information */}
        <div className="space-y-6">
          <h2 className="text-lg font-semibold text-gray-900">Song Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Song title"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="genre" className="block text-sm font-medium text-gray-700">
                Genre *
              </label>
              <input
                type="text"
                id="genre"
                name="genre"
                value={formData.genre}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Pop, Rock, Classical"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="duration" className="block text-sm font-medium text-gray-700">
                Duration *
              </label>
              <input
                type="text"
                id="duration"
                name="duration"
                value={formData.duration}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="3:22"
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700">
                Difficulty *
              </label>
              <select
                id="difficulty"
                name="difficulty"
                value={formData.difficulty}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value={CourseLevel.BEGINNER}>Beginner</option>
                <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
                <option value={CourseLevel.ADVANCED}>Advanced</option>
              </select>
            </div>
          </div>
        </div>

        {/* File Uploads */}
        <div className="space-y-6">
          <h2 className="text-lg font-semibold text-gray-900">Files</h2>

          {/* Music File */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Music File *
            </label>
            
            {musicFile ? (
              <div className="flex items-center justify-between p-4 bg-white border border-blue-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <Music className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{musicFile.name}</p>
                    <p className="text-xs text-gray-600">{formatFileSize(musicFile.size)}</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile('music')}
                  className="p-1 hover:bg-red-100 rounded"
                >
                  <X className="h-4 w-4 text-gray-500 hover:text-red-600" />
                </button>
              </div>
            ) : (
              <div>
                <input
                  type="file"
                  accept="audio/mpeg"
                  onChange={(e) => e.target.files?.[0] && handleFileSelect('music', e.target.files[0])}
                  className="hidden"
                  id="music-upload"
                />
                <label
                  htmlFor="music-upload"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  Choose Audio File
                </label>
                <p className="text-xs text-gray-500 mt-1">MP3, WAV, M4A (Max 50MB)</p>
              </div>
            )}
          </div>

          {/* LRC File */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Lyrics File (Optional)
            </label>
            
            {lrcFile ? (
              <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{lrcFile.name}</p>
                    <p className="text-xs text-gray-600">{formatFileSize(lrcFile.size)}</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile('lrc')}
                  className="p-1 hover:bg-red-100 rounded"
                >
                  <X className="h-4 w-4 text-gray-500 hover:text-red-600" />
                </button>
              </div>
            ) : (
              <div>
                <input
                  type="file"
                  accept=".lrc,text/plain"
                  onChange={(e) => e.target.files?.[0] && handleFileSelect('lrc', e.target.files[0])}
                  className="hidden"
                  id="lrc-upload"
                />
                <label
                  htmlFor="lrc-upload"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 cursor-pointer transition-colors"
                >
                  <FileText className="h-4 w-4" />
                  Choose LRC File
                </label>
                <p className="text-xs text-gray-500 mt-1">LRC format for synchronized lyrics</p>
              </div>
            )}
          </div>
        </div>

        {/* Submit */}
        <div className="pt-6 border-t border-gray-200">
          <button
            type="submit"
            disabled={isSubmitting || !musicFile || !formData.title}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              isSubmitting || !musicFile || !formData.title
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Adding Song...
              </div>
            ) : (
              'Add Song'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}