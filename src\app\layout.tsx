import type { Metadata } from "next";
import { Inter, Open_Sans, Poppins } from "next/font/google";
import "./globals.css";
import { AuthInitializer } from "@/components/Auth/AuthInitialiser";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const openSans = Open_Sans({
  subsets: ["latin"],
  variable: "--font-open-sans",
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Dhwanini Music Academy",
  description: "Learn music with expert instructors",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} bg-white`}>
        <AuthInitializer />
        {children}
      </body>
    </html>
  );
}