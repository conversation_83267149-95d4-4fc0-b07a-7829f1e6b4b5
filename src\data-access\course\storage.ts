"use server"

import { prisma } from "@/utils/prisma/prisma"
import { createClient } from "@/utils/supabase/server"


const PUBLIC_BUCKET = process.env.NEXT_PUBLIC_SUPABASE_PUBLIC_BUCKET || 'dhwanini-public'

export const getThumbnailUrl = async (courseId: string, path: string): Promise<{url: string, error: string}> => {
    try {
        const supabase = await createClient()
        const {data} = await supabase.storage.from(PUBLIC_BUCKET).getPublicUrl(path)
        return {url: data.publicUrl, error: ''}
    } 
    catch (error) {
        console.error('Error getting thumbnail URL:', error)
        return {url: '', error: 'Error getting thumbnail URL'}
    }
}
  
// Upload thumbnail to Supabase
export const uploadThumbnail = async (courseId: string, file: File): Promise<{success: boolean, url: string, error: string}> => {
    try {
        const supabase = await createClient()
        
        const {data,error} = await supabase.storage.from(PUBLIC_BUCKET).upload(`thumbnails/${courseId}`, file, {
            upsert: true
        })
        if (error) {
            console.error('Error uploading thumbnail:', error)
            return {success: false, url: '', error: 'Error uploading thumbnail'}
        }
        await prisma.course.update({
            where: {id: courseId},
            data: {thumbnailPath: data.path}
        })
        const {data: {publicUrl}} = supabase.storage.from(PUBLIC_BUCKET).getPublicUrl(data.path)
        return {success: true, url: publicUrl, error: ''}
    }
    catch (error) {
        console.error('Error uploading thumbnail:', error)
        return {success: false, url: '', error: 'Error uploading thumbnail'}
    }
}
  
// Delete thumbnail from Supabase
export const deleteThumbnail = async (courseId: string, path: string): Promise<{success: boolean, error: string}> => {
    try {
        const supabase = await createClient()
        const {error} = await supabase.storage.from(PUBLIC_BUCKET).remove([path])
        if (error) {
            console.error('Error deleting thumbnail:', error)
            return {success: false, error: 'Error deleting thumbnail'}
        }
        await prisma.course.update({
            where: {id: courseId},
            data: {thumbnailPath: null}
        })
        return {success: true, error: ''}
    }
    catch (error) {
        console.error('Error deleting thumbnail:', error)
        return {success: false, error: 'Error deleting thumbnail'}
    }
}

// ===== VIDEO FUNCTIONS =====

// Get video URL from Supabase using lesson ID
export const getVideoUrl = async (lessonId: string): Promise<{url: string, error: string}> => {
    try {
        const supabase = await createClient()
        const {data} = await supabase.storage.from(PUBLIC_BUCKET).getPublicUrl(`videos/lessons/${lessonId}`)
        return {url: data.publicUrl, error: ''}
    } 
    catch (error) {
        console.error('Error getting video URL:', error)
        return {url: '', error: 'Error getting video URL'}
    }
}

// Upload video to Supabase
export const uploadVideo = async (lessonId: string, file: File): Promise<{success: boolean, url: string, error: string}> => {
    try {
        const supabase = await createClient()
        
        // Generate unique filename with timestamp
        const fileExtension = file.name.split('.').pop()
        const fileName = `videos/lessons/${lessonId}.${fileExtension}`
        
        const {data, error} = await supabase.storage.from(PUBLIC_BUCKET).upload(fileName, file, {
            upsert: true // Allow overwriting existing files
        })
        
        if (error) {
            console.error('Error uploading video:', error)
            return {success: false, url: '', error: 'Error uploading video'}
        }
        
        const {data: {publicUrl}} = supabase.storage.from(PUBLIC_BUCKET).getPublicUrl(fileName)
        return {success: true, url: publicUrl, error: ''}
    }
    catch (error) {
        console.error('Error uploading video:', error)
        return {success: false, url: '', error: 'Error uploading video'}
    }
}

// Delete video from Supabase
export const deleteVideo = async (lessonId: string): Promise<{success: boolean, error: string}> => {
    try {
        const supabase = await createClient()
        
        // List all files that start with the lesson ID to handle different extensions
        const {data: files, error: listError} = await supabase.storage
            .from(PUBLIC_BUCKET)
            .list('videos/lessons', {
                search: lessonId
            })
            
        if (listError) {
            console.error('Error listing video files:', listError)
            return {success: false, error: 'Error finding video files'}
        }
        
        // Delete all matching files
        if (files && files.length > 0) {
            const filesToDelete = files
                .filter(file => file.name.startsWith(lessonId))
                .map(file => `videos/lessons/${file.name}`)
                
            const {error} = await supabase.storage.from(PUBLIC_BUCKET).remove(filesToDelete)
            if (error) {
                console.error('Error deleting video:', error)
                return {success: false, error: 'Error deleting video'}
            }
        }
        
        return {success: true, error: ''}
    }
    catch (error) {
        console.error('Error deleting video:', error)
        return {success: false, error: 'Error deleting video'}
    }
}

  
const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-sing-along';

export const getSignedUrl = async (fileName: string): Promise<{signedUrl: string | null, error: string | null}> => {
    try {
        const supabase = await createClient();
        const { data, error } = await supabase.storage.from(BUCKET_NAME).createSignedUrl(fileName, 60 * 60);
        if (error) {
            throw error;
        }
        return {signedUrl: data.signedUrl, error: null};
    }
    catch (error) {
        console.error(`Error getting signed url for ${fileName} in bucket ${BUCKET_NAME}`, error);
        return {signedUrl: null, error: error instanceof Error ? error.message : 'Unknown error'};
    }
}


