'use client';

import { useState, useTransition } from 'react';
import { ApplicationWithDetails } from '@/types/auditions';
import { updateApplication } from '@/data-access/auditions/auditions';
import { 
  Eye, 
  Edit, 
  Calendar, 
  User, 
  GraduationCap, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  Download
} from 'lucide-react';
import Link from 'next/link';

interface AuditionListProps {
  applications: ApplicationWithDetails[];
  onApplicationUpdate: (application: ApplicationWithDetails) => void;
  isLoading?: boolean;
}

export function AuditionList({ 
  applications, 
  onApplicationUpdate, 
  isLoading 
}: AuditionListProps) {
  const [isPending, startTransition] = useTransition();
  const [expandedApplications, setExpandedApplications] = useState<Set<string>>(new Set());

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'UNDER_REVIEW':
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'SUBMITTED':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded";
    
    switch (status) {
      case 'APPROVED':
        return `${baseClasses} bg-green-50 text-green-700 border border-green-200`;
      case 'REJECTED':
        return `${baseClasses} bg-red-50 text-red-700 border border-red-200`;
      case 'UNDER_REVIEW':
        return `${baseClasses} bg-blue-50 text-blue-700 border border-blue-200`;
      case 'SUBMITTED':
        return `${baseClasses} bg-gray-50 text-gray-700 border border-gray-200`;
      case 'PENDING':
        return `${baseClasses} bg-yellow-50 text-yellow-700 border border-yellow-200`;
      default:
        return `${baseClasses} bg-gray-50 text-gray-700 border border-gray-200`;
    }
  };

  const formatDate = (date: string | Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const handleStatusUpdate = async (applicationId: string, newStatus: string) => {
    startTransition(async () => {
      const { data: updatedApplication, error } = await updateApplication(applicationId, {
        auditionStatus: newStatus,
      });

      if (updatedApplication && !error) {
        onApplicationUpdate(updatedApplication);
      }
    });
  };

  const toggleExpanded = (applicationId: string) => {
    setExpandedApplications(prev => {
      const newSet = new Set(prev);
      if (newSet.has(applicationId)) {
        newSet.delete(applicationId);
      } else {
        newSet.add(applicationId);
      }
      return newSet;
    });
  };

  if (applications.length === 0) {
    return (
      <div className="text-center py-12">
        <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
        <p className="text-gray-500">No applications match your current filters.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4 text-gray-700">
      {applications.map((application) => {
        const isExpanded = expandedApplications.has(application.id);
        
        return (
          <div 
            key={application.id} 
            className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
          >
            {/* Application Card Header */}
            <div className="p-6 bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <User className="h-10 w-10 text-gray-400 bg-gray-100 rounded-full p-2" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {application.user.name}
                      </h3>
                      <div className={getStatusBadge(application.auditionStatus)}>
                        {getStatusIcon(application.auditionStatus)}
                        {application.auditionStatus.replace('_', ' ')}
                      </div>
                    </div>
                    
                    <div className="mt-1 flex items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {application.user.email}
                      </span>
                      <span className="flex items-center gap-1">
                        <GraduationCap className="h-4 w-4" />
                        {application.course.name}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(application.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">

                  {/* Quick Status Actions */}
                  <div className="flex items-center gap-1">
                    {application.auditionStatus !== 'APPROVED' && (
                      <button
                        onClick={() => handleStatusUpdate(application.id, 'APPROVED')}
                        disabled={isLoading || isPending}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors disabled:opacity-50"
                        title="Approve"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </button>
                    )}
                    
                    {application.auditionStatus !== 'REJECTED' && (
                      <button
                        onClick={() => handleStatusUpdate(application.id, 'REJECTED')}
                        disabled={isLoading || isPending}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                        title="Reject"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-1">
                    <Link
                      href={`/admin/admissions/${application.id}`}
                      className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                      title="View details"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    
                    <button
                      onClick={() => toggleExpanded(application.id)}
                      className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                      title="Toggle details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Expanded Details */}
            {isExpanded && (
              <div className="px-6 pb-6 bg-gray-50 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  {/* User Details */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Student Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Email:</span>
                        <span className="text-gray-900">{application.user.email}</span>
                      </div>
                      {application.user.phone && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Phone:</span>
                          <span className="text-gray-900">{application.user.phone}</span>
                        </div>
                      )}
                      {application.user.country && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Country:</span>
                          <span className="text-gray-900">{application.user.country}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Course Details */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Course Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Course:</span>
                        <span className="text-gray-900">{application.course.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Level:</span>
                        <span className="text-gray-900">{application.course.level}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Price:</span>
                        <span className="text-gray-900">
                          {application.course.currency} {application.course.price}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Audition Fee:</span>
                        <span className="text-gray-900">
                          {application.course.currency} {application.course.auditionFee}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Feedback Section */}
                {application.feedback && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Feedback</h4>
                    <div className="bg-white p-3 rounded-lg border border-gray-200">
                      <p className="text-sm text-gray-700">{application.feedback}</p>
                    </div>
                  </div>
                )}

                {/* Payment Status */}
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Status</h4>
                  <div className={getStatusBadge(application.enrollmentPaymentStatus)}>
                    {application.enrollmentPaymentStatus.replace('_', ' ')}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
} 