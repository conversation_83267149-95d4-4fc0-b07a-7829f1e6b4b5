'use client'

import { useState, useMemo } from 'react'
import { UserAssignment, AssignmentStats } from '@/types/assignment'
import { SubmissionStatus } from '@prisma/client'
import { AssignmentCard } from '@/components/Dashboard/Assignments/AssignmentCard'
import { AssignmentStatsCards } from '@/components/Dashboard/Assignments/AssignmentStats'
import { AssignmentFilters, FilterStatus } from '@/components/Dashboard/Assignments/AssignmentFilters'

interface AssignmentDashboardProps {
  initialAssignments: UserAssignment[]
  stats: AssignmentStats
  courses: Array<{ id: string; name: string }>
}

export function AssignmentDashboard({ 
  initialAssignments, 
  stats, 
  courses 
}: AssignmentDashboardProps) {
  const [filterStatus, setFilterStatus] = useState<FilterStatus>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCourse, setSelectedCourse] = useState('')

  const filteredAssignments = useMemo(() => {
    let filtered = initialAssignments

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(assignment => {
        switch (filterStatus) {
          case 'pending':
            return !assignment.submission && !assignment.isOverdue
          case 'submitted':
            return assignment.submission?.status === SubmissionStatus.SUBMITTED
          case 'graded':
            return assignment.submission?.status === SubmissionStatus.GRADED
          case 'overdue':
            return assignment.isOverdue
          case 'needs_revision':
            return assignment.submission?.status === SubmissionStatus.NEEDS_REVISION
          default:
            return true
        }
      })
    }

    // Filter by course
    if (selectedCourse) {
      filtered = filtered.filter(assignment => 
        assignment.module.course.id === selectedCourse
      )
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(assignment =>
        assignment.title.toLowerCase().includes(term) ||
        assignment.description?.toLowerCase().includes(term) ||
        assignment.module.title.toLowerCase().includes(term) ||
        assignment.module.course.name.toLowerCase().includes(term)
      )
    }

    return filtered
  }, [initialAssignments, filterStatus, selectedCourse, searchTerm])

  const handleFilterChange = (status: FilterStatus, search: string) => {
    setFilterStatus(status)
    setSearchTerm(search)
  }

  const handleCourseChange = (courseId: string) => {
    setSelectedCourse(courseId)
  }

  // Separate assignments by urgency
  const urgentAssignments = filteredAssignments.filter(a => 
    (a.daysUntilDeadline <= 3 && a.daysUntilDeadline >= 0 && !a.submission) || a.isOverdue
  )
  const otherAssignments = filteredAssignments.filter(a => 
    !((a.daysUntilDeadline <= 3 && a.daysUntilDeadline >= 0 && !a.submission) || a.isOverdue)
  )

  return (
    <div className="space-y-8 text-gray-700">
      {/* Stats Cards */}
      <AssignmentStatsCards stats={stats} />

      {/* Filters */}
      <AssignmentFilters
        onFilterChange={handleFilterChange}
        courses={courses}
        selectedCourse={selectedCourse}
        onCourseChange={handleCourseChange}
      />

      {/* Assignments */}
      <div className="space-y-8">
        {/* Urgent Assignments */}
        {urgentAssignments.length > 0 && (
          <div>
            <div className="flex items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Urgent Assignments
              </h2>
              <span className="ml-3 px-2 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full">
                {urgentAssignments.length}
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {urgentAssignments.map((assignment) => (
                <AssignmentCard key={assignment.id} assignment={assignment} />
              ))}
            </div>
          </div>
        )}

        {/* Other Assignments */}
        {otherAssignments.length > 0 && (
          <div>
            <div className="flex items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                {urgentAssignments.length > 0 ? 'Other Assignments' : 'All Assignments'}
              </h2>
              <span className="ml-3 px-2 py-1 bg-gray-100 text-gray-800 text-sm font-medium rounded-full">
                {otherAssignments.length}
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {otherAssignments.map((assignment) => (
                <AssignmentCard key={assignment.id} assignment={assignment} />
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {filteredAssignments.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No assignments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || filterStatus !== 'all' || selectedCourse
                ? 'Try adjusting your filters to see more assignments.'
                : 'You don\'t have any assignments yet.'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
} 