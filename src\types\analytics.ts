import { SubmissionStatus } from '@prisma/client'

export interface CourseAnalytics {
  id: string
  name: string
  description: string
  level: string
  price: number | null
  isPublished: boolean
  enrollmentCount: number
  completionRate: number
  averageProgress: number
  activeUsers: number
  totalModules: number
  totalLessons: number
  totalAssignments: number
  revenueGenerated: number
  createdAt: Date
  updatedAt: Date
}

export interface UserAnalytics {
  id: string
  name: string
  email: string
  enrolledAt: Date
  progressPercentage: number
  completedModules: number
  totalModules: number
  completedLessons: number
  totalLessons: number
  submittedAssignments: number
  totalAssignments: number
  overdueCount: number
  lastActivity: Date | null
  isActive: boolean
  feedbackSessions: {
    total: number
    completed: number
    scheduled: number
  }
}

export interface AnalyticsStats {
  totalCourses: number
  totalEnrollments: number
  totalActiveUsers: number
  totalRevenue: number
  averageCompletionRate: number
  monthlyGrowth: number
}

export interface CourseDetailAnalytics {
  course: CourseAnalytics
  users: UserAnalytics[]
  moduleStats: Array<{
    id: string
    title: string
    order: number
    completionRate: number
    averageTimeSpent: number
    lessonCount: number
    assignmentCount: number
  }>
  recentActivity: Array<{
    type: 'enrollment' | 'completion' | 'assignment' | 'feedback'
    userId: string
    userName: string
    description: string
    timestamp: Date
  }>
}

export interface AssignmentSubmissionDetail {
  id: string | null // null for unsubmitted assignments
  assignmentId: string
  assignmentTitle: string
  assignmentDescription: string | null
  assignmentDeadline: Date
  assignmentPath: string | null
  moduleTitle: string
  moduleOrder: number
  status: SubmissionStatus | null
  feedback: string | null
  submittedAt: Date | null
  updatedAt: Date | null
  submissionPath: string | null
  hasSubmission: boolean
  isOverdue: boolean
}

export interface UserAssignmentAnalytics {
  user: {
    id: string
    name: string
    email: string
  }
  course: {
    id: string
    name: string
  }
  enrolledAt: Date
  submissions: AssignmentSubmissionDetail[]
  stats: {
    totalAssignments: number
    submitted: number
    graded: number
    needsRevision: number
    overdue: number
    pending: number
  }
} 