import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'worldmusiccentral.org',
        port: '',
        pathname: '/wp-content/uploads/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'bbymsakqowtrbpjqwkff.supabase.co',
        port: '',
        pathname: '/**',
      }
    ],
  },
  experimental: {
    useCache: true,
    reactCompiler: true
  }
};

export default nextConfig;
