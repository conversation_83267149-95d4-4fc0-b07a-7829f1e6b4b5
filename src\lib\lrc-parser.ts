import { LyricsLine } from "@/types/sing-along";

/**
 * Parse LRC file content into lyrics array
 * LRC format: [mm:ss.xx]Lyric text
 * Example: [00:12.34]This is a lyric line
 */
export function parseLrcContent(lrcContent: string): LyricsLine[] {
    const lines = lrcContent.split('\n');
    const lyrics: LyricsLine[] = [];
    
    // Regex to match LRC timestamp format [mm:ss.xx] or [mm:ss]
    const lrcRegex = /^\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\](.*)$/;
    
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;
        
        const match = trimmedLine.match(lrcRegex);
        if (match) {
            const minutes = parseInt(match[1], 10);
            const seconds = parseInt(match[2], 10);
            const milliseconds = match[3] ? parseInt(match[3].padEnd(3, '0'), 10) : 0;
            const text = match[4].trim();
            
            if (text) { // Only add non-empty lyrics
                const timeInSeconds = minutes * 60 + seconds + milliseconds / 1000;
                lyrics.push({
                    time: timeInSeconds,
                    text: text
                });
            }
        }
    }
    
    // Sort by time to ensure proper order
    return lyrics.sort((a, b) => a.time - b.time);
}

/**
 * Process an LRC file and return lyrics data
 * This function can be used when uploading LRC files
 */
export function processLrcFile(lrcContent: string): {
    lyrics: LyricsLine[];
    totalLines: number;
    duration: number;
    error: string | null;
} {
    try {
        const lyrics = parseLrcContent(lrcContent);
        
        if (lyrics.length === 0) {
            return {
                lyrics: [],
                totalLines: 0,
                duration: 0,
                error: 'No valid lyrics found in LRC file'
            };
        }
        
        const duration = lyrics[lyrics.length - 1].time;
        
        return {
            lyrics,
            totalLines: lyrics.length,
            duration,
            error: null
        };
    } catch (error) {
        return {
            lyrics: [],
            totalLines: 0,
            duration: 0,
            error: error instanceof Error ? error.message : 'Failed to process LRC file'
        };
    }
}