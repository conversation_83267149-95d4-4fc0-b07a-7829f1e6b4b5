'use client'

import { useState, useMemo } from 'react'
import Link from 'next/link'
import { 
  ChevronDown, 
  ChevronRight, 
  Play,
  Menu,
  X,
  CheckCircle,
  Clock,
  BookOpen,
  ArrowLeft,
  FileText,
  Lock
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CourseDetails, SelectedLesson } from '@/types/course'

interface CourseContentSidebarProps {
  course: CourseDetails | null
  selectedLesson: SelectedLesson | null
  selectedAssignment: { id: string; title: string } | null
  onLessonSelect: (lesson: { id: string; title: string }) => void
  onAssignmentSelect: (assignment: { id: string; title: string }) => void
  progress: {
    completedLessons: number
    totalLessons: number
    currentModule: number
    totalModules: number
  }
  isMobileSidebarOpen: boolean
  setIsMobileSidebarOpen: (open: boolean) => void
  completedLessons: Set<string>
  completedAssignments: Set<string>
  accessibleLessons: Set<string>
  accessibleAssignments: Set<string>
}

export default function CourseContentSidebar({
  course,
  selectedLesson,
  selectedAssignment,
  onLessonSelect,
  onAssignmentSelect,
  progress,
  isMobileSidebarOpen,
  setIsMobileSidebarOpen,
  completedLessons,
  completedAssignments,
  accessibleLessons,
  accessibleAssignments
}: CourseContentSidebarProps) {
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set())

  // Calculate actual progress
  const actualProgress = useMemo(() => {
    if (!course || !course.modules) {
      return {
        completedLessons: 0,
        totalLessons: 0,
        completedAssignments: 0,
        totalAssignments: 0,
        progressPercentage: 0
      }
    }

    const actualTotalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0)
    const actualTotalAssignments = course.modules.reduce((total, module) => total + (module.assignments?.length || 0), 0)
    const actualCompletedLessons = completedLessons.size
    const actualCompletedAssignments = completedAssignments.size

    const totalItems = actualTotalLessons + actualTotalAssignments
    const completedItems = actualCompletedLessons + actualCompletedAssignments
    const progressPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0

    return {
      completedLessons: actualCompletedLessons,
      totalLessons: actualTotalLessons,
      completedAssignments: actualCompletedAssignments,
      totalAssignments: actualTotalAssignments,
      progressPercentage
    }
  }, [course?.modules, completedLessons, completedAssignments])

  const toggleModule = (moduleId: string) => {
    const newExpanded = new Set(expandedModules)
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId)
    } else {
      newExpanded.add(moduleId)
    }
    setExpandedModules(newExpanded)
  }

  const handleLessonSelect = (lesson: { id: string; title: string }) => {
    if (accessibleLessons.has(lesson.id)) {
      onLessonSelect(lesson)
      setIsMobileSidebarOpen(false)
    }
  }

  const handleAssignmentSelect = (assignment: { id: string; title: string }) => {
    if (accessibleAssignments.has(assignment.id)) {
      onAssignmentSelect(assignment)
      setIsMobileSidebarOpen(false)
    }
  }

  const getLessonStatus = (lessonId: string) => {
    if (completedLessons.has(lessonId)) return 'completed'
    if (accessibleLessons.has(lessonId)) return 'accessible'
    return 'locked'
  }

  const getAssignmentStatus = (assignmentId: string) => {
    if (completedAssignments.has(assignmentId)) return 'completed'
    if (accessibleAssignments.has(assignmentId)) return 'accessible'
    return 'locked'
  }

  const getAssignmentSubmissionStatus = (assignment: any) => {
    if (!assignment.submission) {
      return { text: 'Not Submitted', color: 'text-gray-500' }
    }
    
    switch (assignment.submission.status) {
      case 'SUBMITTED':
        return { text: 'Under Review', color: 'text-blue-600' }
      case 'GRADED':
        return { text: 'Graded', color: 'text-green-600' }
      case 'NEEDS_REVISION':
        return { text: 'Needs Revision', color: 'text-amber-600' }
      case 'OVERDUE':
        return { text: 'Overdue', color: 'text-red-600' }
      default:
        return { text: 'Not Submitted', color: 'text-gray-500' }
    }
  }

  const SidebarContent = () => (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <Link 
          href="/dashboard/courses"
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to courses
        </Link>

        <h1 className="text-xl font-semibold text-gray-900 mb-4">
          {course?.name || 'Loading...'}
        </h1>
        
        {/* Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="font-medium text-gray-900">
              {actualProgress.progressPercentage}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${actualProgress.progressPercentage}%` }}
            />
          </div>
          
          <div className="text-xs text-gray-500">
            {actualProgress.completedLessons + actualProgress.completedAssignments} of {actualProgress.totalLessons + actualProgress.totalAssignments} completed
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {!course || !course.modules ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Loading content...</p>
            </div>
          </div>
        ) : course.modules.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No content available</p>
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {course.modules.map((module, moduleIndex) => (
              <div key={module.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleModule(module.id)}
                  className="w-full px-4 py-4 flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xs font-semibold text-gray-500 bg-gray-200 px-2 py-1 rounded">
                      {moduleIndex + 1}
                    </span>
                    <h3 className="text-sm font-medium text-gray-900 text-left">
                      {module.title}
                    </h3>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-xs text-gray-500">
                      {(module.lessons?.length || 0) + (module.assignments?.length || 0)} items
                    </span>
                    {expandedModules.has(module.id) ? (
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                </button>

                {expandedModules.has(module.id) && (
                  <div className="bg-white">
                    {/* Lessons */}
                    {module.lessons && module.lessons.length > 0 && (
                      <>
                        {module.lessons.map((lesson, lessonIndex) => {
                          const lessonStatus = getLessonStatus(lesson.id)
                          const isSelected = selectedLesson?.id === lesson.id
                          const isLocked = lessonStatus === 'locked'
                          const isCompleted = lessonStatus === 'completed'
                          
                          return (
                            <button
                              key={lesson.id}
                              onClick={() => handleLessonSelect({ id: lesson.id, title: lesson.title })}
                              disabled={isLocked}
                              className={cn(
                                "w-full px-6 py-4 flex items-center space-x-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0",
                                isSelected && "bg-blue-50 border-r-2 border-r-blue-600",
                                isLocked && "opacity-50 cursor-not-allowed"
                              )}
                            >
                              <div className="flex-shrink-0">
                                {isLocked ? (
                                  <Lock className="w-4 h-4 text-gray-400" />
                                ) : isCompleted ? (
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                ) : (
                                  <Play className="w-4 h-4 text-blue-600" />
                                )}
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <p className={cn(
                                  "text-sm font-medium truncate",
                                  isSelected ? "text-blue-900" : "text-gray-900",
                                  isLocked && "text-gray-500"
                                )}>
                                  {lessonIndex + 1}. {lesson.title}
                                </p>
                                <div className="flex items-center mt-1 text-xs text-gray-500">
                                  <Clock className="w-3 h-3 mr-1" />
                                  <span>15 min</span>
                                  {isCompleted && (
                                    <>
                                      <span className="mx-2">•</span>
                                      <span className="text-green-600">Complete</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </button>
                          )
                        })}
                      </>
                    )}

                    {/* Assignments */}
                    {module.assignments && module.assignments.length > 0 && (
                      <>
                        {module.assignments.map((assignment) => {
                          const assignmentStatus = getAssignmentStatus(assignment.id)
                          const isSelected = selectedAssignment?.id === assignment.id
                          const isLocked = assignmentStatus === 'locked'
                          const submissionStatus = getAssignmentSubmissionStatus(assignment)
                          
                          return (
                            <button
                              key={assignment.id}
                              onClick={() => handleAssignmentSelect({ id: assignment.id, title: assignment.title })}
                              disabled={isLocked}
                              className={cn(
                                "w-full px-6 py-4 flex items-center space-x-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0",
                                isSelected && "bg-purple-50 border-r-2 border-r-purple-600",
                                isLocked && "opacity-50 cursor-not-allowed"
                              )}
                            >
                              <div className="flex-shrink-0">
                                {isLocked ? (
                                  <Lock className="w-4 h-4 text-gray-400" />
                                ) : (
                                  <FileText className="w-4 h-4 text-purple-600" />
                                )}
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <p className={cn(
                                  "text-sm font-medium truncate",
                                  isSelected ? "text-purple-900" : "text-gray-900",
                                  isLocked && "text-gray-500"
                                )}>
                                  {assignment.title}
                                </p>
                                <div className="flex items-center mt-0.5 text-xs">
                                  <span className="text-gray-500">Assignment</span>
                                  <span className="mx-1 text-gray-500">•</span>
                                  <span className={submissionStatus.color}>
                                    {submissionStatus.text}
                                  </span>
                                </div>
                              </div>
                            </button>
                          )
                        })}
                      </>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsMobileSidebarOpen(true)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
          <h1 className="text-sm font-medium text-gray-900 truncate">
            {course?.name || 'Course'}
          </h1>
          <div className="w-9" />
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div className="lg:hidden fixed inset-0 z-50">
          <div 
            className="absolute inset-0 bg-white/25" 
            onClick={() => setIsMobileSidebarOpen(false)} 
          />
          <div className="absolute inset-y-0 left-0 w-80 bg-white shadow-xl">
            {/* Mobile Header */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-white">
              <h2 className="text-base font-semibold text-gray-900">Course Content</h2>
              <button
                onClick={() => setIsMobileSidebarOpen(false)}
                className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
            
            {/* Mobile Content */}
            <div className="h-full overflow-y-auto">
              {/* Course Info */}
              <div className="p-4 border-b border-gray-200">
                <Link 
                  href="/dashboard/courses"
                  className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors mb-3"
                  onClick={() => setIsMobileSidebarOpen(false)}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to courses
                </Link>

                <h1 className="text-base font-semibold text-gray-900 mb-3 leading-tight">
                  {course?.name || 'Loading...'}
                </h1>
                
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium text-gray-900">
                      {actualProgress.progressPercentage}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="bg-blue-600 h-1.5 rounded-full transition-all duration-500"
                      style={{ width: `${actualProgress.progressPercentage}%` }}
                    />
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {actualProgress.completedLessons + actualProgress.completedAssignments} of {actualProgress.totalLessons + actualProgress.totalAssignments} completed
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="pb-4">
                {!course || !course.modules ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-sm text-gray-500">Loading...</p>
                    </div>
                  </div>
                ) : course.modules.length === 0 ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <BookOpen className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No content available</p>
                    </div>
                  </div>
                ) : (
                  <div className="px-3 pt-3 space-y-2">
                    {course.modules.map((module, moduleIndex) => (
                      <div key={module.id} className="border border-gray-200 rounded-lg overflow-hidden">
                        <button
                          onClick={() => toggleModule(module.id)}
                          className="w-full px-3 py-3 flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-xs font-semibold text-gray-500 bg-gray-200 px-1.5 py-0.5 rounded">
                              {moduleIndex + 1}
                            </span>
                            <h3 className="text-sm font-medium text-gray-900 text-left truncate">
                              {module.title}
                            </h3>
                          </div>
                          <div className="flex items-center space-x-2 flex-shrink-0">
                            <span className="text-xs text-gray-500">
                              {(module.lessons?.length || 0) + (module.assignments?.length || 0)}
                            </span>
                            {expandedModules.has(module.id) ? (
                              <ChevronDown className="w-4 h-4 text-gray-400" />
                            ) : (
                              <ChevronRight className="w-4 h-4 text-gray-400" />
                            )}
                          </div>
                        </button>

                        {expandedModules.has(module.id) && (
                          <div className="bg-white">
                            {/* Lessons */}
                            {module.lessons && module.lessons.length > 0 && (
                              <>
                                {module.lessons.map((lesson, lessonIndex) => {
                                  const lessonStatus = getLessonStatus(lesson.id)
                                  const isSelected = selectedLesson?.id === lesson.id
                                  const isLocked = lessonStatus === 'locked'
                                  const isCompleted = lessonStatus === 'completed'
                                  
                                  return (
                                    <button
                                      key={lesson.id}
                                      onClick={() => handleLessonSelect({ id: lesson.id, title: lesson.title })}
                                      disabled={isLocked}
                                      className={cn(
                                        "w-full px-3 py-3 flex items-center space-x-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0",
                                        isSelected && "bg-blue-50 border-r-2 border-r-blue-600",
                                        isLocked && "opacity-50 cursor-not-allowed"
                                      )}
                                    >
                                      <div className="flex-shrink-0">
                                        {isLocked ? (
                                          <Lock className="w-4 h-4 text-gray-400" />
                                        ) : isCompleted ? (
                                          <CheckCircle className="w-4 h-4 text-green-600" />
                                        ) : (
                                          <Play className="w-4 h-4 text-blue-600" />
                                        )}
                                      </div>
                                      
                                      <div className="flex-1 min-w-0">
                                        <p className={cn(
                                          "text-sm font-medium truncate",
                                          isSelected ? "text-blue-900" : "text-gray-900",
                                          isLocked && "text-gray-500"
                                        )}>
                                          {lessonIndex + 1}. {lesson.title}
                                        </p>
                                        <div className="flex items-center mt-0.5 text-xs text-gray-500">
                                          <Clock className="w-3 h-3 mr-1" />
                                          <span>15 min</span>
                                          {isCompleted && (
                                            <>
                                              <span className="mx-1">•</span>
                                              <span className="text-green-600">Complete</span>
                                            </>
                                          )}
                                        </div>
                                      </div>
                                    </button>
                                  )
                                })}
                              </>
                            )}

                            {/* Assignments */}
                            {module.assignments && module.assignments.length > 0 && (
                              <>
                                {module.assignments.map((assignment) => {
                                  const assignmentStatus = getAssignmentStatus(assignment.id)
                                  const isSelected = selectedAssignment?.id === assignment.id
                                  const isLocked = assignmentStatus === 'locked'
                                  const submissionStatus = getAssignmentSubmissionStatus(assignment)
                                  
                                  return (
                                    <button
                                      key={assignment.id}
                                      onClick={() => handleAssignmentSelect({ id: assignment.id, title: assignment.title })}
                                      disabled={isLocked}
                                      className={cn(
                                        "w-full px-3 py-3 flex items-center space-x-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0",
                                        isSelected && "bg-purple-50 border-r-2 border-r-purple-600",
                                        isLocked && "opacity-50 cursor-not-allowed"
                                      )}
                                    >
                                      <div className="flex-shrink-0">
                                        {isLocked ? (
                                          <Lock className="w-4 h-4 text-gray-400" />
                                        ) : (
                                          <FileText className="w-4 h-4 text-purple-600" />
                                        )}
                                      </div>
                                      
                                      <div className="flex-1 min-w-0">
                                        <p className={cn(
                                          "text-sm font-medium truncate",
                                          isSelected ? "text-purple-900" : "text-gray-900",
                                          isLocked && "text-gray-500"
                                        )}>
                                          {assignment.title}
                                        </p>
                                        <div className="flex items-center mt-0.5 text-xs">
                                          <span className="text-gray-500">Assignment</span>
                                          <span className="mx-1 text-gray-500">•</span>
                                          <span className={submissionStatus.color}>
                                            {submissionStatus.text}
                                          </span>
                                        </div>
                                      </div>
                                    </button>
                                  )
                                })}
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      <div className="hidden lg:block w-80 border-r border-gray-200 bg-white">
        <SidebarContent />
      </div>
    </>
  )
}