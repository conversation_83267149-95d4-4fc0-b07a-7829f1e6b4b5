import Link from 'next/link';
import { PlayCircle, Music4, Users, Star, ArrowRight, Sparkles, NotebookPen, MessageSquare, Headphones, BookOpen, Heart } from 'lucide-react';

export default function HomePage() {
  return (
    <main className="min-h-screen bg-white relative overflow-hidden flex flex-col">
      {/* Decorative background musical elements - very subtle, light gradients */}
      <div className="absolute inset-0 z-0">
        <Music4 className="absolute top-1/4 left-[5%] text-blue-100 opacity-50 w-48 h-48 pointer-events-none select-none rotate-12" strokeWidth={0.5} />
        <Music4 className="absolute bottom-1/4 right-[5%] text-blue-100 opacity-50 w-48 h-48 pointer-events-none select-none -rotate-12" strokeWidth={0.5} />
        <Sparkles className="absolute top-[10%] right-[20%] text-blue-100 opacity-30 w-24 h-24 pointer-events-none select-none animate-pulse-slow" strokeWidth={0.7} />
        <Sparkles className="absolute bottom-[10%] left-[20%] text-blue-100 opacity-30 w-20 h-20 pointer-events-none select-none animate-spin-slow" strokeWidth={0.7} />
        {/* More musical shapes, like a drum/tabla or sitar outline if available, could go here */}
      </div>

      {/* HEADER - Minimalist, inspired by the image */}
      <header className="relative z-20 flex justify-between items-center px-8 py-6 max-w-7xl mx-auto w-full">
        <div className="text-3xl font-extrabold text-gray-800">dhwaini</div>
        <nav className="hidden md:flex space-x-8 text-gray-600 font-medium">
          <Link href="#testimonials" className="hover:text-blue-600 transition-colors">Testimonials</Link>
          <Link href="/dashboard" className="px-5 py-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg hover:from-blue-600 hover:to-indigo-700 transition-all">
            Dashboard
          </Link>
        </nav>
      </header>

      {/* HERO SECTION */}
      <section className="relative z-10 flex flex-col items-center justify-center pt-20 pb-20 px-4 text-center flex-grow">
        <div className="max-w-3xl w-full">
          <span className="inline-flex items-center gap-2 mb-4 px-4 py-1 rounded-full bg-white text-blue-700 font-semibold text-sm shadow-sm border border-blue-200">
            <BookOpen size={18} className="text-blue-500" />
            Your Gateway to Indian Classical Music
          </span>
          <h1 className="text-6xl md:text-7xl font-extrabold text-gray-900 mb-6 leading-tight animate-fade-in-up">
            Master Music with <br />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800">DhwaniNi </span>
          </h1>
          <p className="text-lg md:text-xl text-gray-700 mb-10 max-w-2xl mx-auto animate-fade-in-up delay-200">
            Interactive lessons, personalized practice, and a supportive community to nurture your talent.
          </p>
          <Link
            href="/dashboard"
            className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold px-10 py-5 rounded-full shadow-xl hover:from-blue-600 hover:to-indigo-700 transition-all text-lg group transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 focus:ring-offset-white animate-fade-in-up delay-400"
          >
            <PlayCircle size={26} className="transition-transform group-hover:scale-110" />
            Explore Courses
          </Link>
        </div>
      </section>


      {/* TESTIMONIAL SECTION */}
      <section id="testimonials" className="relative z-10 bg-white max-w-full px-4 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-extrabold text-gray-900 mb-12">
            What Our Students Say
          </h2>
          <div className="bg-white rounded-3xl shadow-xl p-8 md:p-12 border border-gray-100">
            <Headphones size={60} className="text-blue-600 mx-auto mb-6" />
            <blockquote className="italic text-xl md:text-2xl text-gray-700 leading-relaxed mb-6">
              “Dhwanini LMS has completely revolutionized my learning experience. The interactive lessons and direct feedback have been invaluable in my journey. Highly recommend for anyone serious about Indian classical music!”
            </blockquote>
            <p className="text-lg font-semibold text-gray-800">— Sangeeta Sharma, Advanced Student</p>
          </div>
        </div>
      </section>

      {/* CALL TO ACTION */}
      <section className="relative z-10 flex flex-col items-center justify-center py-20 px-4 text-center bg-gradient-to-br from-blue-600 to-indigo-800 text-white">
        <h2 className="text-4xl md:text-5xl font-extrabold mb-8 leading-tight drop-shadow-md">
          Ready to Elevate Your Music?
        </h2>
        <p className="text-lg md:text-xl mb-10 max-w-2xl opacity-90">
          Join Dhwanini LMS today and transform your passion into mastery.
        </p>
        <Link
          href="/dashboard"
          className="inline-flex items-center gap-3 bg-white text-blue-700 font-bold px-12 py-5 rounded-full shadow-2xl hover:bg-gray-100 transition-all text-xl group transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
        >
          <ArrowRight size={28} className="transition-transform group-hover:translate-x-1" />
          Dive into Dashboard
        </Link>
      </section>

      {/* FOOTER */}
      <footer className="relative z-10 py-10 flex flex-col items-center bg-gray-800 text-gray-300">
        <div className="text-xl font-bold mb-4">Dhwanini LMS</div>
        <div className="flex space-x-6 mb-4">
          <Link href="#" className="hover:text-white transition-colors">Privacy Policy</Link>
          <Link href="#" className="hover:text-white transition-colors">Terms of Service</Link>
          <Link href="#" className="hover:text-white transition-colors">Contact Us</Link>
        </div>
        <span className="text-sm">
          &copy; {new Date().getFullYear()} Dhwanini LMS. All rights reserved.
        </span>
        <span className="text-xs mt-2 opacity-70">Empowering the next generation of classical musicians.</span>
      </footer>
    </main>
  );
}