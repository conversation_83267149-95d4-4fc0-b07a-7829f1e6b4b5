import { Music, Prisma, UserRecording } from "@prisma/client";


export type LyricsLine = {
    time: number;
    text: string;
}

export type Song = Music & {
    lyrics: LyricsLine[];
}

export type RecentSession = Prisma.UserRecordingGetPayload<{
    select: {
        id: true,
        musicTitle: true,
        createdAt: true,
        duration: true,
        fileName: true,
    }
}>

export type Recording = Prisma.UserRecordingGetPayload<{
    select: {
        id: true,
        musicTitle: true,
        createdAt: true,
        duration: true,
        fileName: true,
        favourite: true,
    }
}>


