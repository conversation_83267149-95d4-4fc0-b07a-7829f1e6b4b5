// app/components/sections/HeroSection.tsx
import Link from 'next/link';
import { Music4, Play, Headphones } from 'lucide-react'; // Using Music4 for a more thematic icon

export default function HeroSection() {
  return (
    <section className="rounded-2xl bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white p-8 md:p-12 mb-12 relative overflow-hidden">
      <div className="relative z-10">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 z-10 leading-tight">
          Welcome to Dhwanini
        </h1>
        <p className="text-blue-400 text-lg max-w-3xl mb-8">
          Your journey through the world of classical Indian music continues here. Track your progress,
          explore new courses, and connect with instructors.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/dashboard/courses"
            className="group inline-flex items-center bg-white text-blue-700 font-semibold px-6 py-3 rounded-lg shadow-md hover:bg-white transition-colors duration-300 text-base focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-700">
            <Play size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110" />
            Continue Learning
          </Link>
          
          <Link
            href="/dashboard/sing-along"
            className="group inline-flex items-center bg-white0 text-white font-semibold px-6 py-3 rounded-lg shadow-md hover:bg-blue-600 transition-colors duration-300 text-base border border-blue-400 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-700">
            <Headphones size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110" />
            Sing Along Studio
          </Link>
        </div>
      </div>
      {/* Subtle background pattern using Lucide icon */}
      <Music4 className="absolute -right-10 -bottom-16 text-white text-opacity-10 w-72 h-72 md:w-96 md:h-96 transform rotate-[15deg]" strokeWidth={1} />
       <div className="absolute -left-20 top-10 opacity-5">
         <svg width="320" height="320" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
           <path d="M9 18V5H7V18H9Z" fill="currentColor" />
           <path d="M13 18V5H11V18H13Z" fill="currentColor" />
           <path d="M17 18V5H15V18H17Z" fill="currentColor" />
         </svg>
       </div>
    </section>
  );
}