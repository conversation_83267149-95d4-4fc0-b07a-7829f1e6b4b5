/*
  Warnings:

  - You are about to drop the column `videoUrl` on the `Lesson` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[calEventId]` on the table `FeedbackSession` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "FeedbackSession" ADD COLUMN     "assignmentId" TEXT,
ADD COLUMN     "calEventId" TEXT;

-- AlterTable
ALTER TABLE "Lesson" DROP COLUMN "videoUrl";

-- CreateTable
CREATE TABLE "Video" (
    "id" TEXT NOT NULL,
    "lessonId" TEXT NOT NULL,
    "upload_id" TEXT NOT NULL,
    "playback_id" TEXT,
    "asset_id" TEXT,
    "title" TEXT,
    "status" TEXT NOT NULL DEFAULT 'preparing',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Video_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FeedbackSettings" (
    "id" TEXT NOT NULL DEFAULT 'feedback_settings',
    "calLink" TEXT NOT NULL DEFAULT 'dhwani/feedback-session',

    CONSTRAINT "FeedbackSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Video_lessonId_key" ON "Video"("lessonId");

-- CreateIndex
CREATE UNIQUE INDEX "Video_upload_id_key" ON "Video"("upload_id");

-- CreateIndex
CREATE INDEX "Video_lessonId_idx" ON "Video"("lessonId");

-- CreateIndex
CREATE INDEX "Video_title_idx" ON "Video"("title");

-- CreateIndex
CREATE UNIQUE INDEX "FeedbackSession_calEventId_key" ON "FeedbackSession"("calEventId");

-- CreateIndex
CREATE INDEX "FeedbackSession_assignmentId_idx" ON "FeedbackSession"("assignmentId");

-- AddForeignKey
ALTER TABLE "Video" ADD CONSTRAINT "Video_lessonId_fkey" FOREIGN KEY ("lessonId") REFERENCES "Lesson"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FeedbackSession" ADD CONSTRAINT "FeedbackSession_assignmentId_fkey" FOREIGN KEY ("assignmentId") REFERENCES "Assignment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
