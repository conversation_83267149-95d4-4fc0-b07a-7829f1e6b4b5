"use client"

import { useEffect, useState } from "react"
import { PlayCircle, X, Save, AlertCircle, Video, Clock, CheckCircle, Upload, Trash2, RotateCcw } from "lucide-react"
import { Lesson as LessonData } from "@/types/course"

import MuxUploader from '@mux/mux-uploader-react';
import { createDirectUpload, createVideo } from "@/data-access/mux/video";


interface LessonBuilderProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: LessonData) => Promise<any>
  initialData?: Partial<LessonData>
  nextOrder: number
}


export default function LessonBuilder({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData, 
  nextOrder 
}: LessonBuilderProps) {
  
  const [formData, setFormData] = useState<LessonData>({
    id: initialData?.id || "",
    moduleId: initialData?.moduleId || "",
    title: initialData?.title || "",
    description: initialData?.description || "",
    video: initialData?.video || null,
    durationMin: initialData?.durationMin || null,
    order: initialData?.order || nextOrder,
    createdAt: initialData?.createdAt || new Date(),
    updatedAt: initialData?.updatedAt || new Date()
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [directUploadUrl, setDirectUploadUrl] = useState<string | null>(null)
  const [directUploadId, setDirectUploadId] = useState<string | null>(null)
  const [opening, setOpening] = useState(false)

  const handleDirectUpload = async () => {
    setOpening(true)
    try {
      const {success,url,id,error} = await createDirectUpload()
      if(success && url) {
        setDirectUploadUrl(url)
        setDirectUploadId(id)
      }
      else {
        console.error("Failed to create direct upload:", error)
      }
    }
    catch (error) {
      console.error("Failed to create direct upload:", error)
    }
    finally {
      setOpening(false)
    }
  }

  const handleUploadComplete = async (data: any) => {
    console.log("Upload complete:", data)
    try {
      const result = await createVideo(formData.id, directUploadId, formData.title)
      if(result.success) {
        setFormData({ ...formData, video: result.video })
      }
      else {
        console.error("Failed to create video:", result.error)
      }
    }
    catch (error) {
      console.error("Failed to create video:", error)
    }
  }

  const handleUploadError = (error: any) => {
    console.error("Upload error:", error)
    setErrors({
      video: "Failed to upload video"
    })
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      newErrors.title = "Lesson title is required"
    }
    
    if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters"
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required"
    }

    if (formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters"
    }

    if (formData.durationMin !== null && formData.durationMin <= 0) {
      newErrors.durationMin = "Duration must be greater than 0"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      const result = await onSave(formData)
      
      if (!initialData && result && 'data' in result && result.data?.lesson) {
        setFormData({
          ...result.data.lesson,
          video: null, 
        })
        
        setErrors({})
        setDirectUploadUrl(null)
        setOpening(false)
        
      } else if (initialData) {
        onClose()
      }
    } 
    catch (error) {
      console.error("Failed to save lesson:", error)
    } 
    finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-white/70 text-gray-700 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <PlayCircle className="w-5 h-5 text-gray-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {initialData ? "Edit Lesson" : "Create New Lesson"}
              </h2>
              <p className="text-sm text-gray-600">
                {initialData ? "Update lesson content and settings" : "Add a new lesson with video content"}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lesson Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.title ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter lesson title"
                />
                {errors.title && (
                  <div className="flex items-center gap-2 mt-2 text-red-600">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm">{errors.title}</span>
                  </div>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                    errors.description ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Describe what students will learn in this lesson"
                />
                {errors.description && (
                  <div className="flex items-center gap-2 mt-2 text-red-600">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm">{errors.description}</span>
                  </div>
                )}
                <div className="text-right text-sm text-gray-500 mt-1">
                  {formData.description?.length || 0}/500
                </div>
              </div>

              {/* Duration and Order */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes)
                  </label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="number"
                      value={formData.durationMin || ""}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        durationMin: e.target.value ? parseInt(e.target.value) : null 
                      })}
                      min="1"
                      className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.durationMin ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="30"
                    />
                  </div>
                  {errors.durationMin && (
                    <div className="flex items-center gap-2 mt-2 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{errors.durationMin}</span>
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-1">
                    Optional - leave empty if unknown
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Lesson Order
                  </label>
                  <input
                    type="number"
                    value={formData.order}
                    onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 1 })}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="1"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Order within the module
                  </p>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Video Upload - Show when lesson has an ID (either existing or newly created) */}
              {formData.id && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Lesson Video
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-gray-400 transition-colors">
                    {formData.video ? (
                      <div className="space-y-4">
                        {/* Video Preview Card */}
                        <div className="relative overflow-hidden bg-white border border-gray-400 border-dashed rounded-sm">
                          {/* Video Thumbnail Area */}
                          <div className="aspect-video flex items-center justify-center relative">
                            <div className="text-center">
                              <PlayCircle className="w-16 h-16 text-gray-400 mx-auto mb-3" />
                              <p className="text-gray-400 font-medium">{formData.video.title}</p>
                            </div>
                            
                            {/* Status Badge */}
                            <div className="absolute top-3 right-3">
                              {formData.video.status === 'ready' ? (
                                <div className="flex items-center gap-1 text-gray-500 bg-green-500/20 border border-green-500/30 rounded-full px-2 py-1">
                                  <CheckCircle className="w-3 h-3 text-green-400" />
                                  <span className="text-xs font-medium">Ready</span>
                                </div>
                              ) : formData.video.status === 'processing' ? (
                                <div className="flex items-center gap-1 text-gray-500 bg-blue-500/20 border border-blue-500/30 rounded-full px-2 py-1">
                                  <div className="w-3 h-3 border border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                                  <span className="text-xs font-medium">Processing</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-1 text-gray-500 bg-yellow-500/20 border border-yellow-500/30 rounded-full px-2 py-1">
                                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                  <span className="text-xs font-medium">Uploaded</span>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {/* Video Info Bar */}
                          <div className="bg-gray-100 p-3 border-t border-gray-200">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3 text-gray-500 text-sm">
                                {formData.durationMin && (
                                  <div className="flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    <span>{formData.durationMin} min</span>
                                  </div>
                                )}
                                {formData.video.createdAt && (
                                  <span>
                                    Uploaded {new Date(formData.video.createdAt).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                              
                              <button
                                onClick={() => {setFormData({ ...formData, video: null }); setDirectUploadUrl(null); setDirectUploadId(null)}}
                                className="text-red-500 hover:text-red-600"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        {/* Upload in Progress */}
                        {directUploadUrl && opening && (
                          <div className="border-2 border-dashed border-blue-300 bg-blue-50 rounded-lg p-4">
                            <div className="text-center mb-3">
                              <Upload className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                              <h4 className="text-sm font-medium text-blue-900">Replacing Video</h4>
                              <p className="text-xs text-blue-700">Upload your new video file</p>
                            </div>
                            <MuxUploader
                              endpoint={directUploadUrl}
                              pausable={true}
                              onSuccess={handleUploadComplete}
                              onError={handleUploadError}
                            />
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center space-y-4">
                        {!directUploadUrl ? (
                          <>
                            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                              <Video className="w-8 h-8 text-gray-400" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-900 mb-1">Upload lesson video</h3>
                              <p className="text-sm text-gray-500 mb-4">Add a video to make this lesson interactive</p>
                            </div>
                            <button
                              type="button"
                              onClick={handleDirectUpload}
                              disabled={opening}
                              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Upload className="w-4 h-4" />
                              {opening ? "Opening..." : "Upload Video"}
                            </button>
                            <p className="text-xs text-gray-500 mt-2">MP4, WebM, MOV up to 500MB</p>
                          </>
                        ) : (
                          <div className="space-y-4">
                            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                              <Upload className="w-8 h-8 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-900 mb-1">Upload your video</h3>
                              <p className="text-sm text-gray-500 mb-4">Drag and drop or click to select</p>
                            </div>
                            <MuxUploader
                              endpoint={directUploadUrl}
                              pausable={true}
                              onSuccess={handleUploadComplete}
                              onError={handleUploadError}
                            />
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Video Upload Error */}
                    {errors.video && (
                      <div className="flex items-center gap-2 mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                        <AlertCircle className="w-4 h-4 flex-shrink-0" />
                        <span className="text-sm">{errors.video}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Show message when creating new lesson (no ID yet) */}
              {!formData.id && (
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center">
                  <Video className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <h3 className="text-sm font-medium text-gray-700 mb-1">Video Upload Available After Creation</h3>
                  <p className="text-sm text-gray-500">Create the lesson first, then you can upload the video content.</p>
                </div>
              )}
            </div>
          </div>
          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-white transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              {loading ? "Saving..." : "Save Lesson"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 