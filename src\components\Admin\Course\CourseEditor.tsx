"use client"

import { useState, useEffect, useTransition } from "react"
import { useRouter } from "next/navigation"
import { CourseLevel } from "@prisma/client"
import { Module, Lesson, Assignment, CourseDetails } from "@/types/course"
import { 
  createCourseAction, 
  updateCourseAction,
  createModuleAction,
  updateModuleAction,
  createLessonAction,
  updateLessonAction,
  createAssignmentAction,
  updateAssignmentAction,
  deleteModuleAction,
  deleteAssignmentAction,
  deleteLessonAction,
} from "@/data-access/course/admin-course"
import { 
  getThumbnailUrl,
  uploadThumbnail,
  deleteThumbnail
} from "@/data-access/course/storage"
import LessonBuilder from "./Lesson/LessonBuilder"
import AssignmentBuilder from "./Assignment/AssignmentBuilder"
import { 
  Save, 
  ArrowLeft, 
  Plus, 
  Edit, 
  Trash2, 
  BookOpen, 
  PlayCircle, 
  FileText,
  ChevronDown,
  ChevronRight,
  Upload,
  X,
  Image,
  Clock,
  Calendar,
  Users
} from "lucide-react"

interface CourseEditorProps {
  course?: CourseDetails
}

export default function CourseEditor({ course }: CourseEditorProps) {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  const isEditing = !!course
  
  // Thumbnail state
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null)
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null)
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null)
  const [thumbnailLoading, setThumbnailLoading] = useState(false)

  // UI state
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set())
  const [showModuleForm, setShowModuleForm] = useState(false)
  const [showLessonForm, setShowLessonForm] = useState<string | null>(null)
  const [showAssignmentForm, setShowAssignmentForm] = useState<string | null>(null)
  
  // Edit state
  const [editingModule, setEditingModule] = useState<string | null>(null)
  const [editingLesson, setEditingLesson] = useState<string | null>(null)
  const [editingAssignment, setEditingAssignment] = useState<string | null>(null)

  // Load existing thumbnail on mount
  useEffect(() => {
    if (isEditing && course) {
      loadThumbnail()
    }
  }, [isEditing, course])

  const loadThumbnail = async () => {
    if (!course) return
    
    try {
      const { url, error } = await getThumbnailUrl(course.id, course.thumbnailPath)
      if (!error && url) {
        setThumbnailUrl(url)
      }
    } catch (error) {
      console.error("Failed to load thumbnail:", error)
    }
  }

  const handleThumbnailSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }
      
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB')
        return
      }

      setThumbnailFile(file)
      
      const reader = new FileReader()
      reader.onload = (e) => {
        setThumbnailPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleThumbnailUpload = async () => {
    if (!thumbnailFile || !course) return

    setThumbnailLoading(true)
    try {
      const { success, url, error } = await uploadThumbnail(course.id, thumbnailFile)
      if (success && url) {
        course.thumbnailPath = url
        setThumbnailUrl(url)
        setThumbnailFile(null)
        setThumbnailPreview(null)
      } else {
        console.error("Failed to upload thumbnail:", error)
      }
    } catch (error) {
      console.error("Failed to upload thumbnail:", error)
    } finally {
      setThumbnailLoading(false)
    }
  }

  const handleThumbnailRemove = async () => {
    if (!course) return

    setThumbnailLoading(true)
    try {
      const { success } = await deleteThumbnail(course.id, course.thumbnailPath)
      if (success) {
        setThumbnailUrl(null)
        setThumbnailFile(null)
        setThumbnailPreview(null)
      }
    } catch (error) {
      console.error("Failed to remove thumbnail:", error)
    } finally {
      setThumbnailLoading(false)
    }
  }

  const handleCourseSubmit = async (formData: FormData) => {
    startTransition(async () => {
      if (isEditing && course) {
        const result = await updateCourseAction(course.id, formData)
        if (result.success && thumbnailFile) {
          await handleThumbnailUpload()
        }
      } else {
        const result = await createCourseAction(formData)
        if (result.success && result.data?.courseId) {
          if (thumbnailFile) {
            await uploadThumbnail(result.data.courseId, thumbnailFile)
          }
          router.push(`/admin/courses/${result.data.courseId}`)
        }
      }
    })
  }

  const handleModuleSubmit = async (formData: FormData) => {
    if (!course) return
    
    startTransition(async () => {
      await createModuleAction(course.id, formData)
      setShowModuleForm(false)
    })
  }

  const handleLessonSave = async (moduleId: string, lessonData: Lesson): Promise<any> => {
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          // Convert lesson data to FormData for the existing action
          const formData = new FormData()
          formData.set('title', lessonData.title)
          formData.set('description', lessonData.description || '')
          formData.set('durationMin', lessonData.durationMin?.toString() || '')
          formData.set('order', lessonData.order.toString())
          
          const result = await createLessonAction(moduleId, formData)
          
          if (lessonData.id) {
            setShowLessonForm(null)
          }
          
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
    })
  }

  const handleAssignmentSave = async (moduleId: string, assignmentData: Assignment): Promise<void> => {
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          const formData = new FormData()
          formData.set('title', assignmentData.title)
          formData.set('description', assignmentData.description || '')
          formData.set('deadline', assignmentData.deadline.toISOString())
          formData.set('assignmentPath', assignmentData.assignmentPath || '')
          const { success, error } = await createAssignmentAction(moduleId, formData)
          if(!success) {
            reject(error)
          }
          setShowAssignmentForm(null)
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    })
  }

  const handleModuleEdit = async (moduleData: { title: string; description?: string }) => {
    if (!editingModule) return
    
    startTransition(async () => {
      try {
        const formData = new FormData()
        formData.set('title', moduleData.title)
        formData.set('description', moduleData.description || '')
        
        await updateModuleAction(editingModule, formData)
        setEditingModule(null)
      } catch (error) {
        console.error('Failed to update module:', error)
      }
    })
  }

  const handleLessonEdit = async (lessonData: Lesson): Promise<void> => {
    if (!editingLesson) return Promise.reject('No lesson selected for editing')
    
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          const formData = new FormData()
          formData.set('title', lessonData.title)
          formData.set('description', lessonData.description || '')
          formData.set('durationMin', lessonData.durationMin?.toString() || '')
          formData.set('order', lessonData.order.toString())
          
          await updateLessonAction(editingLesson, formData)
          setEditingLesson(null)
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    })
  }

  const handleAssignmentEdit = async (assignmentData: Assignment): Promise<void> => {
    if (!editingAssignment) return Promise.reject('No assignment selected for editing')
    
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          const formData = new FormData()
          formData.set('title', assignmentData.title)
          formData.set('description', assignmentData.description || '')
          formData.set('deadline', assignmentData.deadline.toISOString())
          formData.set('assignmentPath', assignmentData.assignmentPath || '')
          const { success, error } = await updateAssignmentAction(editingAssignment, formData)
          if(!success) {
            reject(error)
          }
          setEditingAssignment(null)
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    })
  }

  const toggleModuleExpansion = (moduleId: string) => {
    const newExpanded = new Set(expandedModules)
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId)
    } else {
      newExpanded.add(moduleId)
    }
    setExpandedModules(newExpanded)
  }

  const getNextModuleOrder = () => {
    if (!course?.modules.length) return 1
    return Math.max(...course.modules.map(m => m.order)) + 1
  }

  const getNextLessonOrder = (moduleId: string) => {
    const module = course?.modules.find(m => m.id === moduleId)
    if (!module?.lessons.length) return 1
    return Math.max(...module.lessons.map(l => l.order)) + 1
  }

  return (
    <div className="min-h-screen bg-white text-gray-700">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center gap-3 sm:gap-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div className="min-w-0">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
                  {isEditing ? course.name : "Create New Course"}
                </h1>
                <p className="text-gray-600 text-sm mt-1 hidden sm:block">
                  {isEditing ? "Manage course content and settings" : "Set up a new course with modules and lessons"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Course Details Form */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 sm:p-6 border-b border-gray-200 bg-white">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <BookOpen className="w-5 h-5 text-blue-600" />
              </div>
              <div className="min-w-0">
                <h2 className="text-lg font-semibold text-gray-900">Course Information</h2>
                <p className="text-sm text-gray-600 hidden sm:block">Basic details about your course</p>
              </div>
            </div>
          </div>
          
          <form action={handleCourseSubmit} className="p-4 sm:p-6">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
              {/* Left Column - Basic Info */}
              <div className="xl:col-span-2 space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      defaultValue={course?.name || ""}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                      placeholder="Enter course name"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Course Title
                    </label>
                    <input
                      type="text"
                      name="title"
                      defaultValue={course?.name || ""}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                      placeholder="Enter course title"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    name="description"
                    defaultValue={course?.description || ""}
                    rows={4}
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none text-sm sm:text-base"
                    placeholder="Describe your course..."
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Level
                    </label>
                    <select
                      name="level"
                      defaultValue={course?.level || CourseLevel.BEGINNER}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                    >
                      <option value={CourseLevel.BEGINNER}>Beginner</option>
                      <option value={CourseLevel.INTERMEDIATE}>Intermediate</option>
                      <option value={CourseLevel.ADVANCED}>Advanced</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price
                    </label>
                    <input
                      type="number"
                      name="price"
                      defaultValue={course?.price || 0}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Audition Fee
                    </label>
                    <input
                      type="number"
                      name="auditionFee"
                      defaultValue={course?.auditionFee || 0}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency
                    </label>
                    <select
                      name="currency"
                      defaultValue={course?.currency || "USD"}
                      className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                    >
                      <option value="USD">USD</option>
                      <option value="INR">INR</option>
                      <option value="EUR">EUR</option>
                      <option value="GBP">GBP</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instructor Email
                  </label>
                  <input
                    type="email"
                    name="instructorEmail"
                    defaultValue={course?.instructorEmail || ""}
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm sm:text-base"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="flex items-start gap-3 p-4 bg-white rounded-lg border border-blue-200">
                  <input
                    type="checkbox"
                    name="isPublished"
                    value="true"
                    defaultChecked={course?.isPublished || false}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-0.5 flex-shrink-0"
                  />
                  {/* Hidden input to ensure unchecked state is captured */}
                  <input
                    type="hidden"
                    name="isPublished"
                    value="false"
                  />
                  <div className="min-w-0">
                    <span className="text-sm font-medium text-gray-900 block">
                      Publish course immediately
                    </span>
                    <p className="text-sm text-gray-600 mt-1">
                      Published courses will be visible to students
                    </p>
                  </div>
                </div>
              </div>

              {/* Right Column - Thumbnail */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Course Thumbnail
                  </label>
                  
                  {/* Thumbnail Display */}
                  <div className="space-y-4">
                    {(thumbnailUrl || thumbnailPreview) && (
                      <div className="relative">
                        <img
                          src={thumbnailPreview || thumbnailUrl || ''}
                          alt="Course thumbnail"
                          className="w-full h-32 sm:h-48 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={thumbnailPreview ? () => {setThumbnailFile(null); setThumbnailPreview(null)} : handleThumbnailRemove}
                          disabled={thumbnailLoading}
                          className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 transition-colors disabled:opacity-50 shadow-lg"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    )}

                    {/* Upload Controls */}
                    <div className="space-y-3">
                      <label className="cursor-pointer block">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 text-center hover:border-blue-400 hover:bg-white transition-colors">
                          <Image className="w-6 sm:w-8 h-6 sm:h-8 text-gray-400 mx-auto mb-2" />
                          <span className="text-sm font-medium text-gray-700 block">
                            Choose Image
                          </span>
                          <p className="text-xs text-gray-500 mt-1">
                            PNG, JPG up to 5MB
                          </p>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleThumbnailSelect}
                          className="hidden"
                        />
                      </label>

                      {thumbnailFile && (
                        <button
                          type="button"
                          onClick={handleThumbnailUpload}
                          disabled={thumbnailLoading || !course}
                          className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors text-sm"
                        >
                          <Upload className="w-4 h-4" />
                          {thumbnailLoading ? 'Uploading...' : 'Upload Thumbnail'}
                        </button>
                      )}
                    </div>

                    {!isEditing && (
                      <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <p className="text-sm text-amber-700">
                          💡 Save the course first to upload a thumbnail
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200 mt-8">
              <button
                type="submit"
                disabled={isPending}
                className="order-1 sm:order-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors font-medium"
              >
                <Save className="w-4 h-4" />
                {isPending ? "Saving..." : isEditing ? "Update Course" : "Create Course"}
              </button>
            </div>
          </form>
        </div>

        {/* Course Content - Only show for existing courses */}
        {isEditing && course && (
          <div className="space-y-4 sm:space-y-6">
            {/* Course Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              <div className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                    <BookOpen className="w-3 sm:w-4 h-3 sm:h-4 text-blue-600" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm text-gray-600">Modules</p>
                    <p className="text-lg sm:text-xl font-semibold text-gray-900">{course.modules.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-green-100 rounded-lg flex-shrink-0">
                    <PlayCircle className="w-3 sm:w-4 h-3 sm:h-4 text-green-600" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm text-gray-600">Lessons</p>
                    <p className="text-lg sm:text-xl font-semibold text-gray-900">
                      {course.modules.reduce((sum, module) => sum + module.lessons.length, 0)}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-purple-100 rounded-lg flex-shrink-0">
                    <FileText className="w-3 sm:w-4 h-3 sm:h-4 text-purple-600" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm text-gray-600">Assignments</p>
                    <p className="text-lg sm:text-xl font-semibold text-gray-900">
                      {course.modules.reduce((sum, module) => sum + module.assignments.length, 0)}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-orange-100 rounded-lg flex-shrink-0">
                    <Users className="w-3 sm:w-4 h-3 sm:h-4 text-orange-600" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-xs sm:text-sm text-gray-600">Status</p>
                    <p className="text-xs sm:text-sm font-medium text-gray-900">
                      {course.isPublished ? (
                        <span className="text-green-600">Published</span>
                      ) : (
                        <span className="text-gray-600">Draft</span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modules Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 sm:p-6 border-b border-gray-200 bg-white">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                      <BookOpen className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <h2 className="text-lg font-semibold text-gray-900">Course Modules</h2>
                      <p className="text-sm text-gray-600 hidden sm:block">Organize your content into modules</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowModuleForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors text-sm font-medium w-full sm:w-auto"
                  >
                    <Plus className="w-4 h-4" />
                    <span className="sm:inline">Add Module</span>
                  </button>
                </div>
              </div>

              <div className="p-4 sm:p-6">
                {/* Add Module Form */}
                {showModuleForm && (
                  <form action={handleModuleSubmit} className="mb-6 p-4 bg-white rounded-lg border border-blue-200">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Module Title *
                        </label>
                        <input
                          type="text"
                          name="title"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                          placeholder="Enter module title"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description
                        </label>
                        <textarea
                          name="description"
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-sm sm:text-base"
                          placeholder="Module description (optional)"
                        />
                      </div>
                      <input type="hidden" name="order" value={getNextModuleOrder()} />
                      <div className="flex flex-col sm:flex-row gap-2">
                        <button
                          type="submit"
                          disabled={isPending}
                          className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm font-medium"
                        >
                          {isPending ? "Creating..." : "Create Module"}
                        </button>
                        <button
                          type="button"
                          onClick={() => setShowModuleForm(false)}
                          className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </form>
                )}

                {/* Modules List */}
                <div className="space-y-4">
                  {course.modules.length === 0 ? (
                    <div className="text-center py-8 sm:py-12 border-2 border-dashed border-gray-300 rounded-lg bg-white">
                      <BookOpen className="w-8 sm:w-12 h-8 sm:h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No modules yet</h3>
                      <p className="text-gray-600 mb-4 text-sm sm:text-base px-4">Start building your course by adding the first module</p>
                      <button
                        onClick={() => setShowModuleForm(true)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 transition-colors text-sm font-medium"
                      >
                        <Plus className="w-4 h-4" />
                        Create First Module
                      </button>
                    </div>
                  ) : (
                    course.modules.map((module) => (
                      <ModuleCard
                        key={module.id}
                        module={module}
                        isExpanded={expandedModules.has(module.id)}
                        onToggleExpansion={() => toggleModuleExpansion(module.id)}
                        showLessonForm={showLessonForm === module.id}
                        showAssignmentForm={showAssignmentForm === module.id}
                        onShowLessonForm={() => setShowLessonForm(module.id)}
                        onShowAssignmentForm={() => setShowAssignmentForm(module.id)}
                        onHideLessonForm={() => setShowLessonForm(null)}
                        onHideAssignmentForm={() => setShowAssignmentForm(null)}
                        onLessonSubmit={(lessonData) => handleLessonSave(module.id, lessonData)}
                        onAssignmentSubmit={(assignmentData) => handleAssignmentSave(module.id, assignmentData)}
                        nextLessonOrder={getNextLessonOrder(module.id)}
                        isPending={isPending}
                        editingModule={editingModule}
                        editingLesson={editingLesson}
                        editingAssignment={editingAssignment}
                        onEditModule={(moduleId) => setEditingModule(moduleId)}
                        onEditLesson={(lessonId) => setEditingLesson(lessonId)}
                        onEditAssignment={(assignmentId) => setEditingAssignment(assignmentId)}
                        onModuleEdit={(moduleData) => handleModuleEdit(moduleData)}
                        onLessonEdit={(lessonData) => handleLessonEdit(lessonData)}
                        onAssignmentEdit={(assignmentData) => handleAssignmentEdit(assignmentData)}
                      />
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Separate ModuleCard component for better organization
interface ModuleCardProps {
  module: Module
  isExpanded: boolean
  onToggleExpansion: () => void
  showLessonForm: boolean
  showAssignmentForm: boolean
  onShowLessonForm: () => void
  onShowAssignmentForm: () => void
  onHideLessonForm: () => void
  onHideAssignmentForm: () => void
  onLessonSubmit: (lessonData: Lesson) => Promise<any>
  onAssignmentSubmit: (assignmentData: Assignment) => Promise<void>
  nextLessonOrder: number
  isPending: boolean
  // Edit props
  editingModule: string | null
  editingLesson: string | null
  editingAssignment: string | null
  onEditModule: (moduleId: string) => void
  onEditLesson: (lessonId: string) => void
  onEditAssignment: (assignmentId: string) => void
  onModuleEdit: (moduleData: { title: string; description?: string }) => Promise<void>
  onLessonEdit: (lessonData: Lesson) => Promise<void>
  onAssignmentEdit: (assignmentData: Assignment) => Promise<void>
}

function ModuleCard({
  module,
  isExpanded,
  onToggleExpansion,
  showLessonForm,
  showAssignmentForm,
  onShowLessonForm,
  onShowAssignmentForm,
  onHideLessonForm,
  onHideAssignmentForm,
  onLessonSubmit,
  onAssignmentSubmit,
  nextLessonOrder,
  isPending,
  editingModule,
  editingLesson,
  editingAssignment,
  onEditModule,
  onEditLesson,
  onEditAssignment,
  onModuleEdit,
  onLessonEdit,
  onAssignmentEdit
}: ModuleCardProps) {
  const [isDeleting, startDeleteTransition] = useTransition()

  const handleDelete = (type: 'module' | 'lesson' | 'assignment', id: string) => {
    if (!confirm(`Are you sure you want to delete this ${type}?`)) return
    
    startDeleteTransition(async () => {
      try {
        switch (type) {
          case 'module':
            await deleteModuleAction(id)
            break
          case 'lesson':
            await deleteLessonAction(id)
            break
          case 'assignment':
            await deleteAssignmentAction(id)
            break
        }
      } catch (error) {
        console.error(`Failed to delete ${type}:`, error)
        alert(`Failed to delete ${type}. Please try again.`)
      }
    })
  }

  const handleModuleEditSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    await onModuleEdit({
      title: formData.get('title') as string,
      description: formData.get('description') as string || undefined
    })
  }

  // Get lesson data for editing
  const getEditingLessonData = () => {
    if (!editingLesson) return undefined
    const lesson = module.lessons.find(lesson => lesson.id === editingLesson)
    if (!lesson) return undefined
    return {
      ...lesson,
      video: lesson.video || null
    }
  }

  // Get assignment data for editing
  const getEditingAssignmentData = () => {
    if (!editingAssignment) return undefined
    return module.assignments.find(assignment => assignment.id === editingAssignment)
  }

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      {/* Module Header */}
      <div className="p-3 sm:p-4 bg-white border-b border-gray-200">
        {editingModule === module.id ? (
          /* Module Edit Form */
          <form onSubmit={handleModuleEditSubmit} className="space-y-3">
            <div>
              <input
                type="text"
                name="title"
                defaultValue={module.title}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-semibold text-sm sm:text-base"
                placeholder="Module title"
                required
              />
            </div>
            <div>
              <textarea
                name="description"
                defaultValue={module.description || ''}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-sm"
                placeholder="Module description (optional)"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <button
                type="submit"
                disabled={isPending}
                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-3 py-1.5 rounded text-sm font-medium"
              >
                Save
              </button>
              <button
                type="button"
                onClick={() => onEditModule('')}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1.5 rounded text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </form>
        ) : (
          /* Module Display */
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0">
              <button
                onClick={onToggleExpansion}
                className="p-1 hover:bg-gray-200 rounded transition-colors flex-shrink-0"
                disabled={isDeleting}
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-600" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-600" />
                )}
              </button>
              <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <BookOpen className="w-3 sm:w-4 h-3 sm:h-4 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{module.title}</h3>
                {module.description && (
                  <p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2">{module.description}</p>
                )}
              </div>
            </div>
            <div className="flex items-center justify-between sm:justify-end gap-3 sm:gap-4">
              <div className="text-xs sm:text-sm text-gray-500 flex items-center gap-2 sm:gap-4">
                <span className="flex items-center gap-1">
                  <PlayCircle className="w-3 sm:w-4 h-3 sm:h-4" />
                  <span className="hidden xs:inline">{module.lessons.length} lessons</span>
                  <span className="xs:hidden">{module.lessons.length}</span>
                </span>
                <span className="flex items-center gap-1">
                  <FileText className="w-3 sm:w-4 h-3 sm:h-4" />
                  <span className="hidden xs:inline">{module.assignments.length} assignments</span>
                  <span className="xs:hidden">{module.assignments.length}</span>
                </span>
              </div>
              <div className="flex items-center gap-1">
                <button 
                  onClick={() => onEditModule(module.id)}
                  disabled={isDeleting || isPending}
                  className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
                >
                  <Edit className="w-3 sm:w-4 h-3 sm:h-4 text-gray-600" />
                </button>
                <button 
                  onClick={() => handleDelete('module', module.id)}
                  disabled={isDeleting || isPending}
                  className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
                >
                  <Trash2 className="w-3 sm:w-4 h-3 sm:h-4 text-red-600" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Module Content */}
      {isExpanded && (
        <div className="p-4 sm:p-6 bg-white space-y-4 sm:space-y-6">
          {/* Lessons Section */}
          <div>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
              <div className="flex items-center gap-2">
                <PlayCircle className="w-4 sm:w-5 h-4 sm:h-5 text-blue-600" />
                <h4 className="font-semibold text-gray-900 text-sm sm:text-base">Lessons</h4>
                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                  {module.lessons.length}
                </span>
              </div>
              <button
                onClick={onShowLessonForm}
                disabled={isDeleting || isPending}
                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-3 py-1.5 rounded-lg text-sm flex items-center justify-center gap-1 transition-colors font-medium w-full sm:w-auto"
              >
                <Plus className="w-3 h-3" />
                <span>Add Lesson</span>
              </button>
            </div>

            {/* Lesson Builder */}
            {showLessonForm && (
              <LessonBuilder
                isOpen={showLessonForm}
                onClose={onHideLessonForm}
                onSave={onLessonSubmit}
                nextOrder={nextLessonOrder}
              />
            )}

            {/* Lesson Edit Builder */}
            {editingLesson && (
              <LessonBuilder
                isOpen={!!editingLesson}
                onClose={() => onEditLesson('')}
                onSave={onLessonEdit}
                initialData={getEditingLessonData()}
                nextOrder={nextLessonOrder}
              />
            )}

            {/* Lessons List */}
            <div className="space-y-2">
              {module.lessons.length === 0 ? (
                <div className="text-center py-4 sm:py-6 border border-dashed border-gray-200 rounded-lg bg-white">
                  <PlayCircle className="w-5 sm:w-6 h-5 sm:h-6 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 text-xs sm:text-sm">No lessons yet</p>
                </div>
              ) : (
                module.lessons.map((lesson) => (
                  <div key={lesson.id} className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 p-3 bg-white rounded-lg border border-gray-200">
                    <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                      <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                        <PlayCircle className="w-3 sm:w-4 h-3 sm:h-4 text-blue-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <h5 className="font-medium text-gray-900 text-sm sm:text-base truncate">{lesson.title}</h5>
                        <div className="flex items-center gap-2 sm:gap-3 text-xs sm:text-sm text-gray-500 mt-1">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {lesson.durationMin || 15} min
                          </span>
                          <span>Lesson {lesson.order}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 justify-end sm:justify-start">
                      <button 
                        onClick={() => onEditLesson(lesson.id)}
                        disabled={isDeleting || isPending}
                        className="p-1 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
                      >
                        <Edit className="w-3 sm:w-4 h-3 sm:h-4 text-gray-600" />
                      </button>
                      <button 
                        onClick={() => handleDelete('lesson', lesson.id)}
                        disabled={isDeleting || isPending}
                        className="p-1 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
                      >
                        <Trash2 className="w-3 sm:w-4 h-3 sm:h-4 text-red-600" />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Assignments Section */}
          <div>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
              <div className="flex items-center gap-2">
                <FileText className="w-4 sm:w-5 h-4 sm:h-5 text-green-600" />
                <h4 className="font-semibold text-gray-900 text-sm sm:text-base">Assignments</h4>
                <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                  {module.assignments.length}
                </span>
              </div>
              <button
                onClick={onShowAssignmentForm}
                disabled={isDeleting || isPending}
                className="bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white px-3 py-1.5 rounded-lg text-sm flex items-center justify-center gap-1 transition-colors font-medium w-full sm:w-auto"
              >
                <Plus className="w-3 h-3" />
                <span>Add Assignment</span>
              </button>
            </div>

            {/* Assignment Builder */}
            {showAssignmentForm && (
              <AssignmentBuilder
                isOpen={showAssignmentForm}
                onClose={onHideAssignmentForm}
                onSave={onAssignmentSubmit}
                initialData={{
                  moduleId: module.id
                }}
              />
            )}

            {/* Assignment Edit Builder */}
            {editingAssignment && (
              <AssignmentBuilder
                isOpen={!!editingAssignment}
                onClose={() => onEditAssignment('')}
                onSave={onAssignmentEdit}
                initialData={getEditingAssignmentData()}
              />
            )}

            {/* Assignments List */}
            <div className="space-y-2">
              {module.assignments.length === 0 ? (
                <div className="text-center py-4 sm:py-6 border border-dashed border-gray-200 rounded-lg bg-white">
                  <FileText className="w-5 sm:w-6 h-5 sm:h-6 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 text-xs sm:text-sm">No assignments yet</p>
                </div>
              ) : (
                module.assignments.map((assignment) => (
                  <div key={assignment.id} className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 p-3 border border-gray-200 rounded-lg hover:bg-white transition-colors">
                    <div className="flex items-start gap-2 sm:gap-3 min-w-0">
                      <FileText className="w-3 sm:w-4 h-3 sm:h-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <span className="font-medium text-gray-900 text-sm sm:text-base block truncate">{assignment.title}</span>
                        <span className="text-xs sm:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded flex items-center gap-1 mt-2 w-fit">
                          <Calendar className="w-3 h-3" />
                          Due: {new Date(assignment.deadline).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-1 justify-end sm:justify-start">
                      <button 
                        onClick={() => onEditAssignment(assignment.id)}
                        disabled={isDeleting || isPending}
                        className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
                      >
                        <Edit className="w-3 h-3 text-gray-600" />
                      </button>
                      <button 
                        onClick={() => handleDelete('assignment', assignment.id)}
                        disabled={isDeleting || isPending}
                        className="p-1 hover:bg-gray-200 rounded transition-colors disabled:opacity-50"
                      >
                        <Trash2 className="w-3 h-3 text-red-600" />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 