import { getAssignmentById } from '@/data-access/assignment/assignment'
import { redirect, notFound } from 'next/navigation'
import { AssignmentView } from './AssignmentView'
import { createClient } from '@/utils/supabase/server'

interface AssignmentPageProps {
  params: Promise<{ id: string }>
}

export default async function AssignmentPage({ params }: AssignmentPageProps) {
  const { id } = await params
  
  const supabase = await createClient();
  const {data: {user}, error} = await supabase.auth.getUser();
  if (error || !user) {
    return <div>Error: {error?.message || 'User not found'}</div>;
  }

  const result = await getAssignmentById(id, user.email)

  if (!result.success) {
    if (result.error === 'User not authenticated') {
      redirect('/login')
    }
    throw new Error(result.error || 'Failed to load assignment')
  }

  if (!result.data) {
    notFound()
  }

  return <AssignmentView assignment={result.data} />
} 