'use client'

import { ProfileData, UserProfile } from '@/types/profile'
import { ProfileStatsCards } from './ProfileStatsCards'
import { ProfileDetails } from './ProfileDetails'
import { EnrollmentsList } from './EnrollmentsList'
import { CertificatesList } from './CertificatesList'
import { PaymentHistory } from './PaymentHistory'
import { updateUserProfile } from '@/data-access/profile/profile'

interface ProfileDashboardProps {
  profileData: ProfileData
}

export function ProfileDashboard({ profileData }: ProfileDashboardProps) {
  const { user, enrollments, certificates, payments, stats } = profileData

  const updateProfile = async (updates: Partial<UserProfile>) => {
    const result = await updateUserProfile(updates)
    if (result.success) {
      console.log('Profile updated successfully')
    } else {
      console.error('Failed to update profile:', result.error)
    }
  }

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Stats Cards */}
      <ProfileStatsCards stats={stats} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
        {/* Left Column - Main Content */}
        <div className="xl:col-span-2 space-y-6">
          {/* Profile Details */}
          <ProfileDetails user={user} onUpdate={updateProfile} />

          {/* Enrollments */}
          <EnrollmentsList enrollments={enrollments} />
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Certificates */}
          <CertificatesList certificates={certificates} />

          {/* Payment History */}
          <PaymentHistory payments={payments} />
        </div>
      </div>
    </div>
  )
} 