'use client'

import { useState } from 'react'
import { SubmissionStatus } from '@prisma/client'
import { AssignmentSubmissionDetail } from '@/types/analytics'
import { 
  Send, 
  FileText, 
  Calendar, 
  Clock, 
  CheckCircle2, 
  AlertCircle, 
  XCircle,
  Download,
  ExternalLink
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface AssignmentSubmissionManagerProps {
  submission: AssignmentSubmissionDetail
  onStatusUpdate: (submissionId: string, status: SubmissionStatus, feedback?: string) => Promise<void>
  isUpdating: boolean
}

export default function AssignmentSubmissionManager({
  submission,
  onStatusUpdate,
  isUpdating
}: AssignmentSubmissionManagerProps) {
  const [selectedStatus, setSelectedStatus] = useState<SubmissionStatus>(
    submission.status || SubmissionStatus.SUBMITTED
  )
  const [feedback, setFeedback] = useState(submission.feedback || '')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmitFeedback = async () => {
    if (!submission.id) return
    
    setIsSubmitting(true)
    try {
      await onStatusUpdate(submission.id, selectedStatus, feedback)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusIcon = (status: SubmissionStatus | null) => {
    switch (status) {
      case SubmissionStatus.GRADED:
        return <CheckCircle2 className="w-4 h-4 text-green-600" />
      case SubmissionStatus.NEEDS_REVISION:
        return <AlertCircle className="w-4 h-4 text-amber-600" />
      case SubmissionStatus.OVERDUE:
        return <XCircle className="w-4 h-4 text-red-600" />
      case SubmissionStatus.SUBMITTED:
        return <Clock className="w-4 h-4 text-blue-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: SubmissionStatus | null) => {
    switch (status) {
      case SubmissionStatus.GRADED:
        return 'bg-green-100 text-green-700 border-green-200'
      case SubmissionStatus.NEEDS_REVISION:
        return 'bg-amber-100 text-amber-700 border-amber-200'
      case SubmissionStatus.OVERDUE:
        return 'bg-red-100 text-red-700 border-red-200'
      case SubmissionStatus.SUBMITTED:
        return 'bg-blue-100 text-blue-700 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getStatusText = (status: SubmissionStatus | null) => {
    switch (status) {
      case SubmissionStatus.GRADED:
        return 'Graded'
      case SubmissionStatus.NEEDS_REVISION:
        return 'Needs Revision'
      case SubmissionStatus.OVERDUE:
        return 'Overdue'
      case SubmissionStatus.SUBMITTED:
        return 'Submitted'
      default:
        return 'Not Submitted'
    }
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
      {/* Assignment Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                {submission.assignmentTitle}
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Module: {submission.moduleTitle}
            </p>
            {submission.assignmentDescription && (
              <p className="text-sm text-gray-700 mb-4">
                {submission.assignmentDescription}
              </p>
            )}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>Due: {formatDate(submission.assignmentDeadline)}</span>
              </div>
              {submission.isOverdue && (
                <span className="px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-700">
                  Overdue
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(submission.status)}
            <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(submission.status)}`}>
              {getStatusText(submission.status)}
            </span>
          </div>
        </div>
      </div>

      {/* Assignment Files */}
      {submission.assignmentPath && (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Assignment Materials</h4>
          <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-700">Assignment File</span>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-1 text-gray-600 hover:text-gray-900 transition-colors">
                <Download className="w-4 h-4" />
              </button>
              <button className="p-1 text-gray-600 hover:text-gray-900 transition-colors">
                <ExternalLink className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submission Details */}
      {submission.hasSubmission ? (
        <div className="p-6 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-4">Submission Details</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Submitted At:</span>
              <span className="text-sm font-medium text-gray-900">
                {submission.submittedAt ? formatDate(submission.submittedAt) : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Last Updated:</span>
              <span className="text-sm font-medium text-gray-900">
                {submission.updatedAt ? formatDate(submission.updatedAt) : 'N/A'}
              </span>
            </div>
            {submission.submissionPath && (
              <div className="pt-2">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800">Student Submission</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="p-1 text-blue-600 hover:text-blue-800 transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-blue-600 hover:text-blue-800 transition-colors">
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="text-center py-4">
            <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <h4 className="text-sm font-medium text-gray-900 mb-1">No Submission</h4>
            <p className="text-sm text-gray-600">Student has not submitted this assignment yet</p>
          </div>
        </div>
      )}

      {/* Feedback and Status Management */}
      {submission.hasSubmission && submission.id && (
        <div className="p-6">
          <h4 className="text-sm font-medium text-gray-900 mb-4">Review & Feedback</h4>
          
          <div className="space-y-4">
            {/* Status Update */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Update Status
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as SubmissionStatus)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={isUpdating}
              >
                <option value={SubmissionStatus.SUBMITTED}>Under Review</option>
                <option value={SubmissionStatus.GRADED}>Graded</option>
                <option value={SubmissionStatus.NEEDS_REVISION}>Needs Revision</option>
                <option value={SubmissionStatus.OVERDUE}>Mark as Overdue</option>
              </select>
            </div>
            
            {/* Feedback */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Feedback
              </label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="Provide feedback to the student..."
                disabled={isUpdating}
              />
            </div>
            
            {/* Submit Button */}
            <button
              onClick={handleSubmitFeedback}
              disabled={isSubmitting || isUpdating}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors font-medium"
            >
              <Send className="w-4 h-4" />
              {isSubmitting ? 'Updating...' : 'Update Submission'}
            </button>
          </div>
        </div>
      )}
    </div>
  )
} 