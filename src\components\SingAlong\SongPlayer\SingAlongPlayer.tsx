"use client";
import { useState, useRef, useEffect } from 'react';
import { Play, Pause, SkipBack, SkipForward, Volume2, Mic, MicOff } from 'lucide-react';
import { LyricsDisplay } from '@/components/SingAlong/SongPlayer/LyricsDisplay';
import Recorder from '@/components/SingAlong/SongPlayer/Recorder';
import { Song } from '@/types/sing-along';


interface SingAlongPlayerProps {
  song: Song;
  audioUrl: string;
}

export function SingAlongPlayer({ song, audioUrl }: SingAlongPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [showRecorder, setShowRecorder] = useState(false);

  // Update current time
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    console.log("audio", audio, "audioUrl", audioUrl)
    
    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('ended', () => setIsPlaying(false));

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('ended', () => setIsPlaying(false));
    };
  }, []);

  // Update volume
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const skipTime = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = Math.max(0, Math.min(audio.duration || 0, audio.currentTime + seconds));
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (parseFloat(e.target.value) / 100) * (audio.duration || 0);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const progress = audioRef.current?.duration 
    ? (currentTime / audioRef.current.duration) * 100 
    : 0;

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      {/* Hidden audio element */}
      <audio ref={audioRef} src={audioUrl} preload="metadata" crossOrigin='anonymous' />
      
      {/* Song Header */}
      <div className="text-center">
        <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-2">{song.title}</h1>
      </div>

      {/* Main Player Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Lyrics Display */}
        <div className="lg:col-span-2">
          <LyricsDisplay lyrics={song.lyrics} currentTime={currentTime} />
        </div>

        {/* Controls Sidebar */}
        <div className="space-y-6">
          {/* Audio Controls */}
          <div className="bg-white rounded-xl shadow-xl border border-blue-100 p-6">
            <h3 className="text-lg font-bold text-slate-800 mb-4 text-center">Player Controls</h3>
            
            {/* Progress Bar */}
            <div className="mb-6">
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                onChange={handleSeek}
                className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progress}%, #e2e8f0 ${progress}%, #e2e8f0 100%)`
                }}
              />
              <div className="flex justify-between text-sm text-slate-500 mt-1">
                <span>{formatTime(currentTime)}</span>
                <span>{song.duration}</span>
              </div>
            </div>

            {/* Playback Controls */}
            <div className="flex items-center justify-center space-x-4 mb-6">
              <button
                onClick={() => skipTime(-10)}
                className="p-2 text-slate-600 hover:text-blue-600 transition-colors"
              >
                <SkipBack className="h-6 w-6" />
              </button>
              
              <button
                onClick={togglePlay}
                className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              >
                {isPlaying ? (
                  <Pause className="h-8 w-8" />
                ) : (
                  <Play className="h-8 w-8 ml-1" />
                )}
              </button>
              
              <button
                onClick={() => skipTime(10)}
                className="p-2 text-slate-600 hover:text-blue-600 transition-colors"
              >
                <SkipForward className="h-6 w-6" />
              </button>
            </div>

            {/* Volume Control */}
            <div className="flex items-center space-x-3">
              <Volume2 className="h-5 w-5 text-slate-600" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="flex-1 h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>

          {/* Recording Controls */}
          <div className="bg-white rounded-xl shadow-xl border border-red-100 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-slate-800">Record Along</h3>
              <button
                onClick={() => setShowRecorder(!showRecorder)}
                className={`p-2 rounded-lg transition-colors ${
                  showRecorder 
                    ? 'bg-red-100 text-red-600' 
                    : 'bg-slate-100 text-slate-600 hover:bg-red-100 hover:text-red-600'
                }`}
              >
                {showRecorder ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
              </button>
            </div>
            
            {showRecorder && (
              <div className="pt-4 border-t border-slate-200">
                <Recorder songName={song.title} audioElement={audioRef.current} />
              </div>
            )}
            
            {!showRecorder && (
              <p className="text-sm text-slate-600 text-center">
                Click the microphone to start recording your performance
              </p>
            )}
          </div>

          {/* Song Info */}
          <div className="bg-white rounded-xl shadow-xl border border-slate-100 p-6">
            <h3 className="text-lg font-bold text-slate-800 mb-4">Song Information</h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-slate-600">Title:</span>
                <p className="text-slate-800">{song.title}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-slate-600">Duration:</span>
                <p className="text-slate-800">{song.duration}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-slate-600">Lyrics:</span>
                <p className="text-slate-800">{song.lyrics.length} lines</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 