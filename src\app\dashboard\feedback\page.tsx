import { Suspense } from 'react'
import { getUserDetails } from '@/data-access/auth'
import { getUserFeedbackSessions } from '@/data-access/feedback'
import { getSubmittedAssignments } from '@/data-access/assignment/assignment'
import FeedbackDashboard from '@/components/Dashboard/Feedback/FeedbackDashboard'
import FeedbackLoadingSkeleton from '@/components/Dashboard/Feedback/FeedbackLoadingSkeleton'
import { redirect } from 'next/navigation'

interface FeedbackPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function FeedbackPage({ searchParams }: FeedbackPageProps) {
  const { user, error: authError } = await getUserDetails()
  
  if (authError || !user?.email) {
    return redirect('/login')
  }

  const sessionsResult = await getUserFeedbackSessions(user.email)

  if (!sessionsResult.success) {
    throw new Error(sessionsResult.error || 'Failed to load feedback sessions')
  }

  const sessions = sessionsResult.data || []
  
  const submittedAssignmentsResult = await getSubmittedAssignments(user.email)
  
  if (!submittedAssignmentsResult.success) {
    throw new Error(submittedAssignmentsResult.error || 'Failed to load submitted assignments')
  }
  
  const submittedAssignments = submittedAssignmentsResult.data || []

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Feedback Sessions</h1>
          <p className="text-gray-600 mt-2">
            Schedule and manage your feedback sessions with instructors
          </p>
        </div>

        <Suspense fallback={<FeedbackLoadingSkeleton />}>
          <FeedbackDashboard 
            initialSessions={sessions}
            userEmail={user.email}
            submittedAssignments={submittedAssignments}
          />
        </Suspense>
      </div>
    </div>
  )
} 