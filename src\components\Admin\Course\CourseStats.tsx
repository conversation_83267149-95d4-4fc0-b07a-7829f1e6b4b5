import { BookO<PERSON>, Users, Star, TrendingUp } from "lucide-react"
import { CourseDetails } from "@/types/course"

interface CourseStatsProps {
  courses: CourseDetails[]
}

export function CourseStats({ courses }: CourseStatsProps) {
  const totalCourses = courses.length
  const publishedCourses = courses.filter(course => course.isPublished).length
  const totalModules = courses.reduce((total, course) => total + course.modules.length, 0)
  const totalLessons = courses.reduce((total, course) => 
    total + course.modules.reduce((moduleTotal, module) => 
      moduleTotal + module.lessons.length, 0), 0)

  const stats = [
    {
      label: "Total Courses",
      value: totalCourses,
      icon: BookOpen,
      color: "blue",
      bgColor: "bg-white",
      iconColor: "text-blue-600"
    },
    {
      label: "Published",
      value: publishedCourses,
      icon: Users,
      color: "green",
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      label: "Total Modules",
      value: totalModules,
      icon: TrendingUp,
      color: "purple",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      label: "Total Lessons",
      value: totalLessons,
      icon: Star,
      color: "yellow",
      bgColor: "bg-yellow-50",
      iconColor: "text-yellow-600"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <div key={stat.label} className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <Icon className={`w-6 h-6 ${stat.iconColor}`} />
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
} 