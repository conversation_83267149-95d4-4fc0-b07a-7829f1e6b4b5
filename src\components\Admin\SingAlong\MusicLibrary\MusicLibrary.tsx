import { Clock, Music } from 'lucide-react';
import { CourseLevel } from '@prisma/client';
import { Music as MusicType } from '@prisma/client';
import MusicLibraryFilters from './MusicLibraryFilters';
import SortableTableHeader from '../SortableTableHeader';
import MusicLibraryActions from './MusicLibraryActions';

interface MusicLibraryProps {
  songs: MusicType[];
  onSongsUpdate: (songs: MusicType[]) => void;
  searchParams: {
    search?: string;
    genre?: string;
    difficulty?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default function MusicLibrary({ songs, onSongsUpdate, searchParams }: MusicLibraryProps) {
  const searchTerm = searchParams.search || '';
  const selectedGenre = searchParams.genre || 'All';
  const selectedDifficulty = searchParams.difficulty || 'All';
  const sortBy = (searchParams.sortBy as 'title' | 'genre' | 'difficulty' | 'createdAt') || 'createdAt';
  const sortOrder = (searchParams.sortOrder as 'asc' | 'desc') || 'desc';

  // Get unique genres for filter
  const uniqueGenres = Array.from(new Set(songs.map(song => song.genre)));
  const genres = ['All', ...uniqueGenres];

  // Filter songs
  let filteredSongs = songs.filter(song => {
    const matchesSearch = song.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         song.genre.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === 'All' || song.genre === selectedGenre;
    const matchesDifficulty = selectedDifficulty === 'All' || song.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesGenre && matchesDifficulty;
  });

  // Sort songs
  filteredSongs.sort((a, b) => {
    let aValue: any = a[sortBy];
    let bValue: any = b[sortBy];

    if (sortBy === 'createdAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    } else if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const handleDelete = (songId: string) => {
    const updatedSongs = songs.filter(song => song.id !== songId);
    onSongsUpdate(updatedSongs);
  };

  const formatDuration = (duration: string) => {
    if (duration.includes(':')) {
      return duration;
    }
    const seconds = parseInt(duration);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: CourseLevel) => {
    switch (difficulty) {
      case CourseLevel.BEGINNER:
        return 'bg-green-100 text-green-700';
      case CourseLevel.INTERMEDIATE:
        return 'bg-yellow-100 text-yellow-700';
      case CourseLevel.ADVANCED:
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="space-y-6 text-gray-700">
      {/* Search and Filters */}
      <MusicLibraryFilters genres={genres} />

      {/* Results count */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="text-sm text-gray-600">
          Showing {filteredSongs.length} of {songs.length} songs
        </div>
      </div>

      {/* Songs Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {filteredSongs.length === 0 ? (
          <div className="text-center py-12">
            <Music className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No songs found</h3>
            <p className="text-gray-600">
              {songs.length === 0 
                ? "No songs have been uploaded yet." 
                : "Try adjusting your search or filters."}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white border-b border-gray-200">
                <tr>
                  <SortableTableHeader field="title">
                    Song Title
                  </SortableTableHeader>
                  <SortableTableHeader field="genre">
                    Genre
                  </SortableTableHeader>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <SortableTableHeader field="difficulty">
                    Difficulty
                  </SortableTableHeader>
                  <SortableTableHeader field="createdAt">
                    Added
                  </SortableTableHeader>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSongs.map((song) => (
                  <tr key={song.id} className="hover:bg-white">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                            <Music className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{song.title}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{song.genre}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        {formatDuration(song.duration)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDifficultyColor(song.difficulty)}`}>
                        {song.difficulty}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(song.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <MusicLibraryActions songId={song.id} onDelete={handleDelete} />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
