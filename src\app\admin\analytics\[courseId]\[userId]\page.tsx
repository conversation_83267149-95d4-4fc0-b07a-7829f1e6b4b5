import Link from 'next/link'
import { <PERSON><PERSON><PERSON><PERSON>, User, AlertCircle } from 'lucide-react'
import { getUserAssignmentAnalytics } from '@/data-access/analytics/analytics'
import { notFound } from 'next/navigation'
import UserAssignmentAnalyticsClient from './UserAssignmentAnalyticsClient'

interface UserAssignmentAnalyticsPageProps {
  params: Promise<{ 
    courseId: string
    userId: string 
  }>
}

export default async function UserAssignmentAnalyticsPage({ 
  params 
}: UserAssignmentAnalyticsPageProps) {
  const { courseId, userId } = await params
  
  const result = await getUserAssignmentAnalytics(courseId, userId)
  
  if (!result.success) {
    if (result.error === 'User not enrolled in this course' || result.error === 'Course not found') {
      notFound()
    }
    
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Link
                href="/admin/analytics"
                className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </Link>
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Error Loading Analytics</h1>
                <p className="text-red-600">{result.error || 'Failed to load user assignment analytics'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const analyticsData = result.data!

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href={`/admin/analytics/${courseId}`}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <div className="p-2 bg-blue-100 rounded-lg">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Assignment Analytics</h1>
              <p className="text-gray-600">
                {analyticsData.user.name} • {analyticsData.course.name}
              </p>
            </div>
          </div>
        </div>

        {/* Client Component for Interactive Features */}
        <UserAssignmentAnalyticsClient 
          initialData={analyticsData}
          courseId={courseId}
          userId={userId}
        />
      </div>
    </div>
  )
} 