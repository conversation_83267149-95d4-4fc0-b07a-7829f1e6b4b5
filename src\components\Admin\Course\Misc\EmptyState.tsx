import { BookOpen, Plus } from "lucide-react"
import Link from "next/link"

export function EmptyState() {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
      <div className="text-center py-16 px-6">
        <div className="mx-auto w-16 h-16 bg-white rounded-full flex items-center justify-center mb-6">
          <BookOpen className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No courses yet</h3>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          Get started by creating your first course. You can add modules, lessons, and assignments to build comprehensive learning experiences.
        </p>
        <Link
          href="/admin/courses/new"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2 transition-colors font-medium shadow-sm"
        >
          <Plus className="w-5 h-5" />
          Create Your First Course
        </Link>
      </div>
    </div>
  )
} 