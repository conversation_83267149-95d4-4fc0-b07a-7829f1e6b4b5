import { getUserDetails } from "@/data-access/auth";
import { prisma } from "@/utils/prisma/prisma";
import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-sing-along';


const storeAudioInSupabase = async (audio: Blob, fileName: string, mimeType: string) => {
    try {
        const supabase = await createClient();
        const { error } = await supabase.storage.from(BUCKET_NAME).upload(fileName, audio, {
            contentType: mimeType
        });
        if (error) {
            throw error;
        }
    }
    catch (error) {
        console.error(error);
    }
}

const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

const storeAudio = async (audio: Blob, mimeType: string, songName: string, duration: number) => {
    try {
      const { user } = await getUserDetails();
      if (!user?.id) {
        return { success: false, error: 'User not found' }
      }

      const fileName = `recordings/${user.id}/${songName}-${Date.now()}.${mimeType.split('/')[1]}`;
  
      console.log('Storing audio:', {
        fileName,
        songName,
        mimeType,
        size: audio.size,
        duration: formatDuration(duration),
        userId: user.id
      });
  
      await prisma.userRecording.create({
        data: {
          fileName,
          mimeType,
          duration: formatDuration(duration),
          musicTitle: songName,
          userId: user.id
        }
      });
  
      await storeAudioInSupabase(audio, fileName, mimeType);
  
      return { success: true, error: null }
    } 
    catch (error) {
      console.error('Error storing audio:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}; 



export async function POST(request: Request): Promise<NextResponse<{ success: boolean, error: string | null }>> {
    try {
        const formData = await request.formData();
        const audio = formData.get('audio') as Blob;
        const songName = formData.get('songName') as string;
        const duration = formData.get('duration') as unknown as number;
        const mimeType = formData.get('mimeType') as string;

        const result = await storeAudio(audio, mimeType, songName, duration);

        return NextResponse.json(result);
    }
    catch (error) {
        console.error('Error storing audio:', error);
        return NextResponse.json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
    }
}





