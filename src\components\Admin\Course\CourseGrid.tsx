import { Suspense } from "react"
import { CourseDetails } from "@/types/course"
import { CourseCard } from "@/components/Admin/Course/CourseCard"
import { EmptyState } from "@/components/Admin/Course/Misc/EmptyState"

interface CourseGridProps {
  courses: CourseDetails[]
}

export function CourseGrid({ courses }: CourseGridProps) {
  if (courses.length === 0) {
    return <EmptyState />
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      <div className="p-6 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">All Courses</h2>
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            {courses.length} course{courses.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course) => (
            <Suspense key={course.id} fallback={<CourseCardSkeleton />}>
              <CourseCard course={course} />
            </Suspense>
          ))}
        </div>
      </div>
    </div>
  )
}

function CourseCardSkeleton() {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden animate-pulse">
      <div className="h-40 bg-gray-200"></div>
      <div className="p-5 space-y-3">
        <div className="h-5 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        <div className="flex gap-2">
          <div className="h-8 bg-gray-200 rounded flex-1"></div>
          <div className="h-8 bg-gray-200 rounded w-8"></div>
          <div className="h-8 bg-gray-200 rounded w-8"></div>
        </div>
      </div>
    </div>
  )
} 