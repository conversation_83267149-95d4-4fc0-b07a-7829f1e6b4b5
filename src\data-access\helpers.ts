import { getUserRole } from "./auth"

export type ActionResult<T = void> = {
    success: boolean
    data?: T
    error?: string
}
  
export async function requireAdmin(): Promise<ActionResult> {
    "use server"
    return { success: true }
    const userRole = await getUserRole()
    if (userRole !== 'ADMIN') {
        return { success: false, error: 'You are not authorized to perform this action' }
    }
    return { success: true }
}

export function handleError<T = void>(error: unknown, operation: string): ActionResult<T> {
    console.error(`Failed to ${operation}:`, error)
    return { success: false, error: `Failed to ${operation}` }
}