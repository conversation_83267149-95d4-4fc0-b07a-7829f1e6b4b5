'use client'

import { useState, useEffect, useRef } from 'react'
import { FeedbackSessionWithDetails } from '@/data-access/feedback'
import { UserAssignment } from '@/types/assignment'
import { format } from 'date-fns'
import Cal from '@calcom/embed-react'
import { getCalApi } from '@calcom/embed-react'
import { scheduleFeedbackSession, cancelFeedbackSession, getCalLink } from '@/data-access/feedback'
import { useRouter, useSearchParams } from 'next/navigation'
import { ChevronDown, FileText, Calendar, Clock } from 'lucide-react'

interface FeedbackDashboardProps {
  initialSessions: FeedbackSessionWithDetails[]
  userEmail: string
  submittedAssignments: UserAssignment[]
}

export default function FeedbackDashboard({ 
  initialSessions,
  userEmail,
  submittedAssignments
}: FeedbackDashboardProps) {
  const [sessions, setSessions] = useState<FeedbackSessionWithDetails[]>(initialSessions)
  const [selectedAssignmentId, setSelectedAssignmentId] = useState<string | null>(null)
  const [selectedAssignment, setSelectedAssignment] = useState<UserAssignment | null>(null)
  const [isCalOpen, setIsCalOpen] = useState(false)
  const [calLink, setCalLink] = useState<string>('dhwani/feedback-session')
  const [isLoadingCalLink, setIsLoadingCalLink] = useState(false)
  const [showAssignmentSelector, setShowAssignmentSelector] = useState(false)
  const searchParams = useSearchParams()
  
  // Set selected assignment from URL params
  useEffect(() => {
    const assignmentId = searchParams.get('assignment')
    if (assignmentId) {
      const assignment = submittedAssignments.find(a => a.id === assignmentId)
      if (assignment) {
        setSelectedAssignmentId(assignmentId)
        setSelectedAssignment(assignment)
        setIsCalOpen(true)
      }
    }
  }, [searchParams, submittedAssignments])
  
  // Fetch Cal.com link from settings
  useEffect(() => {
    const fetchCalLink = async () => {
      setIsLoadingCalLink(true)
      try {
        const result = await getCalLink()
        if (result.success && result.data) {
          setCalLink(result.data)
        }
      } catch (error) {
        console.error('Failed to fetch Cal.com link:', error)
      } finally {
        setIsLoadingCalLink(false)
      }
    }
    
    fetchCalLink()
  }, [])
  
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const selectedAssignmentIdRef = useRef<string | null>(null)
  
  useEffect(() => {
    selectedAssignmentIdRef.current = selectedAssignmentId
  }, [selectedAssignmentId])

  useEffect(() => {
    (async function () {
      const cal = await getCalApi()
      cal('ui', {
        styles: { branding: { brandColor: '#000000' } },
        hideEventTypeDetails: false,
      })
      
      cal('on', {
        action: 'bookingSuccessfulV2',
        callback: async (e: any) => {
          setIsLoading(true)
          setError(null)

          try {
            const { data, type, namespace } = e.detail
            
            const scheduledAt = data.startTime ? new Date(data.startTime) : null
            const eventUid = data.uid
            
            console.log('Cal.com booking event:', {
              data,
              type,
              namespace,
              userEmail,
              selectedAssignmentId: selectedAssignmentIdRef.current,
              eventUid,
              scheduledAt
            })
            
            if (!scheduledAt || isNaN(scheduledAt.getTime())) {
              setError('Could not read booking start time from Cal.com event')
              return
            }
            
            if (!eventUid) {
              setError('Could not read booking UID from Cal.com event')
              return
            }
            
            const result = await scheduleFeedbackSession(
              userEmail,
              selectedAssignmentIdRef.current, // Use ref to get current value
              eventUid,
              scheduledAt
            )
            
            if (!result.success) {
              setError(result.error || 'Failed to schedule session')
              return
            }
            
            router.refresh()
          } catch (err) {
            setError('An unexpected error occurred')
          } finally {
            setIsLoading(false)
            setIsCalOpen(false)
            setSelectedAssignmentId(null)
            setSelectedAssignment(null)
          }
        }
      })
    })()
    
    // Click outside handler for assignment selector
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.assignment-selector')) {
        setShowAssignmentSelector(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    
    // Clean up event listeners
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleScheduleClick = (assignmentId: string) => {
    const assignment = submittedAssignments.find(a => a.id === assignmentId)
    setSelectedAssignmentId(assignmentId)
    setSelectedAssignment(assignment || null)
    setIsCalOpen(true)
    setShowAssignmentSelector(false)
  }

  const handleGeneralFeedback = () => {
    setSelectedAssignmentId(null)
    setSelectedAssignment(null)
    setIsCalOpen(true)
    setShowAssignmentSelector(false)
  }

  const handleCancelSession = async (sessionId: string) => {
    if (!confirm('Are you sure you want to cancel this feedback session?')) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await cancelFeedbackSession(userEmail, sessionId)
      
      if (!result.success) {
        setError(result.error || 'Failed to cancel session')
        return
      }
      
      setSessions(prev => prev.map(session => 
        session.id === sessionId 
          ? { ...session, status: 'CANCELLED' } 
          : session
      ))
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }



  const upcomingSessions = sessions.filter(
    session => session.status === 'SCHEDULED' && session.scheduledAt && new Date(session.scheduledAt) > new Date()
  )

  const pastSessions = sessions.filter(
    session => session.status === 'COMPLETED' || 
      (session.status === 'SCHEDULED' && session.scheduledAt && new Date(session.scheduledAt) <= new Date())
  )

  const cancelledSessions = sessions.filter(
    session => session.status === 'CANCELLED' || session.status === 'NO_SHOW'
  )
  
  const hasNoSessions = upcomingSessions.length === 0 && pastSessions.length === 0 && cancelledSessions.length === 0

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {isLoading && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
          Processing your request...
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4 text-black">Schedule Feedback</h2>
        <p className="text-gray-600 mb-6">
          Schedule a feedback session with your instructor for a specific assignment or general guidance.
        </p>
        
        {/* Selected Assignment Display */}
        {selectedAssignment && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-900">{selectedAssignment.title}</h3>
                <p className="text-sm text-blue-700">{selectedAssignment.module.course.name} • {selectedAssignment.module.title}</p>
                <p className="text-xs text-blue-600 mt-1">
                  Submitted: {selectedAssignment.submission?.submittedAt ? format(new Date(selectedAssignment.submission.submittedAt), 'MMM d, yyyy') : 'N/A'}
                </p>
              </div>
              <button
                onClick={() => {
                  setSelectedAssignment(null)
                  setSelectedAssignmentId(null)
                  setIsCalOpen(false)
                }}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                Change
              </button>
            </div>
          </div>
        )}
        
        {!isCalOpen && !selectedAssignment && (
          <div className="space-y-4">
            {/* Assignment Selection */}
            <div className="relative assignment-selector">
              <button
                onClick={() => setShowAssignmentSelector(!showAssignmentSelector)}
                className="w-full flex items-center justify-between px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span className="text-gray-700">Select an assignment for feedback</span>
                <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${showAssignmentSelector ? 'rotate-180' : ''}`} />
              </button>
              
              {showAssignmentSelector && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-64 overflow-y-auto">
                  {submittedAssignments.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm">No submitted assignments found</p>
                      <p className="text-xs text-gray-400 mt-1">Submit an assignment first to schedule feedback</p>
                    </div>
                  ) : (
                    <div className="py-2">
                      {submittedAssignments.map((assignment) => (
                        <button
                          key={assignment.id}
                          onClick={() => handleScheduleClick(assignment.id)}
                          className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-start gap-3">
                            <FileText className="h-4 w-4 text-gray-400 mt-1" />
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-gray-900 truncate">{assignment.title}</h4>
                              <p className="text-sm text-gray-600 truncate">{assignment.module.course.name} • {assignment.module.title}</p>
                              <div className="flex items-center gap-4 mt-1">
                                <span className="text-xs text-gray-500">
                                  Submitted: {assignment.submission?.submittedAt ? format(new Date(assignment.submission.submittedAt), 'MMM d') : 'N/A'}
                                </span>
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                  assignment.submission?.status === 'GRADED' ? 'bg-green-100 text-green-700' :
                                  assignment.submission?.status === 'NEEDS_REVISION' ? 'bg-amber-100 text-amber-700' :
                                  'bg-blue-100 text-blue-700'
                                }`}>
                                  {assignment.submission?.status === 'GRADED' ? 'Graded' :
                                   assignment.submission?.status === 'NEEDS_REVISION' ? 'Needs Revision' :
                                   'Under Review'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {/* General Feedback Option */}
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-3">Or</p>
              <button
                onClick={handleGeneralFeedback}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                disabled={isLoadingCalLink}
              >
                {isLoadingCalLink ? 'Loading...' : 'Schedule General Feedback'}
              </button>
            </div>
          </div>
        )}
        
        {!isCalOpen && selectedAssignment && (
          <button
            onClick={() => setIsCalOpen(true)}
            className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            disabled={isLoadingCalLink}
          >
            {isLoadingCalLink ? 'Loading...' : 'Open Calendar'}
          </button>
        )}
        
        {isCalOpen && (
          <div className="mt-4">
            <Cal
              calLink={calLink?.replace('https://cal.com/', '')}
              config={{
                name: userEmail.split('@')[0],
                email: userEmail,
                notes: selectedAssignment 
                  ? `Assignment feedback session for: ${selectedAssignment.title} (${selectedAssignment.module.course.name})`
                  : 'General feedback session',
              }}
            />
            <div id="cal-booking-placeholder" style={{ display: 'none' }}></div>
            <button
              onClick={() => {
                setIsCalOpen(false)
                setShowAssignmentSelector(false)
              }}
              className="mt-4 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Close Calendar
            </button>
          </div>
        )}
      </div>

      {hasNoSessions && (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <div className="py-6">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No feedback sessions</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't scheduled any feedback sessions yet.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setIsCalOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Schedule a session
              </button>
            </div>
          </div>
        </div>
      )}

      {upcomingSessions.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Upcoming Sessions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {upcomingSessions.map(session => (
              <div key={session.id} className="border rounded-lg p-4">
                <h3 className="font-medium">
                  {session.assignment?.title || 'General Feedback'}
                </h3>
                <p className="text-sm text-gray-600 mt-1">{session.course.name}</p>
                <p className="text-sm font-medium mt-2">
                  {session.scheduledAt ? format(new Date(session.scheduledAt), 'MMM d, yyyy • h:mm a') : 'Date TBD'}
                </p>
                <button
                  onClick={() => handleCancelSession(session.id)}
                  disabled={isLoading}
                  className="mt-3 text-sm text-red-600 hover:text-red-800"
                >
                  Cancel Session
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {pastSessions.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Past Sessions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {pastSessions.map(session => (
              <div key={session.id} className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-medium">
                  {session.assignment?.title || 'General Feedback'}
                </h3>
                <p className="text-sm text-gray-600 mt-1">{session.course.name}</p>
                <p className="text-sm font-medium mt-2">
                  {session.scheduledAt ? format(new Date(session.scheduledAt), 'MMM d, yyyy • h:mm a') : 'Date TBD'}
                </p>
                <span className="inline-block mt-2 px-2 py-1 text-xs rounded bg-gray-200">
                  {session.status === 'COMPLETED' ? 'Completed' : 
                   (session.scheduledAt && new Date(session.scheduledAt) <= new Date()) ? 'Past Due' : session.status}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {cancelledSessions.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Cancelled Sessions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {cancelledSessions.map(session => (
              <div key={session.id} className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-medium">
                  {session.assignment?.title || 'General Feedback'}
                </h3>
                <p className="text-sm text-gray-600 mt-1">{session.course.name}</p>
                <p className="text-sm font-medium mt-2">
                  {format(new Date(session.scheduledAt), 'MMM d, yyyy • h:mm a')}
                </p>
                <span className="inline-block mt-2 px-2 py-1 text-xs rounded bg-red-100 text-red-800">
                  {session.status === 'NO_SHOW' ? 'No Show' : 'Cancelled'}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
} 