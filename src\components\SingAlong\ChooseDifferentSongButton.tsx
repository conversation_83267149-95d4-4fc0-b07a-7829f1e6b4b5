"use client";
import { useRouter } from 'next/navigation';
import { RotateCcw } from 'lucide-react';

export function ChooseDifferentSongButton() {
  const router = useRouter();

  const handleClick = () => {
    router.push('/dashboard/sing-along/play');
  };

  return (
    <button
      onClick={handleClick}
      className="group inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors duration-300"
    >
      <RotateCcw size={20} className="mr-2 transition-transform duration-300 group-hover:-rotate-45" />
      Choose Different Song
    </button>
  );
} 