"use client";
import { useState, useRef, useEffect } from 'react';
import { Play, Pause } from 'lucide-react';

interface CustomAudioPlayerProps {
  src: string;
  className?: string;
  compact?: boolean;
  duration: string;
  audioRef: React.RefObject<HTMLAudioElement>;
}

export function CustomAudioPlayer({ src, duration, className = "", compact = false, audioRef }: CustomAudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Convert duration string "mm:ss" to seconds
  const parseDuration = (durationStr: string): number => {
    const [minutes, seconds] = durationStr.split(':').map(Number);
    return (minutes * 60) + seconds;
  };

  const durationInSeconds = parseDuration(duration);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // Reset states when src changes
    setIsPlaying(false);
    setCurrentTime(0);

    const updateTime = () => {
      if (!isDragging) {
        const time = audio.currentTime;
        if (isFinite(time)) {
          setCurrentTime(time);
        }
      }
    };
    
    const handleEnded = () => setIsPlaying(false);

    const handleProgress = () => {
      console.log('Buffered ranges:', audio.buffered.length);
      for (let i = 0; i < audio.buffered.length; i++) {
        console.log(`Buffered range ${i}:`, audio.buffered.start(i), 'to', audio.buffered.end(i));
      }
    };

    // Listen to events
    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('progress', handleProgress);
    audio.addEventListener('ended', handleEnded);

    // Force load the audio with preload
    audio.preload = 'metadata';
    audio.load();

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('progress', handleProgress);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [src, isDragging]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio || !durationInSeconds || !isFinite(durationInSeconds)) return;

    const newTime = (Number(e.target.value) / 100) * durationInSeconds;
    if (!isFinite(newTime) || newTime < 0 || newTime > durationInSeconds) return;

    // Check if the time is within seekable ranges
    let canSeek = false;
    for (let i = 0; i < audio.seekable.length; i++) {
      if (newTime >= audio.seekable.start(i) && newTime <= audio.seekable.end(i)) {
        canSeek = true;
        break;
      }
    }

    if (canSeek || audio.seekable.length === 0) {
      try {
        audio.currentTime = newTime;
        setCurrentTime(newTime);
        console.log('Seeking to:', newTime);
      } catch (error) {
        console.error('Seek error:', error);
      }
    } else {
      console.warn('Cannot seek to time:', newTime, 'not in seekable range');
    }
  };

  const handleMouseDown = () => setIsDragging(true);
  const handleMouseUp = () => setIsDragging(false);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = durationInSeconds && isFinite(durationInSeconds) && durationInSeconds > 0 
    ? Math.min(Math.max((currentTime / durationInSeconds) * 100, 0), 100) 
    : 0;

  if (compact) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <audio ref={audioRef} src={src} crossOrigin='anonymous'/>
        
        <button
          onClick={togglePlay}
          className="group w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
        >
          {isPlaying ? (
            <Pause className="w-4 h-4 group-hover:scale-110 transition-transform" />
          ) : (
            <Play className="w-4 h-4 ml-0.5 group-hover:scale-110 transition-transform" />
          )}
        </button>

        <div className="flex-1 relative">
          <div className="relative h-2 bg-slate-200 rounded-full overflow-hidden">
            <div 
              className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-150"
              style={{ width: `${progress}%` }}
            />
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={progress}
            onChange={handleSeek}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            className="absolute inset-0 w-full h-2 opacity-0 cursor-pointer"
          />
        </div>

        <span className="text-sm font-medium text-slate-600 w-12 text-right">
          {formatTime(currentTime)}
        </span>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl p-6 border border-slate-200 shadow-lg ${className}`}>
      <audio ref={audioRef} src={src} crossOrigin='anonymous'/>
      
      <div className="flex items-center space-x-6">
        <button
          onClick={togglePlay}
          className="group w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
        >
          {isPlaying ? (
            <Pause className="w-6 h-6 group-hover:scale-110 transition-transform" />
          ) : (
            <Play className="w-6 h-6 ml-1 group-hover:scale-110 transition-transform" />
          )}
        </button>

        <div className="flex-1">
          <div className="relative h-3 bg-slate-200 rounded-full overflow-hidden mb-2">
            <div 
              className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-150"
              style={{ width: `${progress}%` }}
            />
            <div 
              className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white border-2 border-blue-500 rounded-full shadow-md transition-all duration-150"
              style={{ left: `calc(${progress}% - 8px)` }}
            />
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={progress}
            onChange={handleSeek}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            className="absolute w-full h-3 opacity-0 cursor-pointer"
            style={{ marginTop: '-14px' }}
          />
          <div className="flex justify-between text-sm font-medium text-slate-600">
            <span>{formatTime(currentTime)}</span>
            <span>{duration}</span>
          </div>
        </div>
      </div>
    </div>
  );
} 