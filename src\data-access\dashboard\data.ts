"use server"

import { prisma } from '@/utils/prisma/prisma'
import { Course, Enrollment } from '@prisma/client'

// Minimal extensions to Prisma types
export type CourseWithStats = Course & {
  moduleCount: number
  lessonCount: number
  assignmentCount: number
}

export type EnrollmentWithProgress = Enrollment & {
  course: CourseWithStats
  progressPercentage: number
  completedLessons: number
  completedAssignments: number
  completedModules: number
}

export type UpcomingDeadline = {
  id: string
  title: string
  type: 'assignment' | 'feedback'
  deadline: Date
  courseName: string
}

// Result pattern for better error handling
type Result<T> = { success: true; data: T } | { success: false; error: string }

export async function getFeaturedCourses(): Promise<Result<CourseWithStats[]>> {
 
  try {
    const courses = await prisma.course.findMany({
      where: { isPublished: true },
      include: {
        _count: {
          select: {
            modules: true,
            enrollments: true
          }
        },
        modules: {
          include: {
            _count: {
              select: {
                lessons: true,
                assignments: true
              }
            }
          }
        }
      },
      take: 6,
      orderBy: { createdAt: 'desc' }
    })

    const coursesWithStats: CourseWithStats[] = courses.map(course => ({
      ...course,
      moduleCount: course._count.modules,
      lessonCount: course.modules.reduce((sum, module) => sum + module._count.lessons, 0),
      assignmentCount: course.modules.reduce((sum, module) => sum + module._count.assignments, 0)
    }))

    return { success: true, data: coursesWithStats }
  } catch (error) {
    console.error('Error fetching featured courses:', error)
    return { success: false, error: 'Failed to fetch courses' }
  }
}

export async function getUserEnrollments(email: string): Promise<Result<EnrollmentWithProgress[]>> {
  
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const enrollments = await prisma.enrollment.findMany({
      where: { 
        userId: user.id,
        completedAt: null 
      },
      include: {
        course: {
          include: {
            _count: {
              select: {
                modules: true
              }
            },
            modules: {
              include: {
                _count: {
                  select: {
                    lessons: true,
                    assignments: true
                  }
                }
              }
            }
          }
        }
      }
    })

    // Get completion stats for each enrollment
    const enrollmentsWithProgress = await Promise.all(
      enrollments.map(async (enrollment) => {
        const [completedLessons, completedModules, completedAssignments] = await Promise.all([
          prisma.lesson.count({
            where: {
              module: { courseId: enrollment.courseId },
              completedByUsers: { some: { id: user.id } }
            }
          }),
          prisma.module.count({
            where: {
              courseId: enrollment.courseId,
              completedByUsers: { some: { id: user.id } }
            }
          }),
          prisma.assignmentSubmission.count({
            where: {
              userId: user.id,
              assignment: { module: { courseId: enrollment.courseId } },
              status: { in: ['SUBMITTED', 'GRADED'] }
            }
          })
        ])

        const course = enrollment.course
        const totalLessons = course.modules.reduce((sum, m) => sum + m._count.lessons, 0)
        const totalModules = course._count.modules
        const totalAssignments = course.modules.reduce((sum, m) => sum + m._count.assignments, 0)

        // Simple progress calculation: average of completion percentages
        const lessonProgress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 100
        const assignmentProgress = totalAssignments > 0 ? (completedAssignments / totalAssignments) * 100 : 100
        
        const progressPercentage = Math.round((lessonProgress + assignmentProgress) / 2)

        const courseWithStats: CourseWithStats = {
          ...course,
          moduleCount: totalModules,
          lessonCount: totalLessons,
          assignmentCount: totalAssignments
        }

        return {
          ...enrollment,
          course: courseWithStats,
          progressPercentage,
          completedLessons,
          completedAssignments,
          completedModules
        }
      })
    )

    return { success: true, data: enrollmentsWithProgress }
  } catch (error) {
    console.error('Error fetching user enrollments:', error)
    return { success: false, error: 'Failed to fetch enrollments' }
  }
}

export async function getUpcomingDeadlines(email: string): Promise<Result<UpcomingDeadline[]>> {
  
  try {

    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true }
    })

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    const now = new Date()
    const twoWeeksFromNow = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000)

    const [assignments, feedbackSessions] = await Promise.all([
      prisma.assignment.findMany({
        where: {
          deadline: { gte: now, lte: twoWeeksFromNow },
          module: {
            course: {
              enrollments: { some: { userId: user.id, completedAt: null } }
            }
          }
        },
        include: {
          module: {
            select: {
              course: { select: { name: true } }
            }
          }
        },
        orderBy: { deadline: 'asc' },
        take: 10
      }),
      prisma.feedbackSession.findMany({
        where: {
          userId: user.id,
          scheduledAt: { gte: now, lte: twoWeeksFromNow },
          status: 'SCHEDULED'
        },
        include: {
          course: { select: { name: true } }
        },
        orderBy: { scheduledAt: 'asc' },
        take: 5
      })
    ])

    const deadlines: UpcomingDeadline[] = [
      ...assignments.map(assignment => ({
        id: assignment.id,
        title: assignment.title,
        type: 'assignment' as const,
        deadline: assignment.deadline,
        courseName: assignment.module.course.name
      })),
      ...feedbackSessions.map(session => ({
        id: session.id,
        title: `Feedback Session`,
        type: 'feedback' as const,
        deadline: session.scheduledAt,
        courseName: session.course.name
      }))
    ]

    deadlines.sort((a, b) => a.deadline.getTime() - b.deadline.getTime())

    return { success: true, data: deadlines.slice(0, 5) }
  } catch (error) {
    console.error('Error fetching upcoming deadlines:', error)
    return { success: false, error: 'Failed to fetch deadlines' }
  }
}