'use client'

import { useState } from 'react'
import Link from 'next/link'
import { UserAssignment } from '@/types/assignment'
import { SubmissionStatus } from '@prisma/client'

interface AssignmentViewProps {
  assignment: UserAssignment
}

export function AssignmentView({ assignment }: AssignmentViewProps) {
  const getStatusColor = () => {
    if (assignment.isOverdue) return 'text-red-700 bg-red-50 border-red-200'
    if (assignment.submission?.status === SubmissionStatus.GRADED) return 'text-green-700 bg-green-50 border-green-200'
    if (assignment.submission?.status === SubmissionStatus.SUBMITTED) return 'text-blue-700 bg-white border-blue-200'
    if (assignment.submission?.status === SubmissionStatus.NEEDS_REVISION) return 'text-orange-700 bg-orange-50 border-orange-200'
    return 'text-gray-700 bg-white border-gray-200'
  }

  const getStatusText = () => {
    if (assignment.isOverdue) return 'Overdue'
    if (assignment.submission?.status === SubmissionStatus.GRADED) return 'Graded'
    if (assignment.submission?.status === SubmissionStatus.SUBMITTED) return 'Submitted'
    if (assignment.submission?.status === SubmissionStatus.NEEDS_REVISION) return 'Needs Revision'
    return 'Pending'
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const formatDateMobile = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const getTimeRemaining = () => {
    if (assignment.isOverdue) {
      const daysOverdue = Math.abs(assignment.daysUntilDeadline)
      return {
        text: `${daysOverdue} day${daysOverdue === 1 ? '' : 's'} overdue`,
        color: 'text-red-600',
        urgent: true
      }
    }
    if (assignment.daysUntilDeadline === 0) {
      return { text: 'Due today', color: 'text-orange-600', urgent: true }
    }
    if (assignment.daysUntilDeadline === 1) {
      return { text: 'Due tomorrow', color: 'text-orange-600', urgent: true }
    }
    if (assignment.daysUntilDeadline <= 3) {
      return { text: `Due in ${assignment.daysUntilDeadline} days`, color: 'text-yellow-600', urgent: false }
    }
    return { text: `Due in ${assignment.daysUntilDeadline} days`, color: 'text-gray-600', urgent: false }
  }

  const timeRemaining = getTimeRemaining()

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8">
        {/* Header */}
        <div className="mb-6 lg:mb-8">
          <Link 
            href="/dashboard/assignments"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4 sm:mb-6 transition-colors touch-manipulation"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Assignments
          </Link>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3 break-words">
                  {assignment.title}
                </h1>
                <div className="flex flex-col sm:flex-row sm:items-center text-gray-600 gap-1 sm:gap-4">
                  <span className="flex items-center text-sm sm:text-base">
                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <span className="truncate">{assignment.module.course.name}</span>
                  </span>
                  <span className="hidden sm:block text-gray-400">•</span>
                  <span className="text-sm sm:text-base pl-6 sm:pl-0 truncate">{assignment.module.title}</span>
                </div>
              </div>
              <div className="flex flex-row sm:flex-col items-start sm:items-end gap-3 sm:gap-3">
                <span className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium border ${getStatusColor()} whitespace-nowrap`}>
                  {getStatusText()}
                </span>
                <span className={`text-xs sm:text-sm font-medium ${timeRemaining.color} whitespace-nowrap`}>
                  {timeRemaining.text}
                </span>
              </div>
            </div>
            
            <div className="flex items-center text-xs sm:text-sm text-gray-600 pt-4 border-t border-gray-100">
              <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="hidden sm:inline">Due: {formatDate(assignment.deadline)}</span>
              <span className="sm:hidden">Due: {formatDateMobile(assignment.deadline)}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6">
            {/* Assignment Details */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                Assignment Description
              </h2>
              
              {assignment.description ? (
                <div className="prose prose-sm sm:prose max-w-none">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap text-sm sm:text-base">
                    {assignment.description}
                  </p>
                </div>
              ) : (
                <p className="text-gray-500 italic text-sm sm:text-base">
                  No description provided for this assignment.
                </p>
              )}
            </div>

            {/* Submission Section */}
            {assignment.submission && (
              <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">
                  Your Submission
                </h2>
                
                <div className="space-y-4">
                  <div className="bg-white rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                      <span className="text-sm font-medium text-gray-700">Submitted on:</span>
                      <span className="text-sm text-gray-600">
                        <span className="hidden sm:inline">{formatDate(assignment.submission.submittedAt)}</span>
                        <span className="sm:hidden">{formatDateMobile(assignment.submission.submittedAt)}</span>
                      </span>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <span className="text-sm font-medium text-gray-700">Status:</span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor()} w-fit`}>
                        {getStatusText()}
                      </span>
                    </div>
                  </div>

                  {assignment.submission.feedback && (
                    <div className="bg-white border border-blue-200 rounded-lg p-3 sm:p-4">
                      <h3 className="text-sm font-semibold text-blue-900 mb-3">
                        Instructor Feedback
                      </h3>
                      <p className="text-sm text-blue-800 leading-relaxed whitespace-pre-wrap">
                        {assignment.submission.feedback}
                      </p>
                    </div>
                  )}

                  {assignment.submission.status === SubmissionStatus.NEEDS_REVISION && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 sm:p-4">
                      <p className="text-sm text-orange-800">
                        This assignment needs revision. Please review the feedback and resubmit.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Assignment Info */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Assignment Details
              </h3>
              
              <div className="space-y-4">
                <div>
                  <span className="text-sm text-gray-600 block mb-1">Course</span>
                  <span className="text-sm font-medium text-gray-900 break-words">
                    {assignment.module.course.name}
                  </span>
                </div>
                
                <div>
                  <span className="text-sm text-gray-600 block mb-1">Module</span>
                  <span className="text-sm font-medium text-gray-900 break-words">
                    {assignment.module.title}
                  </span>
                </div>

                <div>
                  <span className="text-sm text-gray-600 block mb-1">Due Date</span>
                  <span className="text-sm font-medium text-gray-900">
                    <span className="hidden sm:inline">{formatDate(assignment.deadline)}</span>
                    <span className="sm:hidden">{formatDateMobile(assignment.deadline)}</span>
                  </span>
                </div>

                <div>
                  <span className="text-sm text-gray-600 block mb-1">Time Remaining</span>
                  <span className={`text-sm font-medium ${timeRemaining.color}`}>
                    {timeRemaining.text}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              
              <div className="space-y-3">
                {assignment.submission?.status === SubmissionStatus.SUBMITTED && (
                  <div className="mb-3">
                    <Link
                      href={`/dashboard/feedback?assignmentId=${assignment.id}`}
                      className="flex items-center justify-between w-full px-4 py-3 text-sm text-#000000 bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors touch-manipulation"
                    >
                      <span>Schedule Feedback Session</span>
                      <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </Link>
                  </div>
                )}
                
                <Link
                  href={`/courses/${assignment.module.course.id}`}
                  className="flex items-center justify-between w-full px-4 py-3 text-sm text-gray-700 bg-white rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
                >
                  <span>Go to Course</span>
                  <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                
                <Link
                  href="/dashboard/assignments"
                  className="flex items-center justify-between w-full px-4 py-3 text-sm text-gray-700 bg-white rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
                >
                  <span>All Assignments</span>
                  <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 