import { useState, useRef, useEffect } from 'react'
import { CheckCircle2, Clock, ArrowRight, ChevronLeft, BarChart3, Music, Play, X, Search } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Video, Music as MusicType } from '@prisma/client'
import MuxVideo from './VideoPlayer'
import { CustomAudioPlayer } from '@/components/SingAlong/SongPlayer/CustomAudioPlayer'
import Recorder from '@/components/SingAlong/SongPlayer/Recorder'
import { getAvailableSongs, getAudioUrl } from '@/data-access/sing-along/songs'

interface SelectedLesson {
  id: string
  title: string
  video: Video
}

interface LessonViewProps {
  lesson: SelectedLesson
  videoProgress: number
  isCompleted: boolean
  canMarkComplete: boolean
  canGoPrevious: boolean
  canGoNext: boolean
  isPending: boolean
  onVideoProgress: (progress: number) => void
  onVideoComplete: () => void
  onMarkComplete: () => void
  onPreviousLesson: () => void
  onNextLesson: () => void
}

export default function LessonView({
  lesson,
  videoProgress,
  isCompleted,
  canMarkComplete,
  canGoPrevious,
  canGoNext,
  isPending,
  onVideoProgress,
  onVideoComplete,
  onMarkComplete,
  onPreviousLesson,
  onNextLesson
}: LessonViewProps) {
  // Audio practice state
  const [showMusicSelector, setShowMusicSelector] = useState(false)
  const [showRecorder, setShowRecorder] = useState(false)
  const [availableSongs, setAvailableSongs] = useState<MusicType[]>([])
  const [selectedSong, setSelectedSong] = useState<MusicType | null>(null)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const audioRef = useRef<HTMLAudioElement>(null)

  // Load available songs on component mount
  useEffect(() => {
    const fetchSongs = async () => {
      const { songs, error } = await getAvailableSongs()
      if (!error) {
        setAvailableSongs(songs)
      }
    }
    fetchSongs()
  }, [])

  // Filter songs based on search term only
  const filteredSongs = availableSongs.filter(song => 
    song.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSongSelect = async (song: MusicType) => {
    setSelectedSong(song)
    setIsLoadingAudio(true)
    setShowMusicSelector(false)
    
    try {
      const { audioUrl: url, error } = await getAudioUrl(song.title)
      if (url && !error) {
        setAudioUrl(url)
      } else {
        alert(`Failed to load audio: ${error}`)
      }
    } catch (error) {
      console.error('Error loading audio:', error)
      alert('Failed to load audio file')
    } finally {
      setIsLoadingAudio(false)
    }
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Video Player */}
      <div className="bg-black relative">
        <div className="aspect-video">
          {lesson.video && lesson.video.playback_id ? (
            <MuxVideo
              key={lesson.video.id}
              video={lesson.video}
              videoProgress={videoProgress}
              onProgress={onVideoProgress}
              onComplete={onVideoComplete}
              requiredProgress={70}
              completed={isCompleted}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <div className="text-center">
                <Play className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No video available</p>
              </div>
            </div>
          )}
        </div>
        {/* Video Progress Bar */}
        {videoProgress > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-800 bg-opacity-50">
            <div 
              className="h-1 bg-blue-600 transition-all duration-300"
              style={{ width: `${videoProgress}%` }}
            />
          </div>
        )}
      </div>

      {/* Content Container */}
      <div className="flex-1 p-6 space-y-6">
        {/* Lesson Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">
            {lesson.title}
          </h1>
          
          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-2 h-2 rounded-full",
                isCompleted ? "bg-green-500" : 
                videoProgress >= 70 ? "bg-yellow-500" : "bg-gray-400"
              )}></div>
              <span>
                {isCompleted ? "Completed" : `${Math.round(videoProgress)}% watched`}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              <span>Beginner Level</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>15 minutes</span>
            </div>
          </div>

          <button 
            onClick={onMarkComplete}
            disabled={!canMarkComplete || isPending}
            className={cn(
              "inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors text-sm",
              isCompleted 
                ? 'bg-green-50 text-green-700 border border-green-200 cursor-not-allowed' 
                : canMarkComplete
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-500 cursor-not-allowed'
            )}
          >
            <CheckCircle2 className="w-4 h-4" />
            {isPending ? "Saving..." :
             isCompleted ? "Lesson Completed" : 
             canMarkComplete ? "Mark as Complete" : "Watch 70% to complete"}
          </button>
        </div>

        {/* Audio Practice Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-6">
            <Music className="w-5 h-5 text-purple-600" />
            <h2 className="text-lg font-semibold text-gray-900">Practice Along</h2>
          </div>

          {/* Music Selection */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h3 className="text-sm font-medium text-gray-700">Select Practice Music</h3>
              <button
                onClick={() => setShowMusicSelector(!showMusicSelector)}
                className={cn(
                  "inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors text-sm",
                  showMusicSelector 
                    ? "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                )}
              >
                <Music className="w-4 h-4" />
                {showMusicSelector ? 'Hide Library' : 'Browse Library'}
              </button>
            </div>

            {/* Selected Song Display */}
            {selectedSong && (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between gap-3">
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    <Music className="w-5 h-5 text-blue-600 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-blue-900 truncate">{selectedSong.title}</p>
                      <p className="text-xs text-blue-700">Duration: {selectedSong.duration}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedSong(null)
                      setAudioUrl(null)
                    }}
                    className="p-1 hover:bg-blue-200 rounded transition-colors flex-shrink-0"
                  >
                    <X className="w-4 h-4 text-blue-600" />
                  </button>
                </div>
              </div>
            )}

            {/* Music Selector */}
            {showMusicSelector && (
              <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                {/* Search Bar */}
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search songs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                  />
                </div>

                {/* Songs List */}
                <div className="max-h-64 overflow-y-auto">
                  {filteredSongs.length === 0 ? (
                    <div className="text-center py-8">
                      <Music className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">No songs found</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredSongs.map((song) => (
                        <button
                          key={song.id}
                          onClick={() => handleSongSelect(song)}
                          className="w-full p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all text-left"
                        >
                          <div className="flex items-center gap-3">
                            <Play className="w-4 h-4 text-gray-400 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">{song.title}</p>
                              <p className="text-xs text-gray-500">Duration: {song.duration}</p>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Audio Player */}
          {audioUrl && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Audio Player</h3>
              {isLoadingAudio ? (
                <div className="p-4 bg-gray-50 rounded-lg text-center">
                  <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">Loading audio...</p>
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4">
                  <CustomAudioPlayer 
                    src={audioUrl} 
                    duration={selectedSong?.duration || "0:00"}
                    compact={false}
                    audioRef={audioRef}
                  />
                </div>
              )}
            </div>
          )}

          {/* Recording Section */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
              <h3 className="text-sm font-medium text-gray-700">Record Your Practice</h3>
              <button
                onClick={() => setShowRecorder(!showRecorder)}
                disabled={!selectedSong || !audioUrl}
                className={cn(
                  "inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors text-sm",
                  (!selectedSong || !audioUrl)
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : showRecorder
                    ? "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    : "bg-purple-600 text-white hover:bg-purple-700"
                )}
              >
                {showRecorder ? 'Hide Recorder' : 'Show Recorder'}
              </button>
            </div>

            {showRecorder && selectedSong && audioUrl ? (
              <div className="bg-gray-50 rounded-lg p-4">
                <Recorder 
                  songName={selectedSong?.title || ''}
                  audioElement={audioRef.current}
                />
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-lg text-center">
                <p className="text-sm text-gray-600">
                  {selectedSong 
                    ? "Click 'Show Recorder' to start recording your practice session."
                    : "Select a practice track first to enable recording."
                  }
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <button 
              onClick={onPreviousLesson}
              disabled={!canGoPrevious}
              className={cn(
                "inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-colors text-sm font-medium",
                canGoPrevious 
                  ? "text-gray-700 hover:bg-gray-100" 
                  : "text-gray-400 cursor-not-allowed"
              )}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous Lesson
            </button>
            
            <button 
              onClick={onNextLesson}
              disabled={!canGoNext}
              className={cn(
                "inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-colors text-sm font-medium",
                canGoNext 
                  ? "bg-blue-600 text-white hover:bg-blue-700" 
                  : "bg-gray-100 text-gray-400 cursor-not-allowed"
              )}
            >
              Next Lesson
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 