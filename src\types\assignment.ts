import { Assignment as PrismaAssignment, AssignmentSubmission as PrismaAssignmentSubmission, SubmissionStatus, Prisma } from "@prisma/client"

export type Assignment = PrismaAssignment

export type AssignmentSubmission = PrismaAssignmentSubmission

export type AssignmentWithModule = Prisma.AssignmentGetPayload<{
  include: {
    module: {
      include: {
        course: true
      }
    }
  }
}>

export type AssignmentWithSubmission = Prisma.AssignmentGetPayload<{
  include: {
    module: {
      include: {
        course: true
      }
    }
    submissions: {
      include: {
        user: true
      }
    }
  }
}>

export type UserAssignment = Assignment & {
  module: {
    id: string
    title: string
    course: {
      id: string
      name: string
    }
  }
  submission?: AssignmentSubmission | null
  isOverdue: boolean
  daysUntilDeadline: number
}

export type AssignmentStats = {
  total: number
  submitted: number
  graded: number
  overdue: number
  pending: number
}

export { SubmissionStatus } 