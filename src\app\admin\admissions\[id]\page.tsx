import { notFound } from 'next/navigation';
import { getApplicationById, getAudioUrl } from '@/data-access/auditions/auditions';
import { AuditionDetailView } from '@/components/Admin/Auditions/AuditionDetailView';

interface AuditionDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function AuditionDetailPage({ params }: AuditionDetailPageProps) {

  const { id } = await params;

  const { success, data: application, error } = await getApplicationById(id);
  if (!success) {
    notFound();
  }

  if(!application.audioPath) {
    application.audioPath = null;
  }

  const { success: audioSuccess, data: audioUrl, error: audioError } = await getAudioUrl(application.audioPath);
  if (!audioSuccess) {
    application.audioPath = null;
  }

  if (!success || !application) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto text-gray-700 p-4 sm:p-6 lg:p-8">
        <AuditionDetailView application={application} audioUrl={audioUrl} />
      </div>
    </div>
  );
}
