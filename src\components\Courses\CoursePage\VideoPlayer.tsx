"use client";
import React, { useRef, useEffect, useState } from "react";
import MuxP<PERSON> from "@mux/mux-player-react";
import { Video } from "@prisma/client";
import MuxPlayerElement from "@mux/mux-player/.";
import getVideoUrl from "@/data-access/mux/signing";

interface MuxVideoProps {
  video: Video;
  title?: string;
  onProgress?: (progress: number) => void;
  onComplete?: () => void;
  requiredProgress?: number;
  completed: boolean;
  videoProgress?: number;
}

const MuxVideo: React.FC<MuxVideoProps> = ({
  video,
  title,
  onProgress,
  onComplete,
  requiredProgress = 70,
  completed = false,
  videoProgress,
}) => {
  const playerRef = useRef<HTMLVideoElement>(null);
  const maxWatchedTime = useRef(0);
  const [progress, setProgress] = useState(0);
  const hasCalledComplete = useRef(false);
  const [videoToken, setVideoToken] = useState<string | null>(null)
  
  // Reset state when video changes
  useEffect(() => {
    maxWatchedTime.current = 0;
    setProgress(0);
    hasCalledComplete.current = false;
    setVideoToken(null); // Reset token when video changes
    
    getVideoUrl(video.playback_id).then((url) => {
      if(url) {
        setVideoToken(url.token)
      }
    })
  }, [video.playback_id]); // Remove videoToken from dependency array

  // Sync external progress with internal state
  useEffect(() => {
    if (videoProgress !== undefined && videoProgress !== progress) {
      setProgress(videoProgress);
    }
  }, [videoProgress, progress]);

  const handleTimeUpdate = () => {
    const player = playerRef.current;
    if (!player) return;

    const currentTime = player.currentTime;
    const duration = player.duration;
    
    if (duration === 0) return;

    // If video is completed, allow free seeking
    if (completed) {
      const progressPercent = (currentTime / duration) * 100;
      setProgress(progressPercent);
      onProgress?.(progressPercent);
      return;
    }

    // For incomplete videos, restrict seeking beyond watched content
    if (currentTime > maxWatchedTime.current + 1) {
      player.currentTime = maxWatchedTime.current;
      return;
    }

    // Update max watched time and progress
    maxWatchedTime.current = Math.max(maxWatchedTime.current, currentTime);
    const progressPercent = (currentTime / duration) * 100;
    setProgress(progressPercent);
    onProgress?.(progressPercent);

    // Check completion
    if (progressPercent >= requiredProgress && !hasCalledComplete.current) {
      hasCalledComplete.current = true;
      onComplete?.();
    }
  };

  return (
    <div className="video-wrapper">
      {title && (
        <div className="mb-2 text-lg font-semibold text-gray-800">{title}</div>
      )}
      {video.playback_id && videoToken ? (
      <MuxPlayer
        ref={playerRef as React.RefObject<MuxPlayerElement>}
        playbackId={video.playback_id}
        tokens={{
          playback: videoToken
        }}
        metadata={{
          video_title: title || video.title,
        }}
        streamType="on-demand"
        playbackRates={completed ? [0.5, 1, 1.5, 2] : [1]}
        autoPlay={false}
        muted={false}
        onTimeUpdate={handleTimeUpdate}
        style={{
          width: "100%",
          aspectRatio: "16/9",
        }}
      />
      ) : (
        <div>Loading...</div>
      )}
      {progress > 0 && (
        <div className="h-1 w-full bg-gray-200 mt-2">
          <div
            className="h-full bg-blue-500 transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
};

export default MuxVideo;