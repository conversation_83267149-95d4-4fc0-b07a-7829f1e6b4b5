'use client'

import { useState, useEffect } from 'react'
import { ExternalLink, Save, HelpCircle, X } from 'lucide-react'
import { getFeedbackSettings, updateFeedbackSettings, AdminFeedbackSettings as SettingsType } from '@/data-access/feedback/admin'

export default function AdminFeedbackSettings() {
  const [settings, setSettings] = useState<SettingsType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showInstructions, setShowInstructions] = useState(false)

  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const result = await getFeedbackSettings()
        
        if (result.success && result.data) {
          setSettings(result.data)
        } else {
          setError(result.error || 'Failed to load settings')
        }
      } catch (err) {
        setError('An unexpected error occurred')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadSettings()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!settings) return
    
    setIsSaving(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await updateFeedbackSettings(settings)
      
      if (result.success) {
        setSuccess('Settings updated successfully')
        setTimeout(() => {
          setSuccess(null)
        }, 3000)
      } else {
        setError(result.error || 'Failed to update settings')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
        <div className="h-7 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="h-10 bg-gray-200 rounded w-full mb-4"></div>
        <div className="h-10 bg-gray-200 rounded w-1/3"></div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <p className="text-red-600">
          {error || 'Unable to load settings'}
        </p>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Main Settings Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Feedback Settings</h2>
          <button
            type="button"
            onClick={() => setShowInstructions(true)}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-full hover:bg-indigo-100 focus:outline-none"
          >
            <HelpCircle className="h-4 w-4 mr-1.5" />
            Setup Guide
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md text-green-700 text-sm flex items-center justify-between">
            <span>{success}</span>
            <button onClick={() => setSuccess(null)} className="text-green-500 hover:text-green-700">
              <X className="h-4 w-4" />
            </button>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="max-w-lg">
            <div className="mb-1 flex items-center justify-between">
              <label htmlFor="calLink" className="block text-sm font-medium text-gray-700">
                Cal.com Link
              </label>
              <a 
                href="https://app.cal.com/event-types" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-xs text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                Open Cal.com
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
            <div className="relative rounded-md shadow-sm">
              <input
                type="text"
                id="calLink"
                value={settings.calLink}
                onChange={(e) => setSettings({ ...settings, calLink: e.target.value })}
                className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-12 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="username/event-name"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                /
              </div>
            </div>
            <p className="mt-1.5 text-xs text-gray-500">
              Format: username/event-slug (e.g., dhwani/feedback-session)
            </p>
          </div>
          
          <div className="pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <div className="text-sm text-yellow-700 flex items-center">
                <span className="inline-block w-2 h-2 bg-yellow-400 rounded-full mr-2"></span>
                Remember to set a 5-day minimum notice period in Cal.com
              </div>
              <button
                type="submit"
                disabled={isSaving}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-1.5" />
                    Save
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Instructions Popup */}
      {showInstructions && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 overflow-hidden">
            <div className="flex items-center justify-between bg-indigo-50 px-4 py-3 border-b border-indigo-100">
              <h3 className="text-lg font-medium text-indigo-900 flex items-center">
                <HelpCircle className="h-5 w-5 mr-2 text-indigo-600" />
                Cal.com Setup Guide
              </h3>
              <button 
                onClick={() => setShowInstructions(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-5 max-h-[70vh] overflow-y-auto">
              <ol className="list-decimal pl-5 text-sm text-gray-700 space-y-4">
                <li>
                  <strong className="text-gray-900">Create a Cal.com account</strong>
                  <p className="mt-1 text-gray-600">
                    Sign up at <a href="https://cal.com" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800">cal.com</a> if you don't already have an account
                  </p>
                </li>
                <li>
                  <strong className="text-gray-900">Create a new event type</strong>
                  <ul className="list-disc pl-5 mt-1 space-y-1 text-gray-600">
                    <li>Go to "Event Types" in your Cal.com dashboard</li>
                    <li>Click "New Event Type"</li>
                    <li>Name it "Feedback Session"</li>
                    <li>Set duration to 15-30 minutes</li>
                  </ul>
                </li>
                <li>
                  <strong className="text-gray-900">Configure the event settings</strong>
                  <ul className="list-disc pl-5 mt-1 space-y-1 text-gray-600">
                    <li className="font-medium text-yellow-700">
                      Set minimum notice period to <span className="font-bold">5 days</span>
                    </li>
                    <li>Enable collection of attendee name and email</li>
                    <li>Add a field for assignment ID (optional)</li>
                    <li>Set appropriate availability and buffer times</li>
                  </ul>
                </li>
                <li>
                  <strong className="text-gray-900">Copy the event link</strong>
                  <ul className="list-disc pl-5 mt-1 text-gray-600">
                    <li>Format should be: username/event-slug</li>
                    <li>Paste it in the field in the settings</li>
                  </ul>
                </li>
                <li>
                  <strong className="text-gray-900">Test the integration</strong>
                  <p className="mt-1 text-gray-600">
                    Schedule a test session from the student dashboard to verify everything works correctly
                  </p>
                </li>
              </ol>
              
              <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-3">
                <p className="text-sm text-yellow-800 font-medium">Important Note</p>
                <p className="text-sm text-yellow-700 mt-1">
                  The 5-day minimum notice period is essential to ensure instructors have enough time to prepare for feedback sessions.
                </p>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-3 flex justify-end">
              <button
                type="button"
                onClick={() => setShowInstructions(false)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 