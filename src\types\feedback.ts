import { SessionStatus } from '@prisma/client'

export type FeedbackSessionWithDetails = {
  id: string
  assignmentId?: string | null
  assignment?: {
    id: string
    title: string
    moduleId: string
  } | null
  course: {
    id: string
    name: string
  }
  scheduledAt: Date | null
  status: SessionStatus
  calEventId?: string | null
  notes?: string | null
  user?: {
    id: string
    name: string
    email: string
  }
}

export type { SessionStatus } 