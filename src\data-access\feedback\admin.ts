'use server'

import { prisma } from '@/utils/prisma/prisma'
import { ActionResult, handleError } from '../helpers'
import { SessionStatus } from '@prisma/client'
import { FeedbackSessionWithDetails } from '@/types/feedback'

export type AdminFeedbackStats = {
  total: number
  scheduled: number
  completed: number
  cancelled: number
  noShow: number
}

export type AdminFeedbackSettings = {
  calLink: string
}

export async function getAllFeedbackSessions(): Promise<ActionResult<FeedbackSessionWithDetails[]>> {
  try {
    const sessions = await prisma.feedbackSession.findMany({
      include: {
        course: true,
        assignment: true,
        user: true
      },
      orderBy: { scheduledAt: 'desc' }
    })

    const formattedSessions = sessions.map(session => ({
      id: session.id,
      assignmentId: session.assignmentId,
      assignment: session.assignment ? {
        id: session.assignment.id,
        title: session.assignment.title,
        moduleId: session.assignment.moduleId
      } : null,
      course: {
        id: session.course.id,
        name: session.course.name
      },
      scheduledAt: session.scheduledAt,
      status: session.status,
      calEventId: session.calEventId,
      notes: session.notes,
      user: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email
      }
    }))

    return { success: true, data: formattedSessions }
  } catch (error) {
    return handleError(error, 'fetch all feedback sessions')
  }
}

export async function getFeedbackStats(): Promise<ActionResult<AdminFeedbackStats>> {
  try {
    const [total, scheduled, completed, cancelled, noShow] = await Promise.all([
      prisma.feedbackSession.count(),
      prisma.feedbackSession.count({ where: { status: SessionStatus.SCHEDULED } }),
      prisma.feedbackSession.count({ where: { status: SessionStatus.COMPLETED } }),
      prisma.feedbackSession.count({ where: { status: SessionStatus.CANCELLED } }),
      prisma.feedbackSession.count({ where: { status: SessionStatus.NO_SHOW } })
    ])

    return {
      success: true,
      data: {
        total,
        scheduled,
        completed,
        cancelled,
        noShow
      }
    }
  } catch (error) {
    return handleError(error, 'fetch feedback stats')
  }
}

export async function getFeedbackSettings(): Promise<ActionResult<AdminFeedbackSettings>> {
  try {
    // Get settings from the database or create default if not exists
    let settings = await prisma.feedbackSettings.findUnique({
      where: { id: 'feedback_settings' }
    })
    
    if (!settings) {
      settings = await prisma.feedbackSettings.create({
        data: {
          id: 'feedback_settings',
          calLink: 'dhwani/feedback-session'
        }
      })
    }

    return {
      success: true,
      data: {
        calLink: settings.calLink
      }
    }
  } catch (error) {
    return handleError(error, 'fetch feedback settings')
  }
}

export async function updateFeedbackSettings(settings: AdminFeedbackSettings): Promise<ActionResult<void>> {
  try {
    await prisma.feedbackSettings.upsert({
      where: { id: 'feedback_settings' },
      update: {
        calLink: settings.calLink
      },
      create: {
        id: 'feedback_settings',
        calLink: settings.calLink
      }
    })

    return { success: true }
  } catch (error) {
    return handleError(error, 'update feedback settings')
  }
}

export async function updateSessionStatus(sessionId: string, status: SessionStatus): Promise<ActionResult<void>> {
  try {
    await prisma.feedbackSession.update({
      where: { id: sessionId },
      data: { status }
    })

    return { success: true }
  } catch (error) {
    return handleError(error, 'update session status')
  }
}

export async function addSessionNotes(sessionId: string, notes: string): Promise<ActionResult<void>> {
  try {
    await prisma.feedbackSession.update({
      where: { id: sessionId },
      data: { notes }
    })

    return { success: true }
  } catch (error) {
    return handleError(error, 'add session notes')
  }
} 