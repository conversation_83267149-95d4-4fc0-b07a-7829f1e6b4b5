/*
  Warnings:

  - You are about to drop the column `content` on the `AssignmentSubmission` table. All the data in the column will be lost.
  - You are about to drop the column `score` on the `AssignmentSubmission` table. All the data in the column will be lost.
  - You are about to drop the column `pdfUrl` on the `Certificate` table. All the data in the column will be lost.
  - You are about to alter the column `price` on the `Course` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Decimal(10,2)`.
  - You are about to alter the column `amount` on the `Payment` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Decimal(10,2)`.
  - You are about to drop the `CourseProgress` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[userId,courseId]` on the table `Application` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `pdfPath` to the `Certificate` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `duration` on the `Music` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `duration` on the `UserRecording` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "CourseProgress" DROP CONSTRAINT "CourseProgress_courseId_fkey";

-- DropForeignKey
ALTER TABLE "CourseProgress" DROP CONSTRAINT "CourseProgress_userId_fkey";

-- DropForeignKey
ALTER TABLE "UserRecording" DROP CONSTRAINT "UserRecording_musicTitle_fkey";

-- DropForeignKey
ALTER TABLE "UserRecording" DROP CONSTRAINT "UserRecording_userId_fkey";

-- DropIndex
DROP INDEX "Application_auditionStatus_idx";

-- DropIndex
DROP INDEX "Application_courseId_idx";

-- DropIndex
DROP INDEX "Application_userId_idx";

-- DropIndex
DROP INDEX "Assignment_deadline_idx";

-- DropIndex
DROP INDEX "Assignment_moduleId_idx";

-- DropIndex
DROP INDEX "AssignmentSubmission_assignmentId_idx";

-- DropIndex
DROP INDEX "AssignmentSubmission_userId_idx";

-- DropIndex
DROP INDEX "Course_isPublished_idx";

-- DropIndex
DROP INDEX "Course_level_idx";

-- DropIndex
DROP INDEX "Course_name_idx";

-- DropIndex
DROP INDEX "FeedbackSession_courseId_idx";

-- DropIndex
DROP INDEX "FeedbackSession_scheduledAt_idx";

-- DropIndex
DROP INDEX "FeedbackSession_userId_idx";

-- DropIndex
DROP INDEX "Music_title_idx";

-- DropIndex
DROP INDEX "Payment_status_idx";

-- DropIndex
DROP INDEX "Payment_userId_idx";

-- DropIndex
DROP INDEX "User_isActive_idx";

-- DropIndex
DROP INDEX "User_role_idx";

-- DropIndex
DROP INDEX "UserRecording_musicTitle_idx";

-- DropIndex
DROP INDEX "UserRecording_userId_idx";

-- AlterTable
ALTER TABLE "AssignmentSubmission" DROP COLUMN "content",
DROP COLUMN "score";

-- AlterTable
ALTER TABLE "Certificate" DROP COLUMN "pdfUrl",
ADD COLUMN     "pdfPath" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Course" ALTER COLUMN "price" SET DATA TYPE DECIMAL(10,2);

-- AlterTable
ALTER TABLE "Music" DROP COLUMN "duration",
ADD COLUMN     "duration" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "Payment" ALTER COLUMN "amount" SET DATA TYPE DECIMAL(10,2);

-- AlterTable
ALTER TABLE "UserRecording" DROP COLUMN "duration",
ADD COLUMN     "duration" INTEGER NOT NULL;

-- DropTable
DROP TABLE "CourseProgress";

-- CreateTable
CREATE TABLE "_CompletedModules" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_CompletedModules_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_CompletedLessons" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_CompletedLessons_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_CompletedModules_B_index" ON "_CompletedModules"("B");

-- CreateIndex
CREATE INDEX "_CompletedLessons_B_index" ON "_CompletedLessons"("B");

-- CreateIndex
CREATE INDEX "Application_auditionStatus_paymentStatus_idx" ON "Application"("auditionStatus", "paymentStatus");

-- CreateIndex
CREATE UNIQUE INDEX "Application_userId_courseId_key" ON "Application"("userId", "courseId");

-- CreateIndex
CREATE INDEX "Assignment_moduleId_deadline_idx" ON "Assignment"("moduleId", "deadline");

-- CreateIndex
CREATE INDEX "AssignmentSubmission_userId_status_idx" ON "AssignmentSubmission"("userId", "status");

-- CreateIndex
CREATE INDEX "Course_isPublished_level_idx" ON "Course"("isPublished", "level");

-- CreateIndex
CREATE INDEX "FeedbackSession_userId_scheduledAt_idx" ON "FeedbackSession"("userId", "scheduledAt");

-- CreateIndex
CREATE INDEX "FeedbackSession_courseId_status_idx" ON "FeedbackSession"("courseId", "status");

-- CreateIndex
CREATE INDEX "Music_genre_difficulty_idx" ON "Music"("genre", "difficulty");

-- CreateIndex
CREATE INDEX "Payment_userId_status_idx" ON "Payment"("userId", "status");

-- CreateIndex
CREATE INDEX "Payment_createdAt_idx" ON "Payment"("createdAt");

-- CreateIndex
CREATE INDEX "User_role_isActive_idx" ON "User"("role", "isActive");

-- CreateIndex
CREATE INDEX "UserRecording_userId_musicTitle_idx" ON "UserRecording"("userId", "musicTitle");

-- AddForeignKey
ALTER TABLE "UserRecording" ADD CONSTRAINT "UserRecording_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRecording" ADD CONSTRAINT "UserRecording_musicTitle_fkey" FOREIGN KEY ("musicTitle") REFERENCES "Music"("title") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompletedModules" ADD CONSTRAINT "_CompletedModules_A_fkey" FOREIGN KEY ("A") REFERENCES "Module"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompletedModules" ADD CONSTRAINT "_CompletedModules_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompletedLessons" ADD CONSTRAINT "_CompletedLessons_A_fkey" FOREIGN KEY ("A") REFERENCES "Lesson"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompletedLessons" ADD CONSTRAINT "_CompletedLessons_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
