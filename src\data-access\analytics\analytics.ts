"use server"

import { prisma } from "@/utils/prisma/prisma"
import { 
  CourseAnalytics, 
  UserAnalytics, 
  AnalyticsStats, 
  CourseDetailAnalytics,
  UserAssignmentAnalytics,
  AssignmentSubmissionDetail
} from "@/types/analytics"
import { ActionResult, handleError, requireAdmin } from "../helpers"
import { SubmissionStatus, SessionStatus } from "@prisma/client"
import { revalidatePath } from "next/cache"

export async function getAnalyticsOverview(): Promise<ActionResult<AnalyticsStats>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }

  try {
    const [
      totalCourses,
      totalEnrollments,
      totalActiveUsers,
      totalRevenue
    ] = await Promise.all([
      prisma.course.count({ where: { isPublished: true } }),
      prisma.enrollment.count(),
      prisma.user.count({ where: { isActive: true, role: 'STUDENT' } }),
      prisma.payment.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { amount: true }
      })
    ])

    return {
      success: true,
      data: {
        totalCourses,
        totalEnrollments,
        totalActiveUsers,
        totalRevenue: Number(totalRevenue._sum.amount || 0),
        averageCompletionRate: 0,
        monthlyGrowth: 0
      }
    }
  } catch (error) {
    return handleError<AnalyticsStats>(error, 'fetch analytics overview')
  }
}

export async function getAllCoursesAnalytics(): Promise<ActionResult<CourseAnalytics[]>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }

  try {
    const [courses, payments] = await Promise.all([
      prisma.course.findMany({
        include: {
          enrollments: {
            include: {
              user: {
                include: {
                  completedLessons: true,
                  completedModules: true
                }
              }
            }
          },
          modules: {
            include: {
              lessons: true,
              assignments: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.payment.findMany({
        where: { 
          status: 'COMPLETED',
          courseId: { not: null }
        }
      })
    ])

    const analytics: CourseAnalytics[] = courses.map(course => {
      const totalModules = course.modules.length
      const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
      const totalAssignments = course.modules.reduce((acc, module) => acc + module.assignments.length, 0)
      
      const enrollmentCount = course.enrollments.length
      const activeUsers = course.enrollments.filter(e => e.user.isActive).length
      
      const coursePayments = payments.filter(p => p.courseId === course.id)
      const revenueGenerated = coursePayments.reduce(
        (acc, payment) => acc + Number(payment.amount), 0
      )

      return {
        id: course.id,
        name: course.name,
        description: course.description,
        level: course.level,
        price: course.price,
        isPublished: course.isPublished,
        enrollmentCount,
        completionRate: 0,
        averageProgress: 0,
        activeUsers,
        totalModules,
        totalLessons,
        totalAssignments,
        revenueGenerated,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt
      }
    })

    return { success: true, data: analytics }
  } catch (error) {
    return handleError<CourseAnalytics[]>(error, 'fetch courses analytics')
  }
}

export async function getCourseDetailAnalytics(courseId: string): Promise<ActionResult<CourseDetailAnalytics>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }

  try {
    const [course, payments] = await Promise.all([
      prisma.course.findUnique({
        where: { id: courseId },
        include: {
          enrollments: {
            include: {
              user: {
                include: {
                  completedLessons: true,
                  completedModules: true,
                  assignments: {
                    include: {
                      assignment: {
                        include: {
                          module: true
                        }
                      }
                    }
                  },
                  feedbackSessions: {
                    where: { courseId }
                  }
                }
              }
            }
          },
          modules: {
            include: {
              lessons: true,
              assignments: true
            },
            orderBy: { order: 'asc' }
          }
        }
      }),
      prisma.payment.findMany({
        where: { 
          courseId,
          status: 'COMPLETED'
        }
      })
    ])

    if (!course) {
      return { success: false, error: 'Course not found' }
    }

    const totalModules = course.modules.length
    const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
    const totalAssignments = course.modules.reduce((acc, module) => acc + module.assignments.length, 0)
    
    const enrollmentCount = course.enrollments.length
    const activeUsers = course.enrollments.filter(e => e.user.isActive).length
    
    const revenueGenerated = payments.reduce(
      (acc, payment) => acc + Number(payment.amount), 0
    )

    const courseAnalytics: CourseAnalytics = {
      id: course.id,
      name: course.name,
      description: course.description,
      level: course.level,
      price: course.price,
      isPublished: course.isPublished,
      enrollmentCount,
      completionRate: 0,
      averageProgress: 0,
      activeUsers,
      totalModules,
      totalLessons,
      totalAssignments,
      revenueGenerated,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    }

    const users: UserAnalytics[] = course.enrollments.map(enrollment => {
      const user = enrollment.user
      const courseLessons = course.modules.flatMap(m => m.lessons)
      const courseModules = course.modules
      const courseAssignments = course.modules.flatMap(m => m.assignments)
      
      const completedLessons = user.completedLessons.filter(
        lesson => courseLessons.some(cl => cl.id === lesson.id)
      ).length
      
      const completedModules = user.completedModules.filter(
        module => courseModules.some(cm => cm.id === module.id)
      ).length
      
      const submittedAssignments = user.assignments.filter(
        submission => courseAssignments.some(ca => ca.id === submission.assignmentId)
      ).length
      
      const overdueAssignments = user.assignments.filter(
        submission => {
          const assignment = courseAssignments.find(ca => ca.id === submission.assignmentId)
          return assignment && submission.status === SubmissionStatus.OVERDUE
        }
      ).length
      
      const progressPercentage = courseLessons.length > 0 
        ? (completedLessons / courseLessons.length) * 100 
        : 0
      
      const feedbackSessions = user.feedbackSessions.filter(fs => fs.courseId === courseId)
      const completedFeedbackSessions = feedbackSessions.filter(
        fs => fs.status === SessionStatus.COMPLETED
      ).length
      const scheduledFeedbackSessions = feedbackSessions.filter(
        fs => fs.status === SessionStatus.SCHEDULED
      ).length

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        enrolledAt: enrollment.enrolledAt,
        progressPercentage: Math.round(progressPercentage * 100) / 100,
        completedModules,
        totalModules: courseModules.length,
        completedLessons,
        totalLessons: courseLessons.length,
        submittedAssignments,
        totalAssignments: courseAssignments.length,
        overdueCount: overdueAssignments,
        lastActivity: null,
        isActive: user.isActive,
        feedbackSessions: {
          total: feedbackSessions.length,
          completed: completedFeedbackSessions,
          scheduled: scheduledFeedbackSessions
        }
      }
    })

    const moduleStats = course.modules.map(module => ({
      id: module.id,
      title: module.title,
      order: module.order,
      completionRate: 0,
      averageTimeSpent: 0,
      lessonCount: module.lessons.length,
      assignmentCount: module.assignments.length
    }))

    const recentActivity: CourseDetailAnalytics['recentActivity'] = course.enrollments
      .sort((a, b) => new Date(b.enrolledAt).getTime() - new Date(a.enrolledAt).getTime())
      .slice(0, 10)
      .map(enrollment => ({
        type: 'enrollment' as const,
        userId: enrollment.user.id,
        userName: enrollment.user.name,
        description: `Enrolled in ${course.name}`,
        timestamp: enrollment.enrolledAt
      }))

    return {
      success: true,
      data: {
        course: courseAnalytics,
        users,
        moduleStats,
        recentActivity
      }
    }
  } catch (error) {
    return handleError<CourseDetailAnalytics>(error, 'fetch course detail analytics')
  }
}

export async function getUserAssignmentAnalytics(
  courseId: string, 
  userId: string
): Promise<ActionResult<UserAssignmentAnalytics>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }

  try {
    // Verify user is enrolled in the course
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        userId_courseId: {
          userId,
          courseId
        }
      },
      include: {
        user: true,
        course: true
      }
    })

    if (!enrollment) {
      return { success: false, error: 'User not enrolled in this course' }
    }

    // Get all assignment submissions for this user in this course
    const submissions = await prisma.assignmentSubmission.findMany({
      where: {
        userId,
        assignment: {
          module: {
            courseId
          }
        }
      },
      include: {
        assignment: {
          include: {
            module: {
              select: {
                id: true,
                title: true,
                order: true
              }
            }
          }
        }
      },
      orderBy: [
        { assignment: { module: { order: 'asc' } } },
        { assignment: { deadline: 'asc' } }
      ]
    })

    const courseAssignments = await prisma.assignment.findMany({
      where: {
        module: {
          courseId
        },
        NOT: {
          submissions: {
            some: {
              userId
            }
          }
        }
      },
      include: {
        module: {
          select: {
            id: true,
            title: true,
            order: true
          }
        }
      },
      orderBy: [
        { module: { order: 'asc' } },
        { deadline: 'asc' }
      ]
    })

    const submissionDetails: AssignmentSubmissionDetail[] = [
      ...submissions.map(submission => ({
        id: submission.id,
        assignmentId: submission.assignmentId,
        assignmentTitle: submission.assignment.title,
        assignmentDescription: submission.assignment.description,
        assignmentDeadline: submission.assignment.deadline,
        assignmentPath: submission.assignment.assignmentPath,
        moduleTitle: submission.assignment.module.title,
        moduleOrder: submission.assignment.module.order,
        status: submission.status,
        feedback: submission.feedback,
        submittedAt: submission.submittedAt,
        updatedAt: submission.updatedAt,
        submissionPath: submission.assignmentPath,
        hasSubmission: true,
        isOverdue: submission.status === SubmissionStatus.OVERDUE ||
          (new Date() > submission.assignment.deadline && submission.status === SubmissionStatus.SUBMITTED)
      })),
      ...courseAssignments.map(assignment => ({
        id: null,
        assignmentId: assignment.id,
        assignmentTitle: assignment.title,
        assignmentDescription: assignment.description,
        assignmentDeadline: assignment.deadline,
        assignmentPath: assignment.assignmentPath,
        moduleTitle: assignment.module.title,
        moduleOrder: assignment.module.order,
        status: new Date() > assignment.deadline ? SubmissionStatus.OVERDUE : null,
        feedback: null,
        submittedAt: null,
        updatedAt: null,
        submissionPath: null,
        hasSubmission: false,
        isOverdue: new Date() > assignment.deadline
      }))
    ].sort((a, b) => {
      // Sort by module order first, then by deadline
      if (a.moduleOrder !== b.moduleOrder) {
        return a.moduleOrder - b.moduleOrder
      }
      return new Date(a.assignmentDeadline).getTime() - new Date(b.assignmentDeadline).getTime()
    })

    const stats = {
      totalAssignments: submissionDetails.length,
      submitted: submissionDetails.filter(s => s.hasSubmission).length,
      graded: submissionDetails.filter(s => s.status === SubmissionStatus.GRADED).length,
      needsRevision: submissionDetails.filter(s => s.status === SubmissionStatus.NEEDS_REVISION).length,
      overdue: submissionDetails.filter(s => s.isOverdue).length,
      pending: submissionDetails.filter(s => !s.hasSubmission && !s.isOverdue).length
    }

    return {
      success: true,
      data: {
        user: {
          id: enrollment.user.id,
          name: enrollment.user.name,
          email: enrollment.user.email
        },
        course: {
          id: enrollment.course.id,
          name: enrollment.course.name
        },
        enrolledAt: enrollment.enrolledAt,
        submissions: submissionDetails,
        stats
      }
    }
  } catch (error) {
    return handleError<UserAssignmentAnalytics>(error, 'fetch user assignment analytics')
  }
}

export async function updateAssignmentSubmissionStatus(
  submissionId: string,
  status: SubmissionStatus,
  feedback?: string
): Promise<ActionResult<boolean>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }

  try {
    const submission = await prisma.assignmentSubmission.update({
      where: { id: submissionId },
      data: {
        status,
        feedback: feedback || null,
        updatedAt: new Date()
      },
      include: {
        assignment: {
          include: {
            module: {
              select: { courseId: true }
            }
          }
        }
      }
    })

    // Revalidate relevant pages
    revalidatePath(`/admin/analytics/${submission.assignment.module.courseId}`)
    revalidatePath(`/admin/analytics/${submission.assignment.module.courseId}/${submission.userId}`)

    return { success: true, data: true }
  } catch (error) {
    return handleError<boolean>(error, 'update assignment submission status')
  }
} 