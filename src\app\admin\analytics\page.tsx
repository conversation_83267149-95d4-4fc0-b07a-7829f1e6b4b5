import Link from 'next/link'
import { Bar<PERSON>hart3, <PERSON>rendingUp, Users, DollarSign, BookOpen, ArrowRight } from 'lucide-react'
import { getAnalyticsOverview, getAllCoursesAnalytics } from '@/data-access/analytics/analytics'
import { formatCurrency, formatDate } from '@/lib/utils'

export default async function AnalyticsPage() {
  const [overviewResult, coursesResult] = await Promise.all([
    getAnalyticsOverview(),
    getAllCoursesAnalytics()
  ])

  if (!overviewResult.success) {
    throw new Error(overviewResult.error || 'Failed to load analytics overview')
  }

  if (!coursesResult.success) {
    throw new Error(coursesResult.error || 'Failed to load courses analytics')
  }

  const overview = overviewResult.data!
  const courses = coursesResult.data!

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
              <p className="text-gray-600">Platform insights and course performance</p>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Courses</p>
                <p className="text-2xl font-bold text-gray-900">{overview.totalCourses}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <BookOpen className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Enrollments</p>
                <p className="text-2xl font-bold text-gray-900">{overview.totalEnrollments}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">{overview.totalActiveUsers}</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(overview.totalRevenue)}</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Courses Analytics */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Course Performance</h2>
            <p className="text-gray-600 mt-1">Detailed analytics for each course</p>
          </div>

          <div className="p-6">
            {courses.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No courses found</h3>
                <p className="text-gray-600 mb-6">Create your first course to see analytics</p>
                <Link
                  href="/admin/courses/new"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  <BookOpen className="h-4 w-4" />
                  Create Course
                </Link>
              </div>
            ) : (
              <div className="grid gap-6">
                {courses.map((course) => (
                  <Link
                    key={course.id}
                    href={`/admin/analytics/${course.id}`}
                    className="block p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200 group"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
                            {course.name}
                          </h3>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              course.isPublished 
                                ? 'bg-green-100 text-green-700' 
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {course.isPublished ? 'Published' : 'Draft'}
                            </span>
                            <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-full">
                              {course.level}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-4 line-clamp-2">{course.description}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Enrollments</p>
                            <p className="font-semibold text-gray-900">{course.enrollmentCount}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Active Users</p>
                            <p className="font-semibold text-gray-900">{course.activeUsers}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Modules</p>
                            <p className="font-semibold text-gray-900">{course.totalModules}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Lessons</p>
                            <p className="font-semibold text-gray-900">{course.totalLessons}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Assignments</p>
                            <p className="font-semibold text-gray-900">{course.totalAssignments}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Revenue</p>
                            <p className="font-semibold text-gray-900">{formatCurrency(course.revenueGenerated)}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="ml-4 flex items-center text-gray-400 group-hover:text-indigo-600 transition-colors">
                        <ArrowRight className="h-5 w-5" />
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 