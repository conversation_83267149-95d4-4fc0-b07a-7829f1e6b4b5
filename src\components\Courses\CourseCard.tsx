import Image from 'next/image';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Clock, ArrowRight, Layers } from 'lucide-react';
import { CourseStat } from '@/types/course';
import { Suspense } from 'react';
import { getThumbnailUrl } from '@/data-access/course/storage';

export default function CourseCard({ course }: { course: CourseStat }) {
  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'bg-emerald-500 text-emerald-50';
      case 'intermediate':
        return 'bg-amber-500 text-amber-50';
      case 'advanced':
        return 'bg-red-500 text-red-50';
      default:
        return 'bg-slate-600 text-slate-50';
    }
  };

  return (
    <div className="group bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 ease-in-out hover:shadow-xl hover:-translate-y-1.5 border border-slate-200 hover:border-slate-300">
      <div className="relative w-full h-48 overflow-hidden">
        <Suspense fallback={<ThumbnailSkeleton />}>
          <CourseThumbnail courseId={course.id} courseName={course.name} courseThumbnailPath={course.thumbnailPath} />
        </Suspense>
        <div className="absolute inset-0 bg-gradient-to-t from-black/25 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out" />

        <div
          className={`absolute top-3.5 right-3.5 ${getLevelColor(
            course.level
          )} text-xs font-medium px-3 py-1 rounded-full shadow-md backdrop-blur-sm`}
        >
          {course.level}
        </div>

        <div className="absolute top-3.5 left-3.5 bg-white/90 backdrop-blur-sm text-slate-700 text-xs font-medium px-3 py-1 rounded-full shadow-md flex items-center gap-1.5">
          <Layers className="w-3.5 h-3.5 text-blue-500" />
          {course._count.modules} Modules
        </div>
      </div>

      <div className="p-5 flex flex-col">
        <h3 className="text-lg font-semibold text-slate-800 mb-1.5 leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
          {course.name}
        </h3>

        <p className="text-slate-600 text-sm mb-4 line-clamp-3 leading-relaxed">
          {course.description}
        </p>

        <div className="flex items-center text-sm text-slate-500 mb-5 space-x-5">
          <span className="flex items-center gap-1.5">
            <BookOpen className="w-4 h-4 text-blue-500" />
            {course._count.modules} Modules
          </span>
          <span className="flex items-center gap-1.5">
            <Clock className="w-4 h-4 text-blue-500" />
            {new Date(course.updatedAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
          </span>
        </div>

        <div className="mt-auto pt-4 border-t border-slate-100">
          <Link
            href={`/courses/${course.id}`}
            className="group/btn w-full inline-flex items-center justify-center px-5 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-md hover:shadow-lg"
          >
            <span>View Course</span>
            <ArrowRight className="w-4 h-4 ml-2 transform transition-transform duration-300 ease-in-out group-hover/btn:translate-x-1" />
          </Link>
        </div>
      </div>
    </div>
  );
}

async function CourseThumbnail({ courseId, courseName, courseThumbnailPath }: { courseId: string, courseName: string, courseThumbnailPath: string }) {
  const { url } = await getThumbnailUrl(courseId, courseThumbnailPath)
  
  if (url) {
    return (
      <Image
        src={url}
        alt={courseName}
        fill
        style={{ objectFit: 'cover' }}
        className="object-cover transition-transform duration-200 ease-in-out group-hover:scale-105"
      />
    )
  }
  
  return (
    <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-indigo-100">
      <BookOpen className="w-12 h-12 text-blue-400" />
    </div>
  )
}

function ThumbnailSkeleton() {
  return (
    <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
      <BookOpen className="w-12 h-12 text-gray-400" />
    </div>
  )
}