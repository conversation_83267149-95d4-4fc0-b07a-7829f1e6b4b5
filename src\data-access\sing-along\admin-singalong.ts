'use server'

import { prisma } from "@/utils/prisma/prisma";
import { CourseLevel } from "@prisma/client";

export async function addSong(formData: FormData) {
    try {
        const newSong = await prisma.music.create({
            data: {
                title: formData.get('title') as string,
                genre: formData.get('genre') as string,
                duration: formData.get('duration') as string,
                difficulty: formData.get('difficulty') as CourseLevel,
            }
        })

        if (!newSong) {
            return { success: false, error: 'Failed to create song' }
        }

        return { success: true, error: null };
    }
    catch (error) {
        console.error(error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}