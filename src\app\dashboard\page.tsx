// app/page.tsx
import HeroSection from '@/components/Dashboard/Sections/HeroSection';
import ContinueLearningSection from '@/components/Dashboard/Sections/ContinueLearningSection';
import UpcomingEventsSection from '@/components/Dashboard/Sections/UpcomingEventSection';
import CourseStructureInfoSection from '@/components/Dashboard/Sections/CourseStructureInfoSection';
import { getUserEnrollments, getUpcomingDeadlines } from '@/data-access/dashboard/data';
import { createClient } from '@/utils/supabase/server';


export default async function HomePage() {

  const supabase = await createClient();
  const {data: {user}, error} = await supabase.auth.getUser();
  if (error || !user) {
    return <div>Error: {error?.message || 'User not found'}</div>;
  }
  
  // Fetch data in parallel
  const [enrollmentsResult, deadlinesResult] = await Promise.all([
    getUserEnrollments(user.email),
    getUpcomingDeadlines(user.email)
  ]);

  // Extract data safely
  const enrollments = enrollmentsResult.success ? enrollmentsResult.data : [];
  const deadlines = deadlinesResult.success ? deadlinesResult.data : [];

  return (
    <div className="min-h-screen w-full"> 
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
        <HeroSection />
        
        {/* Show error messages if data fetch failed */}
        {!enrollmentsResult.success && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700">Failed to load your courses. Please try again later.</p>
          </div>
        )}

        {!deadlinesResult.success && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700">Failed to load upcoming events. Please try again later.</p>
          </div>
        )}
        
        {/* Render sections with data */}
        {enrollments.length > 0 && (
          <ContinueLearningSection enrollments={enrollments} />
        )}
        
        {deadlines.length > 0 && (
          <UpcomingEventsSection deadlines={deadlines} />
        )}
        
        <CourseStructureInfoSection />

        {/* Footer */}
        <footer className="text-center py-10 mt-12 border-t border-slate-200">
            <p className="text-sm text-slate-500">&copy; {new Date().getFullYear()} Dhwanini Academy. All rights reserved.</p>
            <p className="text-xs text-slate-400 mt-1">Nurturing Classical Arts</p>
        </footer>
      </main>
    </div>
  );
}