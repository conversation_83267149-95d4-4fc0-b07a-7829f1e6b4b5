import { Suspense } from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, Edit, Trash2, MoreVertical, Clock, Users, FileText } from "lucide-react"
import { CourseLevel } from "@prisma/client"
import { CourseDetails } from "@/types/course"
import { getThumbnailUrl } from "@/data-access/course/storage"
import { DeleteCourseButton } from "./DeleteCourseButton"

interface CourseCardProps {
  course: CourseDetails
}

export function CourseCard({ course }: CourseCardProps) {
  const moduleCount = course.modules.length
  const lessonCount = course.modules.reduce((sum, module) => sum + module.lessons.length, 0)
  const assignmentCount = course.modules.reduce((sum, module) => sum + module.assignments.length, 0)

  const getLevelColor = (level: CourseLevel) => {
    switch (level) {
      case CourseLevel.BEGINNER:
        return "bg-green-100 text-green-700"
      case CourseLevel.INTERMEDIATE:
        return "bg-yellow-100 text-yellow-700"
      case CourseLevel.ADVANCED:
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const formatPrice = (price: number, currency: string) => {
    const symbol = currency === 'USD' ? '$' : currency === 'INR' ? '₹' : currency + ' '
    return `${symbol}${price}`
  }

  return (
    <div className="group bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-lg hover:border-gray-300 transition-all duration-200">
      {/* Course Thumbnail */}
      <div className="relative h-48 bg-gradient-to-br from-blue-50 to-indigo-100">
        <Suspense fallback={<ThumbnailSkeleton />}>
          <CourseThumbnail courseId={course.id} courseName={course.name} courseThumbnailPath={course.thumbnailPath} />
        </Suspense>
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
            {course.level}
          </span>
          {course.isPublished && (
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
              Published
            </span>
          )}
        </div>

        {/* Actions Menu */}
        <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
          <button className="p-2 bg-white/90 hover:bg-white rounded-lg shadow-sm transition-colors">
            <MoreVertical className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-blue-600 transition-colors">
            {course.name}
          </h3>
        </div>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
          {course.description}
        </p>
        
        {/* Course Stats */}
        <div className="flex items-center gap-4 mb-4 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <BookOpen className="w-3 h-3" />
            <span>{moduleCount} modules</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>{lessonCount} lessons</span>
          </div>
          <div className="flex items-center gap-1">
            <FileText className="w-3 h-3" />
            <span>{assignmentCount} tasks</span>
          </div>
        </div>

        {/* Price */}
        <div className="mb-6">
          <span className="text-2xl font-bold text-gray-900">
            {formatPrice(course.price, course.currency)}
          </span>
          <span className="text-sm text-gray-500 ml-1">{course.currency}</span>
          {course.auditionFee > 0 && (
            <div className="text-sm text-gray-600 mt-1">
              Audition Fee: {formatPrice(course.auditionFee, course.currency)}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Link
            href={`/admin/courses/${course.id}`}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-lg text-sm font-medium text-center transition-colors flex items-center justify-center gap-2"
          >
            <Edit className="w-4 h-4" />
            Edit Course
          </Link>
          <DeleteCourseButton courseId={course.id} courseName={course.name} />
        </div>
      </div>
    </div>
  )
}

async function CourseThumbnail({ courseId, courseName, courseThumbnailPath }: { courseId: string, courseName: string, courseThumbnailPath: string }) {
  const { url } = await getThumbnailUrl(courseId, courseThumbnailPath)
  
  if (url) {
    return (
      <img
        src={url}
        alt={courseName}
        className="w-full h-full object-cover"
      />
    )
  }
  
  return (
    <div className="flex items-center justify-center h-full">
      <BookOpen className="w-12 h-12 text-blue-400" />
    </div>
  )
}

function ThumbnailSkeleton() {
  return (
    <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
      <BookOpen className="w-12 h-12 text-gray-400" />
    </div>
  )
} 