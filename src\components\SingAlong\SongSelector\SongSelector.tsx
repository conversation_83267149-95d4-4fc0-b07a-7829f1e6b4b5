import { getAvailableSongs } from "@/data-access/sing-along/songs";
import { SongSelectorClient } from "@/components/SingAlong/SongSelector/SongSelectorClient";
import { Suspense } from "react";



export async function SongSelector() {
  const {songs, error} = await getAvailableSongs();
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  return (
    <Suspense fallback={<div>Loading...</div>}> 
      <SongSelectorClient songs={songs} />
    </Suspense>
  )
}