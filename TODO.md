# Todo List for LMS

## Video: (12-06) DONE
- ~~Store 1080 P content from the user in admin page~~
- ~~Retrieve and show the content to the user in lessons page~~


## Sing-along: (16-06) 

- ~~Add Favourite option for recordings and show it in the page~~
- ~~Add Support to record all 3 tracks in Recorder.tsx~~ (ONLY 2 TRACKS)
-  Add Shruthi-Box ( ask for music )
- ~~Add Support to Use it in Lesson player and Assignment~~
- ~~Record with Video if possible~~ (NOT POSSIBLE)

## Feedback Sessions (NOEL) (12-06)

    Add it in the Assignment place so that its inevitable
    

## ABAC Authorisation (Tharani) (16-06)

    Add Auth properly to all the routes 
    Ensure serverside and clientside auth
    De-Activate account based on the overdue count


## User Analytics (21-06)

    Show user assignment grader to the admin
    Provide the Instructor with all the User activities and performance
    Show dashboard with analytics

## Cron Jobs (21-06)

    Add cron jobs to add overdue count to the user.

## Support page for user support (21-06)

    Add support page for enquiry
    Add Inactivity issue solving

## CMS (23-06)

    Add sanity for Content management


## Subscription Model (23-06)

    Add subscription model using razorpay


## Optimisations -> NEXTJS (28-06)

    Use cache && Other Systems for better perfomance and UX

## UI Tweaks (Sanjay / Noel) (31-06)
