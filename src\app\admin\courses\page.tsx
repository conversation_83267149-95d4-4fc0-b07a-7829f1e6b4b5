import { Suspense } from "react"
import { getAllCoursesAdmin } from "@/data-access/course/admin-course"
import { CourseGrid } from "@/components/Admin/Course/CourseGrid"
import { CourseStats } from "@/components/Admin/Course/CourseStats"
import { PageHeader } from "@/components/Admin/Course/Misc/PageHeader"
import { ErrorBoundary } from "@/components/ui/ErrorBoundary"
import { LoadingSkeleton } from "@/components/ui/LoadingSkeleton"

export default async function CoursesPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        <PageHeader />
        
        <ErrorBoundary fallback={<div className="text-red-600">Failed to load courses</div>}>
          <Suspense fallback={<LoadingSkeleton />}>
            <CoursesContent />
          </Suspense>
        </ErrorBoundary>
      </div>
    </div>
  )
}

async function CoursesContent() {
  const result = await getAllCoursesAdmin()
  
  if (!result.success) {
    throw new Error(result.error || 'Failed to load courses')
  }
  
  const courses = result.data || []
  
  return (
    <>
      <CourseStats courses={courses} />
      <CourseGrid courses={courses} />
    </>
  )
}