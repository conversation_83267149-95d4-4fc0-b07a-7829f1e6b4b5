/*
  Warnings:

  - You are about to drop the column `paymentStatus` on the `Application` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "PaymentType" AS ENUM ('AUDITION', 'ENROLLMENT');

-- DropIndex
DROP INDEX "Application_auditionStatus_paymentStatus_idx";

-- AlterTable
ALTER TABLE "Application" DROP COLUMN "paymentStatus",
ADD COLUMN     "auditionPaymentStatus" "PaymentStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "enrollmentPaymentStatus" "PaymentStatus" NOT NULL DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "Payment" ADD COLUMN     "paymentType" "PaymentType" NOT NULL DEFAULT 'AUDITION';

-- CreateIndex
CREATE INDEX "Application_auditionStatus_idx" ON "Application"("auditionStatus");

-- CreateIndex
CREATE INDEX "Application_userId_courseId_idx" ON "Application"("userId", "courseId");
