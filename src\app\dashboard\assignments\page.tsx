import { Suspense } from 'react'
import { getUserAssignments, getAssignmentStats } from '@/data-access/assignment/assignment'
import { AssignmentDashboard } from '@/components/Dashboard/Assignments/AssignmentDashboard'
import { AssignmentLoadingSkeleton } from '@/components/Dashboard/Assignments/AssignmentLoadingSkeleton'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export default async function AssignmentsPage() {
  const supabase = await createClient();
  const {data: {user}, error} = await supabase.auth.getUser();
  if (error || !user) {
    return <div>Error: {error?.message || 'User not found'}</div>;
  }
  const [assignmentsResult, statsResult] = await Promise.all([
    getUserAssignments(user.email),
    getAssignmentStats(user.email)
  ])

  console.log(assignmentsResult, statsResult)

  if (!assignmentsResult.success) {
    if (assignmentsResult.error === 'User not authenticated') {
      redirect('/login')
    }
    throw new Error(assignmentsResult.error || 'Failed to load assignments')
  }

  if (!statsResult.success) {
    throw new Error(statsResult.error || 'Failed to load assignment stats')
  }

  const assignments = assignmentsResult.data || []
  const stats = statsResult.data!

  // Extract unique courses for filtering
  const courses = Array.from(
    new Map(
      assignments.map(a => [a.module.course.id, a.module.course])
    ).values()
  )

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Assignments</h1>
          <p className="text-gray-600 mt-2">
            Track your assignments, deadlines, and submissions
          </p>
        </div>

        <Suspense fallback={<AssignmentLoadingSkeleton />}>
          <AssignmentDashboard 
            initialAssignments={assignments}
            stats={stats}
            courses={courses}
          />
        </Suspense>
      </div>
    </div>
  )
}
