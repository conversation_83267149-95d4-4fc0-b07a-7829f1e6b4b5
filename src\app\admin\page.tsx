import Link from 'next/link';
import { Suspense } from 'react';
import { 
  Shield, 
  Mic, 
  BookOpen, 
  Users, 
  BarChart3, 
  Settings, 
  Plus,
  GraduationCap,
  MessageSquare
} from 'lucide-react';
import { getApplicationStats } from '@/data-access/auditions/auditions';

const adminSections = [
  {
    title: 'Auditions',
    icon: GraduationCap,
    href: '/admin/admissions',
    color: 'text-blue-600',
    bg: 'bg-blue-50',
    description: 'Manage student applications'
  },
  {
    title: 'Courses',
    icon: BookOpen,
    href: '/admin/courses',
    color: 'text-green-600',
    bg: 'bg-green-50',
    description: 'Create and manage courses'
  },
  {
    title: 'Feedback',
    icon: MessageSquare,
    href: '/admin/feedback',
    color: 'text-indigo-600',
    bg: 'bg-indigo-50',
    description: 'Manage feedback sessions'
  },
  {
    title: 'Sing Along',
    icon: Mic,
    href: '/admin/sing-along',
    color: 'text-purple-600',
    bg: 'bg-purple-50',
    description: 'Music library management'
  },
  {
    title: 'Users',
    icon: Users,
    href: '/admin/users',
    color: 'text-orange-600',
    bg: 'bg-orange-50',
    description: 'User management'
  },
  {
    title: 'Analytics',
    icon: BarChart3,
    href: '/admin/analytics',
    color: 'text-indigo-600',
    bg: 'bg-indigo-50',
    description: 'View insights'
  },
  {
    title: 'Settings',
    icon: Settings,
    href: '/admin/settings',
    color: 'text-gray-600',
    bg: 'bg-gray-50',
    description: 'Platform settings'
  }
];

export default async function AdminOverviewPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-2xl shadow-sm mb-6">
            <Shield className="h-8 w-8 text-gray-700" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 max-w-md mx-auto">
            Manage your music academy platform
          </p>
        </div>

        {/* Quick Stats */}
        <Suspense fallback={<QuickStatsLoading />}>
          <QuickStats />
        </Suspense>

        {/* Admin Sections */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-8 text-center">Platform Management</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {adminSections.map((section) => {
              const Icon = section.icon;
              return (
                <Link
                  key={section.href}
                  href={section.href}
                  className="group block p-6 rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-200"
                >
                  <div className="text-center">
                    <div className={`inline-flex items-center justify-center w-14 h-14 ${section.bg} rounded-xl mb-4 group-hover:scale-105 transition-transform duration-200`}>
                      <Icon className={`h-7 w-7 ${section.color}`} />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {section.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {section.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6 text-center">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
            <Link
              href="/admin/courses/new"
              className="group flex items-center gap-4 p-4 rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-center justify-center w-12 h-12 bg-green-50 rounded-xl">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
              <div className="flex-1">
                <span className="font-medium text-gray-900">Create New Course</span>
                <p className="text-sm text-gray-500">Add a new course to the platform</p>
              </div>
              <Plus className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
            </Link>
            
            <Link
              href="/admin/admissions"
              className="group flex items-center gap-4 p-4 rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl">
                <GraduationCap className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <span className="font-medium text-gray-900">Review Applications</span>
                <p className="text-sm text-gray-500">Manage student auditions</p>
              </div>
              <Plus className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

// Simple stats component
async function QuickStats() {
  const { success, data: stats } = await getApplicationStats();
  
  if (!success || !stats) {
    return null;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
        <p className="text-sm text-gray-600">Total Applications</p>
      </div>
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
        <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
        <p className="text-sm text-gray-600">Pending Review</p>
      </div>
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
        <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
        <p className="text-sm text-gray-600">Approved</p>
      </div>
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm text-center">
        <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
        <p className="text-sm text-gray-600">Rejected</p>
      </div>
    </div>
  );
}

function QuickStatsLoading() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
        </div>
      ))}
    </div>
  );
}