// app/components/cards/InProgressCourseCard.tsx
import Image from 'next/image';
import Link from 'next/link';
import { BookO<PERSON>, Layers, ListChecks, PlayCircle, SkipForward } from 'lucide-react';
import type { EnrollmentWithProgress } from '@/data-access/dashboard/data';

interface InProgressCourseCardProps {
  enrollment: EnrollmentWithProgress;
  thumbnail: string;
}

// Map course level enum to display string
const mapCourseLevel = (level: string) => {
  switch (level) {
    case 'BEGINNER': return 'Beginner';
    case 'INTERMEDIATE': return 'Intermediate';  
    case 'ADVANCED': return 'Advanced';
    default: return 'Beginner';
  }
};

export default function InProgressCourseCard({ enrollment, thumbnail }: InProgressCourseCardProps) {
  const course = enrollment.course;
  
  return (
    <div className="bg-white rounded-xl shadow-xl border border-blue-100 overflow-hidden p-6 transition-all duration-300 hover:shadow-2xl">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-36 h-36 md:h-auto bg-slate-100 rounded-lg relative flex-shrink-0 overflow-hidden">
          <Image
            src={thumbnail}
            alt={course.name}
            fill
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex-1">
          <span className={`inline-block bg-blue-100 text-blue-700 text-xs font-semibold px-2.5 py-1 rounded-full mb-2`}>
            {mapCourseLevel(course.level)}
          </span>
          <h3 className="text-2xl font-semibold text-slate-800 mb-1">{course.name}</h3>
          <p className="text-sm text-slate-500 mb-3">
            <span className="flex items-center">
              <ListChecks size={16} className="mr-1.5 text-blue-500" />
              {enrollment.completedLessons} of {course.lessonCount} lessons completed
            </span>
            <span className="flex items-center">
              <Layers size={16} className="mr-1.5 text-blue-500" />
              {enrollment.completedAssignments} of {course.assignmentCount} assignments completed
            </span>
          </p>
          <div className="w-full bg-slate-200 rounded-full h-2.5 mb-3 overflow-hidden">
            <div
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${enrollment.progressPercentage}%` }}>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
            <span className="text-sm font-medium text-slate-700 flex items-center">
              <SkipForward size={16} className="mr-1.5 text-blue-500" />
              Next: Continue learning
            </span>
            <Link
              href={`/courses/${course.id}`}
              className="group inline-flex items-center px-5 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <PlayCircle size={18} className="mr-2" />
              Resume Learning
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}