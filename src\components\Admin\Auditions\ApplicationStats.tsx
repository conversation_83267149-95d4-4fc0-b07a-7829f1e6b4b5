import { FileText, Clock, Eye, CheckCircle, XCircle } from "lucide-react";

interface ApplicationStatsProps {
  stats: {
    total: number;
    pending: number;
    underReview: number;
    approved: number;
    rejected: number;
    recentApplications: number;
  };
}

export function ApplicationStats({ stats }: ApplicationStatsProps) {
  const statsData = [
    {
      label: "Total Applications",
      value: stats.total,
      icon: FileText,
      bgColor: "bg-white",
      iconColor: "text-blue-600"
    },
    {
      label: "Pending Review",
      value: stats.pending,
      icon: Clock,
      bgColor: "bg-yellow-50",
      iconColor: "text-yellow-600"
    },
    {
      label: "Under Review",
      value: stats.underReview,
      icon: Eye,
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      label: "Approved",
      value: stats.approved,
      icon: CheckCircle,
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      label: "Rejected",
      value: stats.rejected,
      icon: XCircle,
      bgColor: "bg-red-50",
      iconColor: "text-red-600"
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
      {statsData.map((stat) => {
        const Icon = stat.icon;
        return (
          <div key={stat.label} className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <Icon className={`w-6 h-6 ${stat.iconColor}`} />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
} 