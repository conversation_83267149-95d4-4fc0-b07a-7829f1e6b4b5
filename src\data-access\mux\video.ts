"use server"

import { prisma } from '@/utils/prisma/prisma';
import Mux from '@mux/mux-node';
import { Video } from '@prisma/client';


const mux = new Mux({
    tokenId: process.env.MUX_TOKEN_ID!,
    tokenSecret: process.env.MUX_TOKEN_SECRET!
})

type UploadResponse = {
    success: boolean;
    id: string | null;
    url: string | null;
    error: string | null;
}

export const createDirectUpload = async (): Promise<UploadResponse> => {
    try {
        const directUpload = await mux.video.uploads.create({
            cors_origin: '*', // TODO: Change to the frontend url
            new_asset_settings: {
                playback_policy: ['signed'],
            },
        })
        console.log("Direct upload created:", directUpload)
       if(directUpload.error) {
        return {
            success: false,
            id: null,
            url: null,
            error: directUpload.error.message,
        }
       }

       return {
        success: true,
        id: directUpload.id,
        url: directUpload.url,
        error: null,
       }
    }
    catch (error) {
        return {
            success: false,
            id: null,
            url: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        }
    }
}


export const createVideo = async (lessonId: string, uploadId: string, title: string): Promise<{ success: boolean, error?: string, video?: Video }> => {
    try {
        const video = await prisma.video.upsert({
            where: { lessonId: lessonId },
            update: {
                upload_id: uploadId,
            },
            create: {
                lessonId: lessonId,
                upload_id: uploadId,
                title: title,
            }
        })

        if(!video) {
            return { success: false, error: 'Failed to create video' }
        }

        const updatedLesson = await prisma.lesson.update({
            where: { id: lessonId },
            data: {
                video: {
                    connect: {
                        id: video.id,
                    }
                }
            },
            include: {
                video: true
            }
        })

        if(!updatedLesson) {
            return { success: false, error: 'Failed to update lesson' }
        }

        return { success: true, error: null, video: video }
    }
    catch (error) {
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
}