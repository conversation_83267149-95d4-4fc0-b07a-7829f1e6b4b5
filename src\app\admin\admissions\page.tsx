import { Suspense } from "react";
import { getApplications, getApplicationStats } from "@/data-access/auditions/auditions";
import { AdminDashboard } from "@/components/Admin/Auditions/AdminDashboard";
import { ApplicationStats } from "@/components/Admin/Auditions/ApplicationStats";
import { ErrorBoundary } from "@/components/ui/ErrorBoundary";
import { ApplicationFilters } from "@/types/auditions";
import { Plus } from "lucide-react";
import Link from "next/link";

interface AdmissionsPageProps {
  searchParams: Promise<{
    status?: string;
    courseId?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
  }>;
}

// Page Header Component
function PageHeader() {
  return (
    <div className="flex justify-between items-start">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Audition Management</h1>
        <p className="text-gray-600 mt-2">Review and manage student audition submissions</p>
      </div>
      <Link
        href="/admin/courses"
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors text-sm font-medium shadow-sm"
      >
        <Plus className="w-4 h-4" />
        New Course
      </Link>
    </div>
  );
}

export default async function AdmissionsPage({ searchParams }: AdmissionsPageProps) {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        <PageHeader />
        
        {/* Stats Section */}
        <ErrorBoundary fallback={<div className="text-red-600">Failed to load statistics</div>}>
          <Suspense fallback={<StatsLoadingSkeleton />}>
            <StatsContent />
          </Suspense>
        </ErrorBoundary>

        {/* Applications Section */}
        <ErrorBoundary fallback={<div className="text-red-600">Failed to load applications</div>}>
          <Suspense fallback={<ApplicationsLoadingSkeleton />}>
            <ApplicationsContent searchParams={searchParams} />
          </Suspense>
        </ErrorBoundary>
      </div>
    </div>
  );
}

async function StatsContent() {
  const { success, data: stats, error } = await getApplicationStats();
  
  if (!success || error || !stats) {
    return <div className="text-red-600 bg-red-50 p-4 rounded-lg border border-red-200">{error || 'Failed to load statistics'}</div>
  }

  return <ApplicationStats stats={stats} />;
}

async function ApplicationsContent({ searchParams }: { searchParams: Promise<ApplicationFilters> }) {
  const resolvedParams = await searchParams;
  const { success, data: applications, error } = await getApplications(resolvedParams);
  
  if (!success || error) {
    throw new Error(error || 'Failed to load applications');
  }

  return <AdminDashboard initialApplications={applications} searchParams={resolvedParams} />;
}

function StatsLoadingSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm animate-pulse">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-24 mb-3"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

function ApplicationsLoadingSkeleton() {
  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
}