"use server"

import { getUserRole } from "../auth"
import { prisma } from "@/utils/prisma/prisma"
import { CourseDetails, Course } from "@/types/course"
import { CourseLevel } from "@prisma/client"
import { revalidatePath } from "next/cache"
import { uploadVideo } from './storage'
import { ActionResult, handleError, requireAdmin } from "../helpers"


// ===== COURSE ACTIONS =====

export async function getAllCoursesAdmin(): Promise<ActionResult<CourseDetails[]>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }
  
  try {
    const courses = await prisma.course.findMany({
      include: {
        modules: {
          include: {
            lessons: {
              include: {
                video: true
              }
            },
            assignments: true
          },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
    
    return { success: true, data: courses }
  } catch (error) {
    return handleError<CourseDetails[]>(error, 'fetch courses')
  }
}

export async function createCourseAction(formData: FormData): Promise<ActionResult<{ courseId: string }>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }
  
  try {
    const isPublishedValues = formData.getAll('isPublished') as string[]
    const isPublished = isPublishedValues.includes('true')
    
    const courseData = {
      name: formData.get('name') as string || '',
      description: formData.get('description') as string || '',
      level: (formData.get('level') as CourseLevel) || CourseLevel.BEGINNER,
      price: parseFloat(formData.get('price') as string) || 0,
      auditionFee: parseFloat(formData.get('auditionFee') as string) || 0,
      currency: formData.get('currency') as string || 'USD',
      instructorEmail: formData.get('instructorEmail') as string || '',
      isPublished
    }

    const course = await prisma.course.create({ data: courseData })
    
    revalidatePath('/admin/courses')
    return { success: true, data: { courseId: course.id } }
  } catch (error) {
    return handleError<{ courseId: string }>(error, 'create course')
  }
}

export async function updateCourseAction(courseId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }
  
  try {
    // Handle checkbox properly - when checked, both 'false' (hidden) and 'true' (checkbox) are sent
    // When unchecked, only 'false' (hidden) is sent
    const isPublishedValues = formData.getAll('isPublished') as string[]
    const isPublished = isPublishedValues.includes('true')
    
    const courseData = {
      name: formData.get('name') as string,
      description: formData.get('description') as string,
      level: formData.get('level') as CourseLevel,
      price: parseFloat(formData.get('price') as string) || 0,
      auditionFee: parseFloat(formData.get('auditionFee') as string) || 0,
      currency: formData.get('currency') as string,
      instructorEmail: formData.get('instructorEmail') as string,
      isPublished,
      updatedAt: new Date()
    }

    await prisma.course.update({
      where: { id: courseId },
      data: courseData
    })
    
    revalidatePath('/admin/courses','layout')
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'update course')
  }
}

export async function deleteCourseAction(courseId: string): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }
  
  try {
    // Delete in correct order due to foreign key constraints
    await prisma.$transaction([
      prisma.assignment.deleteMany({
        where: { module: { courseId } }
      }),
      prisma.lesson.deleteMany({
        where: { module: { courseId } }
      }),
      prisma.module.deleteMany({
        where: { courseId }
      }),
      prisma.course.delete({
        where: { id: courseId }
      })
    ])
    
    revalidatePath('/admin/courses')
    return { success: true }
  } catch (error) {
    return handleError(error, 'delete course')
  }
}

export async function getCourseByIdAction(courseId: string): Promise<ActionResult<CourseDetails>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) {
    return { success: false, error: authCheck.error }
  }
  
  try {
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: {
        modules: {
          include: {
            lessons: { 
              orderBy: { order: 'asc' },
              include: {
                video: true
              }
            },
            assignments: { orderBy: { deadline: 'asc' } }
          },
          orderBy: { order: 'asc' }
        }
      }
    })
    
    if (!course) {
      return { success: false, error: 'Course not found' }
    }
    
    return { success: true, data: course }
  } catch (error) {
    return handleError<CourseDetails>(error, 'fetch course')
  }
}

// ===== MODULE ACTIONS =====

export async function createModuleAction(courseId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const moduleData = {
      courseId,
      title: formData.get('title') as string,
      description: formData.get('description') as string || null,
      order: parseInt(formData.get('order') as string) || 1
    }

    await prisma.module.create({ data: moduleData })
    
    revalidatePath(`/admin/courses/${courseId}`)
    return { success: true }
  } 
  catch (error) {
    return handleError(error, 'create module')
  }
}

export async function updateModuleAction(moduleId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const moduleData = {
      title: formData.get('title') as string,
      description: formData.get('description') as string || null,
      updatedAt: new Date()
    }

    const module = await prisma.module.update({
      where: { id: moduleId },
      data: moduleData,
      select: { courseId: true }
    })
    
    revalidatePath(`/admin/courses/${module.courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'update module')
  }
}

export async function deleteModuleAction(moduleId: string): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const module = await prisma.module.findUnique({
      where: { id: moduleId },
      select: { courseId: true }
    })
    
    if (!module) {
      return { success: false, error: 'Module not found' }
    }

    await prisma.$transaction([
      prisma.assignment.deleteMany({ where: { moduleId } }),
      prisma.lesson.deleteMany({ where: { moduleId } }),
      prisma.module.delete({ where: { id: moduleId } })
    ])
    
    revalidatePath(`/admin/courses/${module.courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'delete module')
  }
}

// ===== LESSON ACTIONS =====

export async function createLessonAction(moduleId: string, formData: FormData): Promise<ActionResult<{lesson: any}>> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return { success: false, error: authCheck.error }
  
  try {
    const lessonData = {
      moduleId,
      title: formData.get('title') as string,
      description: formData.get('description') as string || '',
      durationMin: parseInt(formData.get('durationMin') as string) || null,
      order: parseInt(formData.get('order') as string) || 1,
    }

    const lesson = await prisma.lesson.create({ data: lessonData })
    
    // TODO: Handle video file upload if provided
    
    const module = await prisma.module.findUnique({
      where: { id: moduleId },
      select: { courseId: true }
    })
    
    if (module) {
      revalidatePath(`/admin/courses/${module.courseId}`)
    }
    
    return { success: true, data: { lesson } }
  } catch (error) {
    return handleError(error, 'create lesson')
  }
}

export async function updateLessonAction(lessonId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const lessonData = {
      title: formData.get('title') as string,
      description: formData.get('description') as string || '',
      durationMin: parseInt(formData.get('durationMin') as string) || null,
      updatedAt: new Date()
    }

    const lesson = await prisma.lesson.update({
      where: { id: lessonId },
      data: lessonData,
      include: { module: { select: { courseId: true } }, video: true }
    })
    
    revalidatePath(`/admin/courses/${lesson.module.courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'update lesson')
  }
}

export async function deleteLessonAction(lessonId: string): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: { module: { select: { courseId: true } } }
    })
    
    if (!lesson) {
      return { success: false, error: 'Lesson not found' }
    }

    await prisma.lesson.delete({ where: { id: lessonId } })
    
    revalidatePath(`/admin/courses/${lesson.module.courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'delete lesson')
  }
}

// ===== ASSIGNMENT ACTIONS =====

export async function createAssignmentAction(moduleId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const assignmentData = {
      moduleId,
      title: formData.get('title') as string,
      description: formData.get('description') as string || null,
      assignmentPath: formData.get('assignmentPath') as string || null,
      deadline: new Date(formData.get('deadline') as string)
    }

    const assignment = await prisma.assignment.create({ data: assignmentData })
    if (!assignment) {
      return { success: false, error: 'Assignment not created' }
    }

    revalidatePath(`/dashboard/courses`, "layout")
    
    return { success: true }
  } catch (error) {
    return handleError(error, 'create assignment')
  }
}

export async function updateAssignmentAction(assignmentId: string, formData: FormData): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const assignmentData = {
      title: formData.get('title') as string,
      description: formData.get('description') as string || null,
      assignmentPath: formData.get('assignmentPath') as string || null,
      deadline: new Date(formData.get('deadline') as string),
      updatedAt: new Date()
    }

    const assignment = await prisma.assignment.update({
      where: { id: assignmentId },
      data: assignmentData,
      include: { module: { select: { courseId: true } } }
    })
    
    revalidatePath(`/admin/courses/${assignment.module.courseId}`)
    return { success: true }
  } 
  catch (error) {
    return handleError(error, 'update assignment')
  }
}

export async function deleteAssignmentAction(assignmentId: string): Promise<ActionResult> {
  const authCheck = await requireAdmin()
  if (!authCheck.success) return authCheck
  
  try {
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: { module: { select: { courseId: true } } }
    })
    
    if (!assignment) {
      return { success: false, error: 'Assignment not found' }
    }

    await prisma.assignment.delete({ where: { id: assignmentId } })
    
    revalidatePath(`/admin/courses/${assignment.module.courseId}`)
    return { success: true }
  } catch (error) {
    return handleError(error, 'delete assignment')
  }
}

// Legacy exports for backward compatibility (will be removed)
export const createCourse = createCourseAction
export const updateCourse = updateCourseAction
export const createModule = createModuleAction
export const createLesson = createLessonAction
export const createAssignment = createAssignmentAction
