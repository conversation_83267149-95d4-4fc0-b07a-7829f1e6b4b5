'use client';

import { useRouter, useSearchParams } from 'next/navigation';

interface SortableTableHeaderProps {
  field: string;
  children: React.ReactNode;
  className?: string;
}

export default function SortableTableHeader({ field, children, className }: SortableTableHeaderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const sortBy = searchParams.get('sortBy') || 'createdAt';
  const sortOrder = searchParams.get('sortOrder') || 'desc';

  const handleSort = () => {
    const params = new URLSearchParams(searchParams);
    
    if (sortBy === field) {
      params.set('sortOrder', sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      params.set('sortBy', field);
      params.set('sortOrder', 'asc');
    }
    
    router.push(`?${params.toString()}`);
  };

  return (
    <th 
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 ${className || ''}`}
      onClick={handleSort}
    >
      <div className="flex items-center gap-1">
        {children}
        {sortBy === field && (
          <span className="text-blue-600">
            {sortOrder === 'asc' ? '↑' : '↓'}
          </span>
        )}
      </div>
    </th>
  );
} 