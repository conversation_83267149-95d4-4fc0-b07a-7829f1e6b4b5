"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Music,
  BookOpen,
  Settings,
  MessageSquare,
  User,
  Award,
  NotebookPen,
  Menu,
  X,
  Mic,
  Video,
  Users,
  LogOut,
  ClipboardCheck,
  GraduationCap,
  BarChart3,
  Headphones,
  HelpCircle,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface NavigationItem {
  to: string;
  icon: React.ElementType;
  label: string;
  badge?: number;
  children?: NavigationItem[];
}

interface NavigationCategory {
  category: string;
  items: NavigationItem[];
}

interface ModernSidebarProps {
  userDetails?: {
    name?: string;
    email?: string;
    role?: string;
    avatar?: string;
  };
}

const getRoleBasedNavigation = (role: string): NavigationCategory[] => {
  switch (role) {
    case "SUPER_ADMIN":
      return [
        {
          category: "Dashboard",
          items: [
            { to: "/admin", icon: LayoutDashboard, label: "Overview" },
            { to: "/admin/analytics", icon: BarChart3, label: "Analytics" },
          ],
        },
        {
          category: "Admissions",
          items: [
            { to: "/admin/admissions", icon: ClipboardCheck, label: "Auditions" },
            { to: "/admin/students", icon: GraduationCap, label: "Students" },
          ],
        },
        {
          category: "Content",
          items: [
            { to: "/admin/courses", icon: BookOpen, label: "Courses" },
            { to: "/admin/feedback", icon: MessageSquare, label: "Feedback" },
            { to: "/admin/sing-along", icon: Mic, label: "Sing Along" },
            { to: "/admin/media", icon: Video, label: "Media Library" },
          ],
        },
        {
          category: "System",
          items: [
            { to: "/admin/users", icon: Users, label: "User Management" },
            { to: "/admin/settings", icon: Settings, label: "Settings" },
          ],
        },
      ];

    case "ADMISSION_SUPPORT":
      return [
        {
          category: "Admissions",
          items: [
            { to: "/admin/admissions", icon: ClipboardCheck, label: "Auditions" },
            { to: "/admin/students", icon: GraduationCap, label: "Students" },
          ],
        },
        {
          category: "Support",
          items: [
            { to: "/admin/support", icon: HelpCircle, label: "Help Desk" },
          ],
        },
      ];

    case "COURSE_MANAGER":
      return [
        {
          category: "Content Management",
          items: [
            { to: "/admin/courses", icon: BookOpen, label: "Courses" },
            { to: "/admin/sing-along", icon: Mic, label: "Sing Along" },
            { to: "/admin/media", icon: Video, label: "Media Library" },
          ],
        },
        {
          category: "Students",
          items: [
            { to: "/admin/students", icon: GraduationCap, label: "Students" },
          ],
        },
      ];

    case "STUDENT":
    default:
      return [
        {
          category: "Learning",
          items: [
            { to: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
            { to: "/dashboard/courses", icon: BookOpen, label: "My Courses" },
            { to: "/dashboard/sing-along", icon: Mic, label: "Sing Along" },
          ],
        },
        {
          category: "Progress",
          items: [
            {
              to: "/dashboard/assignments",
              icon: NotebookPen,
              label: "Assignments",
            },
            {
              to: "/dashboard/feedback",
              icon: MessageSquare,
              label: "Feedback",
            },
            {
              to: "/dashboard/recordings",
              icon: Headphones,
              label: "Recordings",
            },
          ],
        },
        {
          category: "Profile",
          items: [
            { to: "/dashboard/profile", icon: User, label: "Profile" },
            {
              to: "/dashboard/certificates",
              icon: Award,
              label: "Certificates",
            },
            { to: "/dashboard/support", icon: HelpCircle, label: "Support" },
          ],
        },
      ];
  }
};

const NavItem: React.FC<{
  item: NavigationItem;
  isActive: boolean;
  onClick?: () => void;
}> = ({ item, isActive, onClick }) => {
  const Icon = item.icon;

  return (
    <Link
      href={item.to}
      onClick={onClick}
      className={cn(
        "group flex items-center gap-3 px-3 py-2.5 rounded-md text-sm font-medium transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-blue-500",
        isActive
          ? "bg-blue-600 text-white"
          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
      )}
    >
      <Icon className={cn(
        "h-5 w-5 flex-shrink-0",
        isActive ? "text-white" : "text-gray-500"
      )} />
      
      <span className="flex-1">{item.label}</span>
    </Link>
  );
};

const UserProfile: React.FC<{
  userDetails: ModernSidebarProps["userDetails"];
  onLogout: () => void;
}> = ({ userDetails, onLogout }) => {
  const initials = userDetails?.name
    ? userDetails.name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2)
    : "U";

  const getRoleLabel = (role?: string) => {
    switch (role) {
      case "SUPER_ADMIN":
        return "Super Admin";
      case "ADMISSION_SUPPORT":
        return "Admission Support";
      case "COURSE_MANAGER":
        return "Course Manager";
      case "STUDENT":
      default:
        return "Student";
    }
  };

  return (
    <div className="p-4 border-t border-gray-200">
      <div className="flex items-center gap-3 mb-4">
        <div className="h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center text-white font-semibold text-sm">
          {initials}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-semibold text-gray-900 truncate">
            {userDetails?.name || "User"}
          </p>
          <p className="text-xs text-gray-500 truncate">
            {getRoleLabel(userDetails?.role)}
          </p>
        </div>
      </div>

      <button
        onClick={onLogout}
        className="flex items-center gap-2 w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md transition-colors"
      >
        <LogOut className="h-4 w-4" />
        Sign Out
      </button>
    </div>
  );
};

// Skeleton Components
const SkeletonNavItem: React.FC = () => (
  <div className="flex items-center gap-3 px-3 py-2.5 rounded-md">
    <div className="h-5 w-5 bg-gray-200 rounded animate-pulse" />
    <div className="h-4 bg-gray-200 rounded animate-pulse flex-1" />
  </div>
);

const SkeletonCategory: React.FC = () => (
  <div className="mb-6">
    <div className="px-3 py-2">
      <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
    </div>
    <div className="space-y-1 mt-2">
      <SkeletonNavItem />
      <SkeletonNavItem />
      <SkeletonNavItem />
    </div>
  </div>
);

const SkeletonUserProfile: React.FC = () => (
  <div className="p-4 border-t border-gray-200">
    <div className="flex items-center gap-3 mb-4">
      <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse" />
      <div className="flex-1 min-w-0">
        <div className="h-4 bg-gray-200 rounded animate-pulse mb-1" />
        <div className="h-3 w-16 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
    <div className="flex items-center gap-2 w-full px-3 py-2">
      <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
      <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
    </div>
  </div>
);

const SidebarSkeleton: React.FC<{ isMobile?: boolean }> = ({ isMobile }) => {
  if (isMobile) {
    return (
      <>
        <header className="fixed top-0 left-0 right-0 h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 z-50">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-md bg-blue-600 flex items-center justify-center">
              <Music className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-900">Dhwanini</span>
          </div>
          <div className="h-6 w-6 bg-gray-200 rounded animate-pulse" />
        </header>
      </>
    );
  }

  return (
    <aside className="fixed inset-y-0 left-0 w-64 flex flex-col z-40 bg-white border-r border-gray-200">
      {/* Header */}
      <div className="flex items-center gap-3 h-16 px-4 border-b border-gray-200">
        <div className="h-8 w-8 rounded-md bg-blue-600 flex items-center justify-center">
          <Music className="h-5 w-5 text-white" />
        </div>
        <span className="text-lg font-semibold text-gray-900">Dhwanini</span>
      </div>

      {/* Navigation Skeleton */}
      <nav className="flex-1 overflow-y-auto py-4 px-3">
        <SkeletonCategory />
        <SkeletonCategory />
        <SkeletonCategory />
      </nav>

      {/* User Profile Skeleton */}
      <SkeletonUserProfile />
    </aside>
  );
};

const ModernSidebar: React.FC<ModernSidebarProps> = ({ userDetails }) => {
  const pathname = usePathname();
  const { user, logout, isLoading } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const currentUser = user || userDetails;
  const navigation = getRoleBasedNavigation(currentUser?.role || "STUDENT");

  const isItemActive = (itemPath: string) => {
    if (pathname === itemPath) return true;
    if (itemPath === "/dashboard" && pathname === "/dashboard") return true;
    if (itemPath === "/admin" && pathname === "/admin") return true;
    
    if (itemPath !== "/" && itemPath !== "/dashboard" && itemPath !== "/admin") {
      return pathname.startsWith(`${itemPath}/`);
    }
    return false;
  };

  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const handleLogout = async () => {
    await logout();
    setIsMobileMenuOpen(false);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Show skeleton while loading
  if (isLoading) {
    return <SidebarSkeleton isMobile={isMobile} />;
  }

  // Mobile Header
  if (isMobile) {
    return (
      <>
        <header className="fixed top-0 left-0 right-0 h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 z-50">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-md bg-blue-600 flex items-center justify-center">
              <Music className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg font-semibold text-gray-900">Dhwanini</span>
          </div>

          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6 text-gray-600" />
            ) : (
              <Menu className="h-6 w-6 text-gray-600" />
            )}
          </button>
        </header>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <>
            <div
              className="fixed inset-0 bg-black/20 z-40"
              onClick={closeMobileMenu}
            />
            <div className="fixed top-16 left-0 right-0 bg-white shadow-lg z-50 h-[calc(100vh-4rem)] overflow-y-auto border-t border-gray-200">
              <div className="p-4">
                {navigation.map((category) => (
                  <div key={category.category} className="mb-6">
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                      {category.category}
                    </div>
                    <div className="space-y-1 mt-2">
                      {category.items.map((item) => (
                        <NavItem
                          key={item.to}
                          item={item}
                          isActive={isItemActive(item.to)}
                          onClick={closeMobileMenu}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <UserProfile userDetails={currentUser} onLogout={handleLogout} />
            </div>
          </>
        )}
      </>
    );
  }

  // Desktop Sidebar
  return (
    <aside
      className="fixed inset-y-0 left-0 w-64 flex flex-col z-40 bg-white border-r border-gray-200"
      aria-label="Sidebar navigation"
    >
      {/* Header */}
      <div className="flex items-center gap-3 h-16 px-4 border-b border-gray-200">
        <div className="h-8 w-8 rounded-md bg-blue-600 flex items-center justify-center">
          <Music className="h-5 w-5 text-white" />
        </div>
        <span className="text-lg font-semibold text-gray-900">Dhwanini</span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4 px-3">
        {navigation.map((category) => (
          <div key={category.category} className="mb-6">
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
              {category.category}
            </div>
            <div className="space-y-1 mt-2">
              {category.items.map((item) => (
                <NavItem
                  key={item.to}
                  item={item}
                  isActive={isItemActive(item.to)}
                />
              ))}
            </div>
          </div>
        ))}
      </nav>

      {/* User Profile */}
      <UserProfile userDetails={currentUser} onLogout={handleLogout} />
    </aside>
  );
};

export default ModernSidebar;