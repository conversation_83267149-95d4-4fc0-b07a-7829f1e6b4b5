'use server';

import { processLrcFile } from "@/lib/lrc-parser";
import { Song } from "@/types/sing-along";
import { prisma } from "@/utils/prisma/prisma";
import { createClient } from "@/utils/supabase/server";
import { CourseLevel, Music } from "@prisma/client";
import { getUserDetails } from "../auth";

const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-sing-along';


export const getAvailableSongs = async () => {
  try {
    const songs: Music[] = await prisma.music.findMany({
      select: {
        id: true,
        title: true,
        genre: true,
        duration: true,
        difficulty: true,
        createdAt: true,
        updatedAt: true,
      }
    });
    return { songs: songs, error: null };
  } catch (error) {
    console.error('Error fetching available songs:', error);
    return { songs: [], error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

export const getSongByTitle = async (title: string): Promise<{song: Song | null, error: string | null}> => {
    try {
        const songs = await prisma.music.findUnique({
            where: {
                title: title
            },
            select: {
                id: true,
                title: true,
                genre: true,
                duration: true,
                difficulty: true,
                createdAt: true,
                updatedAt: true,
            }
        });

        if (!songs) {
            return { song: null, error: 'Song not found' };
        }

        const supabase = await createClient();
        const { data, error } = await supabase.storage.from(BUCKET_NAME).download(`lyrics/${songs.title}.lrc`);
        if (error) {
            return { song: null, error: error.message };
        }

        const lrcContent = await data.text();
        
        const songWithLyrics = {
            ...songs,
            lyrics: processLrcFile(lrcContent || '').lyrics
        }
        
        return { song: songWithLyrics, error: null };
    }
    catch (error) {
        console.error('Error fetching songs:', error);
        return { song: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
}

export const getAudioUrl = async (filename: string): Promise<{audioUrl: string | null, error: string | null}> => {
  try {
    const supabase = await createClient();
    console.log("filename", filename)
    const files = await supabase.storage.from(BUCKET_NAME).list('music');
    console.log("files", files)
    const { data, error } = await supabase.storage.from(BUCKET_NAME).createSignedUrl(`music/${filename}.mp3`,3600);
    console.log("data", data)
    if (error) {
        return { audioUrl: null, error: error.message };
    }

    return { audioUrl: data.signedUrl, error: null };
  }
  catch (error) {
    console.error('Error fetching audio url:', error);
    return { audioUrl: null, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export const storeUserRecording = async(fileName: string, mimeType: string, duration: number, songName: string) => {
  try {
    const { user, error } = await getUserDetails();
    if(error) {
      return { success: false, error: error };
    }
    console.log('user', user, 'fileName', fileName, 'mimeType', mimeType, 'duration', duration, 'songName', songName)
    const recording = await prisma.userRecording.create({
      data: {
        fileName,
        mimeType,
        duration: duration.toString(),
        musicTitle: songName,
        userId: user.id
      }
    });
    if(!recording) {
      return { success: false, error: 'Error storing recording' };
    }
    return { success: true, recording: recording, error: null };
  }
  catch (error) {
    console.error('Error storing user recording:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}