import { AlertCircle, BookOpen, PlayCircle } from 'lucide-react'

interface CourseLoadingProps {}

export function CourseLoading({}: CourseLoadingProps) {
  return (
    <div className="min-h-screen bg-white">
      <div className="lg:grid lg:grid-cols-4 h-screen">
        <div className="hidden lg:block border-r border-gray-200 bg-white">
          <div className="p-6 space-y-4 animate-pulse">
            <div className="h-5 bg-gray-200 rounded"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="space-y-3">
              {[1,2,3].map(i => <div key={i} className="h-12 bg-gray-200 rounded"></div>)}
            </div>
          </div>
        </div>
        <div className="lg:col-span-3 bg-white">
          <div className="aspect-video bg-gray-200 animate-pulse"></div>
          <div className="p-6 space-y-4 animate-pulse">
            <div className="h-6 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface CourseErrorProps {
  error: string
  onRetry?: () => void
}

export function CourseError({ error, onRetry }: CourseErrorProps) {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-red-100 flex items-center justify-center">
          <AlertCircle className="h-8 w-8 text-red-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-3">Failed to Load Course</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <button
          onClick={onRetry || (() => window.location.reload())}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        >
          Try Again
        </button>
      </div>
    </div>
  )
}

interface CourseNotFoundProps {}

export function CourseNotFound({}: CourseNotFoundProps) {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gray-100 flex items-center justify-center">
          <BookOpen className="h-8 w-8 text-gray-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-3">Course Not Found</h2>
        <p className="text-gray-600">The course you're looking for doesn't exist or you don't have access to it.</p>
      </div>
    </div>
  )
}

interface EmptyContentProps {
  onOpenSidebar: () => void
}

export function EmptyContent({ onOpenSidebar }: EmptyContentProps) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-white">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-blue-600 flex items-center justify-center">
          <PlayCircle className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-3">Select Content</h2>
        <p className="text-gray-600 mb-6">
          Choose a lesson or assignment from the course content to begin learning.
        </p>
        <button
          onClick={onOpenSidebar}
          className="lg:hidden inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        >
          <BookOpen className="h-4 w-4" />
          Browse Content
        </button>
      </div>
    </div>
  )
} 