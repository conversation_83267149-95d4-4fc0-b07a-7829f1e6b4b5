"use server"

import { getUserDetails } from "../auth"
import { prisma } from "@/utils/prisma/prisma"
import { CourseDetails, CourseStat, AccessableCourseDetails } from "@/types/course"
import { ActionResult, handleError } from "../helpers"
import { revalidatePath } from "next/cache"


export async function getCourses(email: string): Promise<ActionResult<CourseStat[]>> {
    
    
    const user = await prisma.user.findUnique({
        where: {
            email: email
        }
    })
    if(!user) {
        return { success: false, error: 'User not found' }
    }

    try {
        const enrollments = await prisma.enrollment.findMany({
            where: {
                userId: user.id
            },
            select: {
                courseId: true
            }
        })
        const courseStats: CourseStat[] = await prisma.course.findMany({
            where: {
                isPublished: true,
                id: {
                    in: enrollments.map(enrollment => enrollment.courseId)
                }
            },
            include: {
                _count: {
                    select: {
                        modules: true,
                        applications: true
                    }
                },
            }
        });
        
    return { success: true, data: courseStats }
    } 
    catch (error) {
        return handleError<CourseStat[]>(error, 'fetch courses')
    }
}


export async function getCourseDetails(courseId: string, email: string): Promise<ActionResult<CourseDetails>> {
    
 
    const user = await prisma.user.findUnique({
        where: {
            email: email
        }
    })
    if(!user) {
        return { success: false, error: 'User not found' }
    }

    try{
        const course = await prisma.course.findUnique({
            where: {
                id: courseId
            },
            include: {
                modules: {
                    include: {
                        lessons: {
                            include: {
                                video: true,
                                completedByUsers: {
                                    where: {
                                        id: user.id
                                    },
                                    select: {
                                        id: true
                                    }
                                }
                            }
                        },
                        assignments: {
                            include: {
                                submissions: {
                                    where: {
                                        userId: user.id
                                    },
                                    select: {
                                        id: true,
                                        feedback: true,
                                        status: true,
                                        submittedAt: true,
                                        updatedAt: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        })

        const courseWithCompletions = {
            ...course,
            modules: course.modules.map(module => ({
                ...module,
                lessons: module.lessons.map(lesson => ({
                    ...lesson,
                    video: lesson.video || null,
                    completed: lesson.completedByUsers.length > 0
                })),
                assignments: module.assignments.map(assignment => ({
                    ...assignment,
                    submission: assignment.submissions.length > 0 ? assignment.submissions[0] : null,
                    submitted: assignment.submissions.length > 0
                }))
            }))
        }

        return { success: true, data: courseWithCompletions as CourseDetails }
    }
    catch(error) {
        return handleError<CourseDetails>(error, 'fetch course details')
    }
}


export async function markLessonAsCompleted(lessonId: string): Promise<ActionResult<boolean>> {
    const {user,error} = await getUserDetails()
    if (error) {
        return { success: false, error: error }
    }

    try {
        const completed = await prisma.lesson.update({
            where: {
                id: lessonId
            },
            data: {
                completedByUsers: {
                    connect: {
                        id: user.id
                    }
                }
            }
        })
        if(!completed) {
            return { success: false, error: 'Failed to mark lesson as completed' }
        }

        revalidatePath(`/`, "layout")
        return { success: true, data: true }
    }
    catch(error) {
        return handleError(error, 'mark lesson as completed')
    }
}

export async function markModuleAsCompleted(moduleId: string): Promise<ActionResult<boolean>> {
    const {user,error} = await getUserDetails()
    if (error) {
        return { success: false, error: error }
    }

    try {
        // If Assignment is Graded.
        const Assignment = await prisma.module.findUnique({
            where: {
                id: moduleId
            },
            select: {
                assignments: {
                    select: {
                        submissions: {
                            where: {
                                userId: user.id
                            }
                        }
                    }
                }
            }
        })

        if(Assignment?.assignments.some(assignment => assignment.submissions.length === 0)) {
            return { success: false, error: 'All assignments must be submitted before marking the module as completed' }
        }

        const completed = await prisma.module.update({
            where: {
                id: moduleId
            },
            data: {
                completedByUsers: {
                    connect: {
                        id: user.id
                    }
                }
            }
        })
        if(!completed) {
            return { success: false, error: 'Failed to mark module as completed' }
        }

        revalidatePath(`/`, "layout")
        return { success: true, data: true }
    }
    catch(error) {
        return handleError(error, 'mark module as completed')
    }
}

export async function getAccessableDetails(courseId: string, email: string): Promise<ActionResult<AccessableCourseDetails>> {
    
    const user = await prisma.user.findUnique({
        where: {
            email: email
        }
    })
    if(!user) {
        return { success: false, error: 'User not found' }
    }

    try {
        const course = await prisma.course.findUnique({
            where: {
                id: courseId
            },
            include: {
                modules: {
                    include: {
                        lessons: {
                            include: {
                                video: true,
                                completedByUsers: {
                                    where: {
                                        id: user.id
                                    },
                                    select: {
                                        id: true
                                    }
                                }
                            }
                        },
                        assignments: {
                            include: {
                                feedbacks:{
                                    where: {
                                        userId: user.id
                                    },
                                    select: {
                                        id: true
                                    }
                                },
                                submissions: {
                                    where: {
                                        userId: user.id
                                    },
                                    select: {
                                        id: true,
                                        feedback: true,
                                        status: true,
                                        submittedAt: true,
                                        updatedAt: true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        })

        if(!course) {
            return { success: false, error: 'Course not found' }
        }

        const courseWithCompletions = {
            ...course,
            modules: course?.modules.map(module => ({
                ...module,
                lessons: module.lessons.map(lesson => ({
                    ...lesson,
                    video: lesson.video || null,
                    completed: lesson.completedByUsers.length > 0
                })),
                assignments: module.assignments.map(assignment => ({
                    ...assignment,
                    submission: assignment.submissions.length > 0 ? assignment.submissions[0] : null,
                    submitted: assignment.submissions.length > 0 && assignment.feedbacks.length > 0
                }))
            }))
        }
        return { success: true, data: courseWithCompletions as AccessableCourseDetails }
    }
    catch(error) {
        return handleError(error, 'get accessable details')
    }
    
}

export async function createAssignmentSubmission(assignmentId: string, filePath: string): Promise<ActionResult<boolean>> {
    const {user,error} = await getUserDetails()
    if (error) {
        return { success: false, error: error }
    }

    try {
        const submission = await prisma.assignmentSubmission.create({
            data: {
                assignmentId: assignmentId,
                userId: user.id,
                assignmentPath: filePath
            }
        })
        if(!submission) {
            return { success: false, error: 'Failed to create assignment submission' }
        }

        revalidatePath(`/`, "layout")
        return { success: true, error: null }
    }
    catch(error) {
        return handleError(error, 'create assignment submission')
    }
}