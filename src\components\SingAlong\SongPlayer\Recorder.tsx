import { useEffect, useState, useRef } from "react";
import { Mic, StopCircle, Save, Clock, AlertCircle, Volume2 } from "lucide-react";
import { storeRecording } from "@/data-access/sing-along/record";

type RecordingState = "idle" | "recording" | "disabled";

export default function Recorder({
  songName = "UR",
  audioElement,
  OnSave,
}: {
  songName?: string;
  audioElement?: HTMLAudioElement;
  OnSave?: (blob: Blob) => void;
}) {
  const [recordingState, setRecordingState] = useState<RecordingState>("idle");
  const [error, setError] = useState<string | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isStoring, setIsStoring] = useState(false);
  const [micVolume, setMicVolume] = useState(0.8);
  const [audioVolume, setAudioVolume] = useState(0.5);

  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const chunks = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const gainsRef = useRef<{
    mic?: GainNode;
    playback?: GainNode;
    recording?: GainNode;
  }>({});

  function formatDuration(seconds: number) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  }

  function cleanup() {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (audioElement) {
      audioElement.pause();
      audioElement.currentTime = 0;
    }
    if (audioContextRef.current?.state !== 'closed') {
      audioContextRef.current?.close();
      audioContextRef.current = null;
    }
    gainsRef.current = {};
  }

  async function startRecording() {
    try {
      setError(null);
      cleanup();

      const audioContext = new AudioContext();
      audioContextRef.current = audioContext;

      const mixedDestination = audioContext.createMediaStreamDestination();

      // Setup microphone
      const micStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 48000,
          channelCount: 1,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
        },
      });
      
      const micSource = audioContext.createMediaStreamSource(micStream);
      const micGain = audioContext.createGain();
      micGain.gain.value = micVolume;
      gainsRef.current.mic = micGain;
      
      micSource.connect(micGain).connect(mixedDestination);

      // Setup background audio if provided
      if (audioElement) {
        const audioSource = audioContext.createMediaElementSource(audioElement);
        const playbackGain = audioContext.createGain();
        const recordingGain = audioContext.createGain();
        
        playbackGain.gain.value = audioVolume;
        recordingGain.gain.value = audioVolume;
        gainsRef.current.playback = playbackGain;
        gainsRef.current.recording = recordingGain;
        
        // Playback path
        audioSource.connect(playbackGain).connect(audioContext.destination);
        // Recording path
        audioSource.connect(recordingGain).connect(mixedDestination);

        await audioElement.play().catch(console.error);
      }

      const mimeType = MediaRecorder.isTypeSupported("audio/webm") ? "audio/webm" : "audio/mp4";
      mediaRecorder.current = new MediaRecorder(mixedDestination.stream, { mimeType });

      chunks.current = [];
      
      mediaRecorder.current.ondataavailable = (e) => {
        if (e.data.size > 0) chunks.current.push(e.data);
      };

      mediaRecorder.current.onstop = () => {
        const blob = new Blob(chunks.current, { type: mimeType });
        setAudioBlob(blob);
        setRecordingState("idle");
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };

      mediaRecorder.current.start(1000);
      setRecordingState("recording");
      
      setRecordingDuration(0);
      timerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
      
    } catch (err) {
      const e = err as DOMException;
      if (e.name === "NotAllowedError") {
        setError("Microphone access denied.");
        setRecordingState("disabled");
      } else {
        setError("Failed to start recording: " + e.message);
      }
    }
  }

  function stopRecording() {
    if (mediaRecorder.current?.state === "recording") {
      mediaRecorder.current.stop();
      cleanup();
    }
  }

  function handleRecording() {
    if (recordingState === "disabled") return;
    recordingState === "idle" ? startRecording() : stopRecording();
  }

  async function handleStoreAudio() {
    if (!audioBlob) return;
    setIsStoring(true);
    setError(null);

    try {

      if(OnSave) {
        OnSave(audioBlob);
        return;
      }

      const { success, error: recordingError } = await storeRecording(audioBlob, audioBlob.type, songName, recordingDuration);
      if(!success) {
        setError(recordingError);
        return;
      }

      setAudioBlob(null);
      setRecordingDuration(0);
      chunks.current = [];
    } catch (err) {
      setError("Save failed: " + (err as Error).message);
    } finally {
      setIsStoring(false);
    }
  }

  function handleMicVolumeChange(value: number) {
    setMicVolume(value);
    if (gainsRef.current.mic) {
      gainsRef.current.mic.gain.value = value;
    }
  }

  function handleAudioVolumeChange(value: number) {
    setAudioVolume(value);
    if (gainsRef.current.playback) {
      gainsRef.current.playback.gain.value = value;
    }
    if (gainsRef.current.recording) {
      gainsRef.current.recording.gain.value = value;
    }
  }

  function clearError() {
    setError(null);
    if (recordingState === "disabled") setRecordingState("idle");
  }

  useEffect(() => cleanup, []);

  return (
    <div className="flex flex-col items-center gap-4 p-6 max-w-md mx-auto">
      <h2 className="text-xl font-semibold mb-2">Audio Recorder</h2>

      <div className="w-full bg-gray-50 p-4 rounded-lg space-y-4">
        <h3 className="text-sm font-medium flex items-center gap-2">
          <Volume2 className="h-4 w-4" />
          Volume Controls
        </h3>

        <div>
          <label className="text-sm block mb-1">Microphone: {Math.round(micVolume * 100)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={micVolume}
            onChange={(e) => handleMicVolumeChange(+e.target.value)}
            className="w-full"
          />
        </div>

        {audioElement && (
          <div>
            <label className="text-sm block mb-1">Background Audio: {Math.round(audioVolume * 100)}%</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={audioVolume}
              onChange={(e) => handleAudioVolumeChange(+e.target.value)}
              className="w-full"
            />
          </div>
        )}
      </div>

      <button
        onClick={handleRecording}
        className={`w-full py-3 rounded-lg font-medium flex items-center justify-center gap-2 ${
          recordingState === "recording"
            ? "bg-red-500 text-white animate-pulse"
            : recordingState === "disabled"
            ? "bg-gray-300 text-gray-500 cursor-not-allowed"
            : "bg-blue-600 hover:bg-blue-700 text-white"
        }`}
      >
        {recordingState === "recording" ? <StopCircle className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
        {recordingState === "recording" ? "Stop Recording" : "Start Recording"}
      </button>

      {recordingState === "recording" && (
        <div className="text-red-700 bg-red-100 px-4 py-2 rounded-lg flex gap-2 items-center">
          <Clock className="h-5 w-5 animate-pulse" />
          <span className="font-mono text-lg">{formatDuration(recordingDuration)}</span>
        </div>
      )}

      {error && (
        <div className="text-red-600 bg-red-50 p-4 rounded-md text-sm w-full">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              {error}
              <button onClick={clearError} className="ml-4 underline text-xs">
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {audioBlob && (
        <div className="w-full mt-6 p-5 rounded-xl border bg-white shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Recording Preview</h3>
            <div className="text-sm text-gray-600">
              Duration: {formatDuration(recordingDuration)}
            </div>
          </div>
          <audio controls src={URL.createObjectURL(audioBlob)} className="w-full mb-4 rounded-lg" crossOrigin='anonymous'/>
          <button
            onClick={handleStoreAudio}
            disabled={isStoring}
            className={`w-full flex items-center justify-center gap-2 py-3 rounded-md text-white ${
              isStoring ? "bg-gray-400" : "bg-green-600 hover:bg-green-700"
            }`}
          >
            <Save className="h-5 w-5" />
            {isStoring ? "Saving..." : "Save Recording"}
          </button>
        </div>
      )}
    </div>
  );
}