import Link from 'next/link'
import { ArrowLeft, BookOpen, Users, Target, TrendingUp, Calendar, Mail, MessageSquare } from 'lucide-react'
import { getCourseDetailAnalytics } from '@/data-access/analytics/analytics'
import { formatCurrency, formatDate } from '@/lib/utils'
import { notFound } from 'next/navigation'

interface CourseAnalyticsPageProps {
  params: Promise<{ courseId: string }>
}

export default async function CourseAnalyticsPage({ params }: CourseAnalyticsPageProps) {
  const { courseId } = await params
  
  const result = await getCourseDetailAnalytics(courseId)
  
  if (!result.success) {
    if (result.error === 'Course not found') {
      notFound()
    }
    throw new Error(result.error || 'Failed to load course analytics')
  }

  const { course, users, moduleStats, recentActivity } = result.data!

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* <PERSON>er */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href="/admin/analytics"
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </Link>
            <div className="p-2 bg-indigo-100 rounded-lg">
              <BookOpen className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{course.name}</h1>
              <p className="text-gray-600">Course analytics and student progress</p>
            </div>
          </div>
        </div>

        {/* Course Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Enrollments</p>
                <p className="text-2xl font-bold text-gray-900">{course.enrollmentCount}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Active Students</p>
                <p className="text-2xl font-bold text-gray-900">{course.activeUsers}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{course.completionRate}%</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Revenue Generated</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(course.revenueGenerated)}</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Module Stats */}
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Module Performance</h3>
            </div>
            <div className="p-6">
              {moduleStats.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No modules available</p>
              ) : (
                <div className="space-y-4">
                  {moduleStats.map((module) => (
                    <div key={module.id} className="p-4 border border-gray-100 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{module.title}</h4>
                        <span className="text-sm text-gray-500">Module {module.order}</span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Lessons</p>
                          <p className="font-semibold">{module.lessonCount}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Assignments</p>
                          <p className="font-semibold">{module.assignmentCount}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
            <div className="p-6">
              {recentActivity.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No recent activity</p>
              ) : (
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <Calendar className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{activity.userName}</p>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">{formatDate(activity.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Course Info */}
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Course Information</h3>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <p className="text-sm text-gray-500">Level</p>
                <p className="font-semibold text-gray-900">{course.level}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Price</p>
                <p className="font-semibold text-gray-900">
                  {course.price ? formatCurrency(course.price) : 'Free'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Content</p>
                <div className="text-sm space-y-1">
                  <p><span className="font-semibold">{course.totalModules}</span> modules</p>
                  <p><span className="font-semibold">{course.totalLessons}</span> lessons</p>
                  <p><span className="font-semibold">{course.totalAssignments}</span> assignments</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  course.isPublished 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  {course.isPublished ? 'Published' : 'Draft'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Students Table */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Enrolled Students</h3>
            <p className="text-gray-600 mt-1">Detailed progress for each student</p>
          </div>
          
          <div className="overflow-x-auto">
            {users.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">No students enrolled</h4>
                <p className="text-gray-600">Students will appear here once they enroll in this course</p>
              </div>
            ) : (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left p-4 font-medium text-gray-900">Student</th>
                    <th className="text-left p-4 font-medium text-gray-900">Progress</th>
                    <th className="text-left p-4 font-medium text-gray-900">Modules</th>
                    <th className="text-left p-4 font-medium text-gray-900">Lessons</th>
                    <th className="text-left p-4 font-medium text-gray-900">
                      Assignments
                      <span className="text-xs text-gray-500 block">Click name for details</span>
                    </th>
                    <th className="text-left p-4 font-medium text-gray-900">Feedback</th>
                    <th className="text-left p-4 font-medium text-gray-900">Enrolled</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="p-4">
                        <Link 
                          href={`/admin/analytics/${courseId}/${user.id}`}
                          className="block hover:text-blue-600 transition-colors"
                        >
                          <div>
                            <p className="font-medium text-gray-900">{user.name}</p>
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {user.email}
                            </p>
                          </div>
                        </Link>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-indigo-600 h-2 rounded-full" 
                              style={{ width: `${Math.min(user.progressPercentage, 100)}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium text-gray-900">
                            {user.progressPercentage}%
                          </span>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-900">
                          {user.completedModules}/{user.totalModules}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-900">
                          {user.completedLessons}/{user.totalLessons}
                        </span>
                      </td>
                      <td className="p-4">
                        <Link 
                          href={`/admin/analytics/${courseId}/${user.id}`}
                          className="block hover:text-blue-600 transition-colors"
                        >
                          <div className="text-sm">
                            <span className="text-gray-900 hover:text-blue-600">
                              {user.submittedAssignments}/{user.totalAssignments}
                            </span>
                            {user.overdueCount > 0 && (
                              <p className="text-red-600 text-xs">
                                {user.overdueCount} overdue
                              </p>
                            )}
                          </div>
                        </Link>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <MessageSquare className="h-3 w-3" />
                          <span>{user.feedbackSessions.completed}/{user.feedbackSessions.total}</span>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">
                          {formatDate(user.enrolledAt)}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          user.isActive 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-gray-100 text-gray-700'
                        }`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 