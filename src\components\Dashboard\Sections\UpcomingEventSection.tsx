// app/components/sections/UpcomingEventsSection.tsx
import type { UpcomingDeadline } from '@/data-access/dashboard/data';
import EventCard from '../Cards/EventCard';
import { BellRing } from 'lucide-react';

interface UpcomingEventsSectionProps {
  deadlines: UpcomingDeadline[];
}

export default function UpcomingEventsSection({ deadlines }: UpcomingEventsSectionProps) {
  if (!deadlines || deadlines.length === 0) {
    return null;
  }
  
  return (
    <section className="mb-16">
      <div className="flex items-center mb-8">
        <BellRing size={32} className="mr-3 text-blue-600" />
        <h2 className="text-3xl font-bold text-slate-800">Upcoming Events & Deadlines</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {deadlines.map(deadline => (
          <EventCard key={deadline.id} deadline={deadline} />
        ))}
      </div>
    </section>
  );
}