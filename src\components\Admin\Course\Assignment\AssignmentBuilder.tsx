"use client"

import { useState, useTransition } from "react"
import { FileText, X, Save, AlertCircle, Calendar, Upload, File } from "lucide-react"
import { Assignment as AssignmentData } from "@/types/course"
import { createClient } from "@/utils/supabase/client"


interface AssignmentBuilderProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: AssignmentData) => Promise<void>
  initialData?: Partial<AssignmentData>
}

export default function AssignmentBuilder({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData 
}: AssignmentBuilderProps) {
  const [formData, setFormData] = useState<AssignmentData>({
    id: initialData?.id || "",
    moduleId: initialData?.moduleId || "",
    assignmentPath: initialData?.assignmentPath || null,
    title: initialData?.title || "",
    description: initialData?.description || null,
    deadline: initialData?.deadline || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    createdAt: initialData?.createdAt || new Date(),
    updatedAt: initialData?.updatedAt || new Date()
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [questionFile, setQuestionFile] = useState<File | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [isPending, startTransition] = useTransition()

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      newErrors.title = "Assignment title is required"
    }
    
    if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters"
    }

    if (formData.deadline <= new Date()) {
      newErrors.deadline = "Deadline must be in the future"
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters"
    }

    if (questionFile) {
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
      if (!allowedTypes.includes(questionFile.type)) {
        newErrors.questionFile = "Only PDF, DOC, DOCX, and TXT files are allowed"
      }
      if (questionFile.size > 10 * 1024 * 1024) { 
        newErrors.questionFile = "File size must be less than 10MB"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleFileUpload = async (file: File) => {

    setQuestionFile(file)

    startTransition(async () => {
      try {
        const BUCKET_NAME = process.env.NEXT_PUBLIC_SUPABASE_PRIVATE_BUCKET || 'dhwanini-private'
        const supabase = createClient()
        const { data, error } = await supabase.storage.from(BUCKET_NAME).upload(`assignments/${initialData?.id}`, file, {
          upsert: true
        })
        if(error) {
          setErrors(prev => ({ ...prev, questionFile: error.message }))
          return
        }
        if(data?.path) {
          setFormData({ ...formData, assignmentPath: data?.path })
        }
        setErrors(prev => ({ ...prev, questionFile: "" }))
      } catch (error) {
        console.error('Upload error:', error)
        setErrors(prev => ({ ...prev, questionFile: 'Network error. Please check your connection and try again.' }))
      }
    })
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const removeFile = () => {
    setQuestionFile(null)
    setErrors(prev => ({ ...prev, questionFile: "" }))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      if(questionFile) {
        await handleFileUpload(questionFile)
      }
      await onSave(formData)
      onClose()
    } 
    catch (error) {
      console.error("Failed to save assignment:", error)
    } 
    finally {
      setLoading(false)
    }
  }

  const formatDateTime = (date: Date) => {
    return date.toISOString().slice(0, 16)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-8 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {initialData ? "Edit Assignment" : "Create New Assignment"}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {initialData ? "Update assignment details and requirements" : "Create a new assignment for students to complete"}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/50 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <div className="overflow-y-auto max-h-[calc(95vh-120px)]">
          <form onSubmit={handleSubmit} className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Assignment Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg ${
                      errors.title ? "border-red-300 bg-red-50" : "border-gray-300 hover:border-gray-400"
                    }`}
                    placeholder="Enter assignment title"
                  />
                  {errors.title && (
                    <div className="flex items-center gap-2 mt-3 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{errors.title}</span>
                    </div>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Description
                  </label>
                  <textarea
                    value={formData.description || ""}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      description: e.target.value || null 
                    })}
                    rows={6}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none ${
                      errors.description ? "border-red-300 bg-red-50" : "border-gray-300 hover:border-gray-400"
                    }`}
                    placeholder="Provide detailed instructions and context for the assignment..."
                  />
                  {errors.description && (
                    <div className="flex items-center gap-2 mt-3 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{errors.description}</span>
                    </div>
                  )}
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">Optional but recommended</span>
                    <span className="text-sm text-gray-500">
                      {formData.description?.length || 0}/500
                    </span>
                  </div>
                </div>

                {/* Deadline */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Deadline *
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="datetime-local"
                      value={formatDateTime(formData.deadline)}
                      onChange={(e) => setFormData({ ...formData, deadline: new Date(e.target.value) })}
                      className={`w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                        errors.deadline ? "border-red-300 bg-red-50" : "border-gray-300 hover:border-gray-400"
                      }`}
                    />
                  </div>
                  {errors.deadline && (
                    <div className="flex items-center gap-2 mt-3 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{errors.deadline}</span>
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-2">
                    When students must submit their completed work
                  </p>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Assignment Questions File Upload */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Assignment Questions File
                  </label>
                  
                  {!questionFile ? (
                    <div
                      className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
                        dragActive 
                          ? "border-blue-400 bg-white scale-105" 
                          : errors.questionFile 
                          ? "border-red-300 bg-red-50" 
                          : "border-gray-300 hover:border-gray-400 hover:bg-white"
                      }`}
                      onDrop={handleDrop}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                    >
                      <div className="space-y-4">
                        <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                          <Upload className="w-8 h-8 text-gray-400" />
                        </div>
                        <div>
                          <p className="text-base text-gray-700 mb-2">
                            Drag and drop your assignment file here
                          </p>
                          <p className="text-sm text-gray-500 mb-4">or</p>
                          <label className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer font-medium transition-colors">
                            <Upload className="w-4 h-4 mr-2" />
                            Browse Files
                            <input
                              type="file"
                              className="hidden"
                              accept=".pdf,.doc,.docx,.txt"
                              onChange={(e) => {
                                const file = e.target.files?.[0]
                                if (file) handleFileUpload(file)
                              }}
                            />
                          </label>
                        </div>
                        <div className="text-xs text-gray-500 space-y-1">
                          <p>Supported formats: PDF, DOC, DOCX, TXT</p>
                          <p>Maximum file size: 10MB</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-xl p-6 bg-white">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="p-3 bg-green-100 rounded-lg">
                            <File className="w-6 h-6 text-green-600" />
                          </div>
                          <div>
                            <p className="text-base font-medium text-gray-900">{questionFile.name}</p>
                            <p className="text-sm text-gray-500">{formatFileSize(questionFile.size)}</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={removeFile}
                          className="p-2 hover:bg-red-100 rounded-lg transition-colors group"
                        >
                          <X className="w-5 h-5 text-gray-400 group-hover:text-red-500" />
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {errors.questionFile && (
                    <div className="flex items-center gap-2 mt-3 text-red-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{errors.questionFile}</span>
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-2">
                    Upload a file containing the assignment questions and detailed instructions
                  </p>
                </div>

                {/* Assignment Preview */}
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FileText className="w-5 h-5 text-gray-600" />
                    Assignment Preview
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <span className="text-sm text-gray-600 font-medium">Title:</span>
                      <span className="font-semibold text-gray-900 text-right max-w-xs">
                        {formData.title || "Untitled Assignment"}
                      </span>
                    </div>
                    <div className="flex justify-between items-start">
                      <span className="text-sm text-gray-600 font-medium">Due Date:</span>
                      <div className="text-right">
                        <span className="font-semibold text-gray-900 block">
                          {formData.deadline.toLocaleDateString()}
                        </span>
                        <span className="text-sm text-gray-500">
                          at {formData.deadline.toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                    {questionFile && (
                      <div className="flex justify-between items-start">
                        <span className="text-sm text-gray-600 font-medium">Questions File:</span>
                        <span className="font-semibold text-gray-900 text-right max-w-xs truncate">
                          {questionFile.name}
                        </span>
                      </div>
                    )}
                    {formData.description && (
                      <div className="pt-3 border-t border-gray-200">
                        <span className="text-sm text-gray-600 font-medium block mb-2">Description:</span>
                        <p className="text-sm text-gray-900 leading-relaxed">{formData.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4 pt-8 mt-8 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-white transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || isPending}
                className="flex-1 px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center justify-center gap-2 shadow-lg"
              >
                <Save className="w-5 h-5" />
                {loading ? "Saving Assignment..." : "Save Assignment"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
} 