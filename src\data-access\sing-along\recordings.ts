'use server'

import { RecentSession, Recording } from "@/types/sing-along"
import { prisma } from "@/utils/prisma/prisma"
import { getUserDetails } from "../auth";

export const getRecentSessions = async (): Promise<{sessions: RecentSession[], error: string | null}> => {
    try {
        const { user, error } = await getUserDetails();
        if (!user?.id || error) {
            return { sessions: [], error: error || 'User not found' };
        }
        const sessions = await prisma.userRecording.findMany({
            where: {
                userId: user.id
            },
            orderBy: {
                createdAt: 'desc'
            },
            select: {
                id: true,
                musicTitle: true,
                createdAt: true,
                duration: true,
                fileName: true,
            },
            take: 5
        })
        return { sessions, error: null };
    }
    catch (error) {
        console.error('Error fetching recent sessions:', error);
        return { sessions: [], error: 'Error fetching recent sessions' };
    }
}

export const getRecordings = async (): Promise<{recordings: Recording[], error: string | null}> => {
    try {
        const { user, error } = await getUserDetails();
        if (!user?.id || error) {
            return { recordings: [], error: error || 'User not found' };
        }
        const recordings = await prisma.userRecording.findMany({
            where: {
                userId: user.id
            },
            orderBy: {
                createdAt: 'desc'
            },
            select: {
                id: true,
                musicTitle: true,
                createdAt: true,
                duration: true,
                fileName: true,
                favourite: true,
            }
        })
        return { recordings, error: null };
    }
    catch (error) {
        console.error('Error fetching recordings:', error);
        return { recordings: [], error: 'Error fetching recordings' };
    }
}

export const toggleFavourite = async (recordingId: string, favourite: boolean): Promise<{success: boolean, error: string | null}> => {
    try {
        const recordings = await prisma.userRecording.update({
            where: {
                id: recordingId
            },
            data: {
                favourite: favourite
            }
        })
        if(!recordings) {
            return {success:false,error:'Failed to update favourite'}
        }
        return {success:true,error:null}
    }
    catch (error) {
        console.error('Error toggling favourite:', error);
        return { success: false, error: 'Error toggling favourite' };
    }
}

export const deleteRecording = async (recordingId: string): Promise<{success: boolean, error: string | null}> => {
    try {
        const recordings = await prisma.userRecording.delete({
            where: {
                id: recordingId
            }
        })
        if(!recordings) {
            return {success:false,error:'Failed to delete recording'}
        }
        return {success:true,error:null}
    }
    catch (error) {
        console.error('Error deleting recording:', error);
        return { success: false, error: 'Error deleting recording' };
    }
}