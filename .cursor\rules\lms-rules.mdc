---
description: 
globs: 
alwaysApply: true
---
# 🎼 Dhwanini Music Academy Platform – Cursor Rules

Enforces architectural, coding, and UI/UX standards across the platform. Use these rules during development, code review, onboarding, and documentation.
don't create client side supabase client, only use server side  clients
---

## 📐 Project Architecture

* **Frontend**: Next.js 15+ (App Router), TypeScript, Tailwind CSS
* **Backend**: Next.js API routes, Supabase Auth, Prisma ORM
* **Database**: PostgreSQL
* **Media**: Video player with progress tracking, audio recording/playback
* **Scheduling**: Cal.com integration for feedback sessions

---

## 🧠 Development Standards

### 🔧 Code Organization

* Use **data-access layer** for all DB operations (via Prisma wrappers)
* Keep **business logic** strictly in server-side functions
* Implement proper **TypeScript interfaces** for all core entities
* Follow **Next.js App Router** conventions:

  * Use `app/` directory
  * Proper server/client component usage

---

### 🚀 Key Features

#### 🎥 Video Progress Tracking

* Track **watch percentage** per user per lesson
* Enforce **70% completion** before unlocking next content
* **Disable fast-forwarding** on first watch

#### 📂 Assignment & Feedback System

* Support **file uploads** (audio, video, docs)
* Enforce **Cal.com session booking** for feedback
* Auto-track **overdue submissions** with reminder emails

#### 🎤 Audition Process

* Include a **validated quiz**
* Show **upload progress** for media
* Track **application status** through pipeline

#### ⏰ Overdue Management

* Track **missed deadlines** (videos, assignments, feedback)
* Send **email reminders every 2 days**
* **Auto-suspend users** after 3 missed items

---

### 🧾 TypeScript Requirements

* Define strict interfaces for:

  * `Course`, `Module`, `Lesson`, `Assignment`, `Application`, `User`
* Use **union types** for all status fields
* Implement **Zod schemas** for API validation
* Explicitly type **all component props**

---

### 🔒 Security & Authentication

* Implement **role-based access control (RBAC)**
* Protect all **API routes** with auth middleware
* Use **server-side rendering (SSR)** for protected pages
* Perform **server-side input validation**

---

### 🖼️ UI/UX Standards

* Show **skeleton loaders** for loading states
* Add **progress indicators** for async ops
* Ensure **fully responsive** layouts across devices
* Display **graceful error messages** for all error states

---

### 🗄️ Database Design

* Use **Prisma** with correct relations and referential integrity
* Implement **soft deletes** where appropriate
* Add **indexes** for performance-critical queries
* Use **transactions** for critical operations (e.g. assignment submission + status update)

---

### ⚡ Performance

* Implement **caching** strategies as needed
* Optimize **video streaming and loading**
* Use **Next.js `<Image />`** for all images
* Use **pagination** for all large datasets (assignments, submissions, feedback, etc.)

---

### 🧱 File Structure Principles

* Organize by **feature, not file type**
* Keep **related code** (components, utils, hooks) together
* Separate **public** and **academy portal** logic
* Use **barrel exports** to clean up import paths

---

### 🛠️ Tooling Rules

* Use **TanStack Query** for all data fetching and mutations
* Do **not** use any third-party UI libraries
