import { prisma } from "@/utils/prisma/prisma";
import { Mux } from "@mux/mux-node";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const mux = new Mux({
    tokenId: process.env.MUX_TOKEN_ID,
    tokenSecret: process.env.MUX_TOKEN_SECRET,
})

export async function POST(request: NextRequest) {
    try {

        const rawBody = await request.text()
        const headersList = await headers()
        const signature = headersList.get("mux-signature")

        if (!signature) {
            console.error("Missing mux-signature header")
            return new Response("Missing signature", { status: 400 })
        }

        if (!process.env.MUX_SIGNING_KEY) {
            console.error("Missing MUX_SIGNING_KEY environment variable")
            return new Response("Server configuration error", { status: 500 })
        }

        const headersObject: Record<string, string> = {}
        headersList.forEach((value, key) => {
            headersObject[key] = value
        })

        mux.webhooks.verifySignature(
            rawBody,
            headersObject,
            process.env.MUX_SIGNING_KEY
        )

        const body = JSON.parse(rawBody)
        

        if (body.type === "video.asset.ready") {
            const video = await prisma.video.update({
                where: {
                    upload_id: body.data.upload_id
                },
                data: {
                    playback_id: body.data.playback_ids[0].id,
                    asset_id: body.data.id,
                    status: "ready"
                }
            })
            if(!video) {
                console.error("Failed to update video")
                return new NextResponse("Failed to update video", { status: 500 })
            }
            await prisma.lesson.update({
                where: {
                    id: video.lessonId
                },
                data: {
                    durationMin: Math.ceil(body.data.duration / 60)
                }
            })
        }

        if (body.type === "video.asset.deleted") {
            const video = await prisma.video.delete({
                where: {
                    upload_id: body.data.upload_id
                }
            })
            if(!video) {
                console.error("Failed to delete video")
                return new NextResponse("Failed to delete video", { status: 500 })
            }
        }



        return new NextResponse("OK", { status: 200 })
    } catch (error) {
        console.error("Webhook processing error:", error)
        return new NextResponse("Webhook processing failed", { status: 500 })
    }
}