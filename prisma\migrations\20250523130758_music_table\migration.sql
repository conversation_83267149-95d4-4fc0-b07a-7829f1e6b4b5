/*
  Warnings:

  - You are about to drop the column `musicId` on the `UserRecording` table. All the data in the column will be lost.
  - Added the required column `musicTitle` to the `UserRecording` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "UserRecording" DROP CONSTRAINT "UserRecording_musicId_fkey";

-- DropIndex
DROP INDEX "UserRecording_musicId_idx";

-- AlterTable
ALTER TABLE "UserRecording" DROP COLUMN "musicId",
ADD COLUMN     "musicTitle" TEXT NOT NULL;

-- CreateIndex
CREATE INDEX "UserRecording_musicTitle_idx" ON "UserRecording"("musicTitle");

-- AddForeignKey
ALTER TABLE "UserRecording" ADD CONSTRAINT "UserRecording_musicTitle_fkey" FOREIGN KEY ("musicTitle") REFERENCES "Music"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
