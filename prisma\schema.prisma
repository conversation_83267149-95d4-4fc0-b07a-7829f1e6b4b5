generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id               String                 @id @default(cuid())
  email            String                 @unique
  name             String
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  country          String?
  overDues         Int                    @default(0)
  isActive         Boolean                @default(true)
  phone            String?
  role             Role                   @default(STUDENT)
  interests        String[]
  preferredTimings String?
  vocalExperience  CourseLevel            @default(BEGINNER)
  whatsappNumber   String?
  applications     Application[]
  assignments      AssignmentSubmission[]
  certificates     Certificate[]
  enrollments      Enrollment[]
  feedbackSessions FeedbackSession[]
  payments         Payment[]
  recordings       UserRecording[]
  completedLessons Lesson[]               @relation("CompletedLessons")
  completedModules Module[]               @relation("CompletedModules")

  @@index([email])
  @@index([role, isActive])
}

model Course {
  id               String            @id @default(cuid())
  name             String            @unique
  description      String
  level            CourseLevel       @default(BEGINNER)
  price            Int?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  instructorEmail  String?
  currency         String            @default("INR")
  isPublished      Boolean           @default(false)
  auditionFee      Int?
  featured         Boolean           @default(false)
  thumbnailPath    String?
  applications     Application[]
  certificates     Certificate[]
  enrollments      Enrollment[]
  feedbackSessions FeedbackSession[]
  modules          Module[]

  @@index([isPublished, level])
}

model Module {
  id               String       @id @default(cuid())
  courseId         String
  title            String
  order            Int
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  description      String?
  assignments      Assignment[]
  lessons          Lesson[]
  course           Course       @relation(fields: [courseId], references: [id], onDelete: Cascade)
  completedByUsers User[]       @relation("CompletedModules")

  @@unique([courseId, order])
  @@index([courseId])
}

model Lesson {
  id          String @id @default(cuid())
  moduleId    String

  title       String
  description String
  durationMin Int?
  order       Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  module           Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  completedByUsers User[] @relation("CompletedLessons")
  video            Video? 

  @@unique([moduleId, order])
  @@index([moduleId])
}

model Video {
  id    String            @id @default(cuid())
  lessonId  String        @unique

  upload_id     String    @unique
  playback_id   String? 
  asset_id      String?

  title         String?
  status        String    @default("preparing")

  lesson        Lesson    @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([lessonId])
  @@index([title])

}

model Application {
  id                      String         @id @default(cuid())
  userId                  String
  courseId                String
  auditionStatus          AuditionStatus @default(PENDING)
  createdAt               DateTime       @default(now())
  updatedAt               DateTime       @updatedAt
  audioPath               String?
  feedback                String?
  userDetails             Json?
  auditionPaymentStatus   PaymentStatus  @default(PENDING)
  enrollmentPaymentStatus PaymentStatus  @default(PENDING)
  course                  Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user                    User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@index([auditionStatus])
  @@index([userId, courseId])
}

model Enrollment {
  id          String    @id @default(cuid())
  userId      String
  courseId    String
  enrolledAt  DateTime  @default(now())
  completedAt DateTime?

  course      Course    @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@index([userId])
  @@index([courseId])
}

model Assignment {
  id             String                 @id @default(cuid())
  moduleId       String
  title          String
  deadline       DateTime
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  description    String?
  assignmentPath String?
  module         Module                 @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  submissions    AssignmentSubmission[]
  feedbacks      FeedbackSession[]

  @@index([moduleId, deadline])
}

model AssignmentSubmission {
  id             String           @id @default(cuid())
  assignmentId   String
  userId         String
  feedback       String?
  status         SubmissionStatus @default(SUBMITTED)
  submittedAt    DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  assignmentPath String?
  assignment     Assignment       @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([assignmentId, userId])
  @@index([userId, status])
}

model FeedbackSession {
  id           String        @id @default(cuid())
  userId       String
  courseId     String?
  status       SessionStatus @default(SCHEDULED)
  scheduledAt  DateTime?
  notes        String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  assignmentId String?
  calEventId   String?       @unique
  assignment   Assignment?   @relation(fields: [assignmentId], references: [id])
  course       Course?        @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, scheduledAt])
  @@index([courseId, status])
  @@index([assignmentId])
}

model Music {
  id         String          @id @default(cuid())
  title      String          @unique
  genre      String
  difficulty CourseLevel
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt
  duration   String
  recordings UserRecording[]

  @@index([genre, difficulty])
}

model UserRecording {
  id         String   @id @default(cuid())
  userId     String

  fileName   String   @unique
  mimeType   String

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  musicTitle String?
  duration   String
  favourite  Boolean  @default(false)
  music      Music?    @relation(fields: [musicTitle], references: [title], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Payment {
  id                String        @id @default(cuid())
  userId            String
  courseId          String?
  amount            Decimal       @db.Decimal(10, 2)
  currency          String        @default("INR")
  status            PaymentStatus @default(PENDING)
  razorpayOrderId   String        @unique
  razorpayPaymentId String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  paymentType       PaymentType   @default(AUDITION)
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
  @@index([razorpayOrderId])
  @@index([createdAt])
}

model Certificate {
  id       String   @id @default(cuid())
  userId   String
  courseId String
  issuedAt DateTime @default(now())
  pdfPath  String
  course   Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@index([userId])
}

model FeedbackSettings {
  id      String @id @default("feedback_settings")
  calLink String @default("dhwani/feedback-session")
}

enum Role {
  STUDENT
  COURSE_MANAGER
  ADMISSION_SUPPORT
  SUPER_ADMIN
}

enum CourseLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum AuditionStatus {
  PENDING
  APPROVED
  REJECTED
  SUBMITTED
  UNDER_REVIEW
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  INITIATED
}

enum SubmissionStatus {
  SUBMITTED
  GRADED
  NEEDS_REVISION
  OVERDUE
}

enum SessionStatus {
  SCHEDULED
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum PaymentType {
  AUDITION
  ENROLLMENT
}
