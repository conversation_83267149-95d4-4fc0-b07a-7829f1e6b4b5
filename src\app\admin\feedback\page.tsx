import { Suspense } from 'react'
import { MessageSquare } from 'lucide-react'
import { getAllFeedbackSessions, getFeedbackStats } from '@/data-access/feedback/admin'
import AdminFeedbackDashboard from '@/components/Admin/Feedback/AdminFeedbackDashboard'
import AdminFeedbackStats from '@/components/Admin/Feedback/AdminFeedbackStats'
import AdminFeedbackSettings from '@/components/Admin/Feedback/AdminFeedbackSettings'
import FeedbackStatsLoading from '@/components/Admin/Feedback/FeedbackStatsLoading'

export default async function AdminFeedbackPage() {
  const [sessionsResult, statsResult] = await Promise.all([
    getAllFeedbackSessions(),
    getFeedbackStats()
  ])

  if (!sessionsResult.success) {
    throw new Error(sessionsResult.error || 'Failed to load feedback sessions')
  }

  if (!statsResult.success) {
    throw new Error(statsResult.error || 'Failed to load feedback stats')
  }

  const sessions = sessionsResult.data || []
  const stats = statsResult.data!

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex items-center justify-center w-10 h-10 bg-indigo-100 rounded-lg">
              <MessageSquare className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Feedback Management</h1>
              <p className="text-gray-600 text-sm">
                Manage feedback sessions and settings
              </p>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-8">
          <Suspense fallback={<FeedbackStatsLoading />}>
            <AdminFeedbackStats stats={stats} />
          </Suspense>
        </div>

        {/* Settings */}
        <div className="mb-8">
          <AdminFeedbackSettings />
        </div>

        {/* Sessions Table */}
        <div>
          <AdminFeedbackDashboard initialSessions={sessions} />
        </div>
      </div>
    </div>
  )
} 